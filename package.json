{"name": "agentous-web-app", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc --noEmit && vite build", "serve": "serve -s dist", "preview": "vite preview", "test": "vitest run", "test:watch": "vitest", "lint": "eslint --cache --cache-location ./node_modules/.cache/eslint .", "lint:fix": "eslint . --fix", "type:check": "tsc --noEmit", "format": "prettier --write .", "format:check": "prettier --check .", "prepare": "husky", "pre-commit": "lint-staged"}, "lint-staged": {"*.{ts,tsx,js,jsx}": ["eslint --fix --max-warnings 300", "prettier --write"], "*.{json,md,html,css,scss,yaml,yml}": ["prettier --write"], "!deployment.yml": [], "!bitbucket-pipelines.yml": [], "!Dockerfile": [], "!docker-compose*.yml": []}, "dependencies": {"@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@eslint/js": "^9.33.0", "@hookform/resolvers": "^3.3.2", "@radix-ui/react-avatar": "^1.1.2", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-popover": "^1.1.5", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-tooltip": "^1.1.7", "@react-keycloak/web": "^3.4.0", "@tailwindcss/forms": "^0.5.7", "@tanstack/react-query": "^4.32.1", "@tanstack/react-query-devtools": "^4.40.1", "@tanstack/react-virtual": "^3.8.3", "@testing-library/jest-dom": "^6.4.2", "@testing-library/react": "^14.2.1", "@testing-library/user-event": "^14.5.2", "@types/lodash": "^4.14.201", "@types/node": "^20.9.0", "@types/react": "^18.2.15", "@types/react-copy-to-clipboard": "^5.0.7", "@types/react-dom": "^18.2.7", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "@vitejs/plugin-react": "^4.0.3", "@vitejs/plugin-react-swc": "^4.0.0", "@vitest/coverage-v8": "^1.5.0", "@vitest/ui": "^1.5.0", "ai": "^5.0.2", "autoprefixer": "^10.4.16", "axios": "^1.4.0", "class-variance-authority": "^0.7.0", "classnames": "^2.5.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "dotenv": "^16.4.5", "embla-carousel-react": "^8.5.2", "eslint": "^8.45.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-react": "^7.34.1", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "eslint-plugin-simple-import-sort": "^12.1.0", "eslint-plugin-unicorn": "^52.0.0", "eslint-plugin-unused-imports": "^3.1.0", "eventsource-parser": "^3.0.3", "framer-motion": "^10.16.4", "globals": "^15.2.0", "happy-dom": "^14.7.1", "html-react-parser": "^5.1.1", "husky": "^9.0.11", "jest-environment-jsdom": "^29.7.0", "jsdom": "^24.0.0", "keycloak-js": "^21.1.1", "libphonenumber-js": "^1.10.41", "lodash": "^4.17.21", "lucide-react": "^0.376.0", "moment": "^2.29.4", "pdf-lib": "^1.17.1", "postcss": "^8.4.31", "prettier": "^3.2.5", "prettier-plugin-tailwindcss": "^0.5.14", "qs": "^6.12.3", "quill": "^1.3.6", "ramda": "^0.30.1", "react": "^18.2.0", "react-copy-to-clipboard": "^5.1.0", "react-datepicker": "^7.5.0", "react-day-picker": "^9.9.0", "react-dom": "^18.2.0", "react-dropzone": "^14.2.3", "react-helmet-async": "^1.3.0", "react-hook-form": "^7.48.2", "react-intersection-observer": "^9.8.0", "react-markdown": "^10.1.0", "react-phone-input-2": "^2.15.1", "react-router-dom": "^6.18.0", "react-select": "^5.7.4", "react-toastify": "^9.1.3", "socket.io-client": "^4.7.3", "tailwind-merge": "^2.0.0", "tailwind-scrollbar": "^3.0.5", "tailwindcss": "^3.3.5", "tiny-invariant": "^1.3.3", "typescript": "^5.0.2", "typescript-eslint": "^7.8.0", "vite": "^4.4.5", "vite-plugin-svgr": "^3.2.0", "vitest": "^1.5.0", "world-countries": "^5.1.0", "yup": "^1.3.2"}, "devDependencies": {"lint-staged": "^16.1.6"}}