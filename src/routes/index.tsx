import React from 'react';
import { createBrowserRouter, Outlet, RouterProvider } from 'react-router-dom';
import { HelmetProvider } from 'react-helmet-async';
import { useKeycloak } from '@react-keycloak/web';

// Layout Shells
import NavigationShell from '../components/layout/NavigationShell';
import DashboardShell from '../components/layout/DashboardShell';

// Route definitions
import { ShellRoutes } from './ShellRoutes';
import { DashboardRoutes } from './DashboardRoutes';
import { ProtectedRoute } from '../components/auth/ProtectedRoute';
import { isDevEnvironment } from '@/utils/helpers';
import { MainLoaderSkeleton } from '@/components/hocs/suspense/withSuspense';
import NotFoundPage from '../pages/NotFoundPage';

const RootLayout = () => (
  <HelmetProvider>
    <Outlet />
  </HelmetProvider>
);

const router = createBrowserRouter([
  {
    element: <RootLayout />,
    children: [
      {
        path: '/',
        element: <NavigationShell />,
        children: ShellRoutes,
      },
      {
        path: '/dashboard',
        element: (
          <ProtectedRoute>
            <DashboardShell />
          </ProtectedRoute>
        ),
        children: DashboardRoutes,
      },
      {
        path: '*',
        element: <NotFoundPage />,
      },
    ],
  },
]);

export const MainAppRoutes = () => {
  const { initialized } = useKeycloak();
  if (!initialized && !isDevEnvironment()) return <MainLoaderSkeleton />;
  return <RouterProvider router={router} />;
};

export default MainAppRoutes;