import { useKeycloak } from '@react-keycloak/web';
import React from 'react';
import { HelmetProvider } from 'react-helmet-async';
import { createBrowserRouter, Outlet, RouterProvider } from 'react-router-dom';

import { MainLoaderSkeleton } from '@/components/hocs/suspense/withSuspense';
import { isDevEnvironment } from '@/utils/helpers';

import { ProtectedRoute } from '../components/auth/ProtectedRoute';
import DashboardShell from '../components/layout/DashboardShell';
// Layout Shells
import NavigationShell from '../components/layout/NavigationShell';
import NotFoundPage from '../pages/NotFoundPage';
import { DashboardRoutes } from './DashboardRoutes';
// Route definitions
import { ShellRoutes } from './ShellRoutes';

// Standalone auth pages
const ResetPasswordPage = React.lazy(() =>
  import('../pages/ResetPasswordPage/ResetPassword').then(module => ({
    default: module.ResetPassword,
  }))
);

const NewPasswordPage = React.lazy(() => import('../pages/NewPasswordPage'));

const RootLayout = () => (
  <HelmetProvider>
    <Outlet />
  </HelmetProvider>
);

const router = createBrowserRouter([
  {
    element: <RootLayout />,
    children: [
      {
        path: '/',
        element: <NavigationShell />,
        children: ShellRoutes,
      },
      {
        path: '/dashboard',
        element: (
          <ProtectedRoute>
            <DashboardShell />
          </ProtectedRoute>
        ),
        children: DashboardRoutes,
      },
      {
        path: '/reset-password',
        element: (
          <React.Suspense fallback={<MainLoaderSkeleton />}>
            <ResetPasswordPage />
          </React.Suspense>
        ),
      },
      {
        path: '/reset-password/:token',
        element: (
          <React.Suspense fallback={<MainLoaderSkeleton />}>
            <NewPasswordPage />
          </React.Suspense>
        ),
      },
      {
        path: '*',
        element: <NotFoundPage />,
      },
    ],
  },
]);

export const MainAppRoutes = () => {
  const { initialized } = useKeycloak();
  if (!initialized && !isDevEnvironment()) return <MainLoaderSkeleton />;
  return <RouterProvider router={router} />;
};

export default MainAppRoutes;
