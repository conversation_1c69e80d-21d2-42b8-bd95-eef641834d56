import { lazy } from "react";
import { Navigate } from "react-router-dom";
import { withSuspense } from "@/components/hocs/suspense/withSuspense";

// Lazy load public pages
const HomePage = withSuspense(lazy(() => import("../../pages/HomePage")));
const OurStoryPage = withSuspense(
  lazy(() => import("../../pages/OurStoryPage"))
);

const LoginPage = withSuspense(lazy(() => import("../../pages/LoginPage")));

const SignupPage = withSuspense(lazy(() => import("../../pages/SignupPage")));

const PricingPage = withSuspense(lazy(() => import("../../pages/PricingPage")));

const AgentsPage = withSuspense(
  lazy(() => import("../../pages/AiAgentsPage/AgentDetailsPage/Marketplace"))
);

const AgentDetailsPage = withSuspense(
  lazy(() => import("../../pages/AiAgentsPage/AgentDetailsPage/AgentDetails"))
);

export const ShellRoutes = [
  {
    index: true,
    element: <HomePage />,
  },
  {
    path: "our-story",
    element: <OurStoryPage />,
  },
  {
    path: "agents",
    element: <AgentsPage />,
  },
  {
    path: "agents/:agentId",
    element: <AgentDetailsPage />,
  },
  {
    path: "login",
    element: <LoginPage />,
  },
  {
    path: "signup",
    element: <SignupPage />,
  },
  {
    path: "pricing",
    element: <PricingPage />,
  },
  {
    path: "*",
    element: <Navigate to="/" replace />,
  },
];
