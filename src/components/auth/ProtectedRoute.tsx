import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from '../../context/AuthContext';
import { ROUTES } from '../../constants/routes';
import { MainLoaderSkeleton } from '@/components/hocs/suspense/withSuspense';

interface ProtectedRouteProps {
  children: React.ReactNode;
  redirectTo?: string;
}

const isDevEnvironment = () => {
  const url = typeof window !== 'undefined' ? window.location.href : '';
  return url.includes('localhost');
};

export const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  redirectTo = ROUTES.LOGIN,
}) => {
  const { isAuthenticated, isLoading, isError, isOnline, user } = useAuth();
  const location = useLocation();
  const isDev = isDevEnvironment();

  // Skip loading screen in development
  if (isLoading && !isDev) {
    return <MainLoaderSkeleton />;
  }

  // Skip error handling in development
  if (isError && !isDev) {
    const isOffline = !isOnline;
    return (
      <div
        className="flex fixed inset-0 justify-center items-center mx-auto max-w-full h-screen bg-white bg-cover z-100"
      >
        <div className="flex h-[400px] w-full items-center justify-center">
          <div className="flex flex-col gap-4 justify-center items-center">
            <div
              className={`rounded-full p-3 ${isOffline ? 'bg-orange-100' : 'bg-red-100'}`}
            >
              {isOffline ? (
                <svg
                  className="w-8 h-8 text-orange-600"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M18.364 5.636L5.636 18.364M9 12h6m-6 0a9 9 0 109 9 9 0 00-9-9z"
                  />
                </svg>
              ) : (
                <svg
                  className="w-8 h-8 text-red-600"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
              )}
            </div>
            <div className="text-center">
              <h4 className="mb-3 text-[24px] font-[600] text-primary">
                {isOffline
                  ? 'No Internet Connection'
                  : 'Unable to Load User Data'}
              </h4>
              <p className="mx-auto max-w-[525px] text-[14px] text-[#2F2F2F]">
                {isOffline
                  ? 'Please check your internet connection and try again. The dashboard requires an active connection.'
                  : "We couldn't load your user information. This might be a temporary issue. Please retry or check your connection."}
              </p>
              <div className="flex gap-4 justify-center mt-8">
                <button
                  onClick={() => window.location.reload()}
                  type="button"
                  className="px-6 py-2 text-white rounded-lg bg-primary hover:bg-primary/80"
                >
                  {isOffline ? 'Check Connection' : 'Retry'}
                </button>
                <button
                  onClick={() => (window.location.href = ROUTES.HOME)}
                  type="button"
                  className="px-6 py-2 text-gray-700 rounded-lg border border-gray-300 hover:bg-gray-50"
                >
                  Go to Homepage
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // In development, only check if we have user data (bypasses Keycloak)
  // In production, require full authentication
  const shouldAllowAccess = isDev ? !!user : isAuthenticated;

  // Skip authentication check in development
  if (!shouldAllowAccess && !isDev) {
    return <Navigate to={redirectTo} state={{ from: location }} replace />;
  }

  return <>{children}</>;
};
