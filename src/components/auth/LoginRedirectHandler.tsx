import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../context/AuthContext';
import { ROUTES } from '../../constants/routes';
import Logo from '../ui/Logo';

interface LoginRedirectHandlerProps {
  defaultRedirectPath?: string;
}

export const LoginRedirectHandler: React.FC<LoginRedirectHandlerProps> = ({
  defaultRedirectPath = ROUTES.DASHBOARD_BASE,
}) => {
  const navigate = useNavigate();
  const { isLoading, isError, isAuthenticated } = useAuth();

  useEffect(() => {
    if (!isAuthenticated) {
      // If not authenticated, redirect to login
      navigate(ROUTES.LOGIN, { replace: true });
      return;
    }

    if (isLoading) {
      // Still loading, wait
      return;
    }

    if (isError) {
      // Error occurred, redirect to login
      navigate(ROUTES.LOGIN, { replace: true });
      return;
    }

    // Authenticated and loaded, redirect to dashboard
    navigate(defaultRedirectPath, { replace: true });
  }, [
    isAuthenticated,
    isLoading,
    isError,
    navigate,
    defaultRedirectPath,
  ]);

  // Show loading state while determining redirect
  return (
    <div className="flex min-h-screen bg-gradient-to-br from-orange-50 to-orange-100 font-inter">
      {/* Logo at top left */}
      <div className="absolute top-6 left-6">
        <Logo />
      </div>

      {/* Loading content */}
      <div className="flex flex-1 justify-center items-center">
        <div className="text-center">
          <div className="flex justify-center mb-4">
            <div className="w-8 h-8 rounded-full border-4 animate-spin border-primary border-t-transparent"></div>
          </div>
          <p className="text-lg text-gray-600">Setting up your workspace...</p>
        </div>
      </div>
    </div>
  );
};

export default LoginRedirectHandler;
