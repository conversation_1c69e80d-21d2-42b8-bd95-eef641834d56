import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';

import { ROUTES } from '../../constants/routes';
import { useAuth } from '../../context/AuthContext';
import Logo from '../ui/Logo';

interface LoginRedirectHandlerProps {
  defaultRedirectPath?: string;
}

export const LoginRedirectHandler: React.FC<LoginRedirectHandlerProps> = ({
  defaultRedirectPath = ROUTES.DASHBOARD_AI_AGENTS,
}) => {
  const navigate = useNavigate();
  const { isLoading, isError, isAuthenticated } = useAuth();

  useEffect(() => {
    if (!isAuthenticated) {
      // If not authenticated, redirect to login
      navigate(ROUTES.LOGIN, { replace: true });
      return;
    }

    if (isLoading) {
      // Still loading, wait
      return;
    }

    if (isError) {
      // Error occurred, redirect to login
      navigate(ROUTES.LOGIN, { replace: true });
      return;
    }

    // Authenticated and loaded, redirect to dashboard
    navigate(defaultRedirectPath, { replace: true });
  }, [isAuthenticated, isLoading, isError, navigate, defaultRedirectPath]);

  // Show loading state while determining redirect
  return (
    <div className="flex min-h-screen bg-gradient-to-br from-orange-50 to-orange-100 font-inter">
      {/* Logo at top left */}
      <div className="absolute left-6 top-6">
        <Logo />
      </div>

      {/* Loading content */}
      <div className="flex flex-1 items-center justify-center">
        <div className="text-center">
          <div className="mb-4 flex justify-center">
            <div className="h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
          </div>
          <p className="text-lg text-gray-600">Setting up your workspace...</p>
        </div>
      </div>
    </div>
  );
};

export default LoginRedirectHandler;
