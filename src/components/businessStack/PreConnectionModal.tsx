import React, { useState } from 'react';

import { useTenant } from '@/context/TenantContext';

import { AvailableApp } from '../../types/businessStack';
import AnimatedModal from '../common/AnimatedModal';

interface PreConnectionModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  app: AvailableApp | null;
}

const PreConnectionModal: React.FC<PreConnectionModalProps> = ({
  isOpen,
  onClose,
  onConfirm,
  app,
}) => {
  const { activeAgent } = useTenant();
  const [isAcknowledged, setIsAcknowledged] = useState(false);

  const capitalizeAgentName = (key: string) => {
    if (!key) return 'Agent';
    return key.charAt(0).toUpperCase() + key.slice(1);
  };

  const getAgentEmail = (agentKey: string) => {
    if (!agentKey) return '<EMAIL>';
    return `${agentKey.toLowerCase()}.<EMAIL>`;
  };

  if (!app) return null;

  const agentName = capitalizeAgentName(activeAgent || '');
  const agentEmail = getAgentEmail(activeAgent || '');

  return (
    <AnimatedModal
      isOpen={isOpen}
      onClose={onClose}
      title={`Connect ${agentName} to ${app.name}`}
      maxWidth="md"
    >
      <div className="space-y-6">
        {/* App Info */}
        <div className="flex items-center justify-center gap-4">
          <div className="h-20 w-20 flex-shrink-0 rounded-lg bg-gray-100 p-2">
            <img
              src={app.logo}
              alt={`${app.name} logo`}
              className="h-full w-full rounded-lg object-contain"
              onError={e => {
                const target = e.target as HTMLImageElement;
                target.src =
                  'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQwIiBoZWlnaHQ9IjQwIiByeD0iOCIgZmlsbD0iI0Y5RkFGQiIvPgo8cGF0aCBkPSJNMjAgMTBWMzBNMTAgMjBIMzAiIHN0cm9rZT0iIzlDQTNBRiIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiLz4KPC9zdmc+';
              }}
            />
          </div>
        </div>

        {/* Connection Message */}
        <div className="rounded-lg bg-orange-50 p-4">
          <div className="flex items-start gap-3">
            <div className="flex-1">
              <p className="text-center text-gray-800">
                Connect {agentName} using the email you assigned to {agentName}{' '}
                at your company, e.g.{' '}
                <code className="rounded bg-gray-200 px-1 py-0.5 font-mono font-semibold">
                  {agentEmail}
                </code>
                <br />
                <p className='mt-2'>You are connecting the agent (not yourself) to this application.</p>
              </p>
            </div>
          </div>
        </div>

        {/* Acknowledgment Checkbox */}
        <div className="flex items-start gap-3 rounded-lg border border-blue-200 bg-blue-50 p-4">
          <input
            type="checkbox"
            id="acknowledgment-checkbox"
            checked={isAcknowledged}
            onChange={e => setIsAcknowledged(e.target.checked)}
            className="mt-0.5 h-4 w-4 rounded border-gray-300 text-primary focus:ring-2 focus:ring-primary"
          />
          <label
            htmlFor="acknowledgment-checkbox"
            className="cursor-pointer text-sm text-gray-800"
          >
            I acknowledge I am using the agent's designated email address for
            authentication
          </label>
        </div>

        {/* Action Button */}
        <div className="flex justify-center gap-3 pt-4">
          <button
            onClick={onConfirm}
            disabled={!isAcknowledged}
            className={`rounded-lg px-6 py-3 text-sm font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-primary/20 ${
              isAcknowledged
                ? 'bg-primary text-white hover:bg-orange-15'
                : 'cursor-not-allowed bg-gray-200 text-gray-400'
            }`}
          >
            Connect the agent (not you)
          </button>
        </div>
      </div>
    </AnimatedModal>
  );
};

export default PreConnectionModal;
