import React, { useState, useEffect } from 'react';
import { Search, ChevronDown } from 'lucide-react';
import { AppCard } from './AppCard';
import { AvailableAppsGridProps } from '../../types/businessStack';
import { Spinner } from '@/components/common/Loader';
import { useDebounce } from '@/hooks/useDebounce';
import { Button } from '@/components/ui';
import { useAppCategoriesApi } from '../../services/businessStackService';

export const AvailableAppsGrid: React.FC<AvailableAppsGridProps> = ({
  apps,
  isLoading,
  searchQuery,
  selectedCategory,
  onSearchChange,
  onCategoryChange,
  onConnectApp,
  connectionFlow,
}) => {
  const [localSearchQuery, setLocalSearchQuery] = useState(searchQuery);
  const [categories, setCategories] = useState<
    Array<{ categoryName: string; categoryAlias: string }>
  >([]);
  const [isLoadingCategories, setIsLoadingCategories] = useState(false);
  const [showCategoryDropdown, setShowCategoryDropdown] = useState(false);

  const getAppCategories = useAppCategoriesApi();

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element;
      if (showCategoryDropdown && !target.closest('.category-dropdown')) {
        setShowCategoryDropdown(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [showCategoryDropdown]);

  const debouncedSearchQuery = useDebounce(localSearchQuery, 1000);

  // Load categories on component mount
  useEffect(() => {
    const loadCategories = async () => {
      try {
        setIsLoadingCategories(true);
        const response = await getAppCategories();
        if (response.status && response.data) {
          setCategories(response.data);
        }
      } catch (error) {
        console.error('Error loading app categories:', error);
      } finally {
        setIsLoadingCategories(false);
      }
    };

    loadCategories();
  }, []);

  // Sync local state when parent searchQuery changes (e.g., when parent resets search)
  useEffect(() => {
    setLocalSearchQuery(searchQuery);
  }, [searchQuery]);

  // Trigger API call when debounced value changes
  useEffect(() => {
    // Only call onSearchChange if the debounced value is different from current searchQuery
    if (debouncedSearchQuery !== searchQuery) {
      onSearchChange(debouncedSearchQuery);
    }
  }, [debouncedSearchQuery, onSearchChange, searchQuery]);

  // Handle input change - update local state immediately for responsive UI
  const handleSearchInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setLocalSearchQuery(e.target.value);
  };

  // Handle category selection
  const handleCategorySelect = (categoryName: string) => {
    onCategoryChange(categoryName);
    setShowCategoryDropdown(false);
  };

  // Get selected category display name
  const getSelectedCategoryDisplay = () => {
    if (!selectedCategory) return 'All Categories';
    const category = categories.find(
      cat => cat.categoryName === selectedCategory,
    );
    return category ? category.categoryAlias : 'All Categories';
  };

  // Sort apps alphabetically and filter based on search query
  const filteredApps = apps
    .sort((a, b) => a.name.localeCompare(b.name))
    .filter(app => app.name.toLowerCase().includes(searchQuery.toLowerCase()));

  return (
    <div className="flex flex-col h-full">
      <div className="flex gap-4 justify-between mb-6">
        {/* Search Bar */}
        <div className="relative w-full">
          <Search className="absolute left-3 top-1/2 w-5 h-5 text-gray-400 transform -translate-y-1/2" />
          <input
            type="text"
            placeholder="Search"
            value={localSearchQuery}
            onChange={handleSearchInputChange}
            className="py-3 pr-4 pl-10 w-full text-sm rounded-lg border border-gray-300 placeholder:text-gray-500 focus:border-primary focus:outline-none focus:ring-2 focus:ring-primary/20"
          />
        </div>

        {/* Category Filter */}
        <div className="relative category-dropdown">
          <Button
            onClick={() => setShowCategoryDropdown(!showCategoryDropdown)}
            className="flex h-[46px] min-w-[140px] items-center gap-2 rounded-[10px] border border-primary bg-[#FDF7F6] p-4 text-primary transition-colors hover:bg-primary hover:text-white"
          >
            <span className="truncate">{getSelectedCategoryDisplay()}</span>
            <ChevronDown
              className={`h-4 w-4 transition-transform ${showCategoryDropdown ? 'rotate-180' : ''}`}
            />
          </Button>

          {showCategoryDropdown && (
            <div className="absolute right-0 top-full z-10 mt-1 w-64 bg-white rounded-lg border border-gray-200 shadow-lg">
              <div className="overflow-y-auto py-1 max-h-60">
                <button
                  onClick={() => handleCategorySelect('')}
                  className={`w-full px-4 py-2 text-left text-sm hover:bg-gray-50 ${
                    !selectedCategory
                      ? 'bg-primary/10 text-primary'
                      : 'text-gray-700'
                  }`}
                >
                  All Categories
                </button>
                {categories.map(category => (
                  <button
                    key={category.categoryName}
                    onClick={() => handleCategorySelect(category.categoryName)}
                    className={`w-full px-4 py-2 text-left text-sm hover:bg-gray-50 ${
                      selectedCategory === category.categoryName
                        ? 'bg-primary/10 text-primary'
                        : 'text-gray-700'
                    }`}
                  >
                    {category.categoryAlias}
                  </button>
                ))}
                {isLoadingCategories && (
                  <div className="px-4 py-2 text-sm text-gray-500">
                    Loading categories...
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Apps Grid */}
      <div className="overflow-y-auto flex-1">
        {isLoading ? (
          <div className="flex justify-center items-center py-24 h-full">
            <div className="flex flex-col gap-3 items-center">
              <Spinner />
              <p className="text-sm text-gray-500">Loading available apps...</p>
            </div>
          </div>
        ) : filteredApps.length === 0 ? (
          <div className="flex justify-center items-center py-20 h-full">
            <div className="text-center">
              <div className="mb-4 text-4xl">🔍</div>
              <h3 className="mb-2 text-lg font-medium text-blackOne">
                {searchQuery ? 'No apps found' : 'No apps available'}
              </h3>
              <p className="text-sm text-gray-500">
                {searchQuery
                  ? `No apps match "${searchQuery}". Try a different search term.`
                  : 'There are no available apps to display at the moment.'}
              </p>
            </div>
          </div>
        ) : (
          <div className="grid grid-cols-1 gap-x-4 gap-y-10 sm:grid-cols-2 lg:grid-cols-4">
            {filteredApps.map(app => (
              <AppCard
                key={app.key}
                app={app}
                onConnect={onConnectApp}
                connectionFlow={connectionFlow}
              />
            ))}
          </div>
        )}
      </div>
    </div>
  );
};
