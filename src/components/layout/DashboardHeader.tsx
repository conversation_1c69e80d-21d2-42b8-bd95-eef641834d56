"use client";

import { motion, AnimatePresence } from "framer-motion";
import { Search, X } from "lucide-react";
import { useLocation, useNavigate } from "react-router-dom";
import { useState, useRef, useEffect } from "react";
import Logo from "../ui/Logo";
import { UserDropdown } from "../ui/UserDropdown";
import { ROUTES } from "../../constants/routes";
import { Button } from "@/components/ui";
import { useTenant } from "@/context/TenantContext";
import { chatIcon2 } from "@/assets/icons";

interface DashboardHeaderProps {
  isSidebarCollapsed: boolean;
}

// eslint-disable-next-line no-empty-pattern
export function DashboardHeader({}: DashboardHeaderProps) {
  const location = useLocation();
  const navigate = useNavigate();
  const { setActiveAgent } = useTenant();
  const [isSearchOpen, setIsSearchOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const searchInputRef = useRef<HTMLInputElement>(null);

  const isSettingsPage = location.pathname.includes("/settings");

  useEffect(() => {
    if (isSearchOpen && searchInputRef.current) {
      searchInputRef.current.focus();
    }
  }, [isSearchOpen]);

  const handleCloseSettings = () => {
    navigate(ROUTES.DASHBOARD_AI_AGENTS);
  };

  const toggleSearch = () => {
    if (isSearchOpen && searchQuery.trim()) {
      // TODO: Implement search functionality
      console.log("Searching for:", searchQuery);
    }
    setIsSearchOpen(!isSearchOpen);
    if (isSearchOpen) {
      setSearchQuery("");
    }
  };

  const handleSearchSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      // TODO: Implement search functionality
      console.log("Searching for:", searchQuery);
      setIsSearchOpen(false);
      setSearchQuery("");
    }
  };

  const handleSearchKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Escape") {
      setIsSearchOpen(false);
      setSearchQuery("");
    }
  };

  const handleChatWithRegis = () => {
    setActiveAgent("regis");
    navigate(ROUTES.DASHBOARD_AI_AGENTS);
  };

  return (
    <motion.header
      initial={{ y: -20, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      className="border-b border-[#E6E6E6] bg-[#FFFDF9]/80 px-4 py-3 backdrop-blur-sm"
    >
      <div className="flex justify-between items-center">
        {/* Left Section */}
        <div className="flex flex-shrink-0 items-center gap-4 md:w-[300px]">
          <Logo />
        </div>

        {/* Right Section */}
        <div className="flex gap-4 justify-between items-center w-full">
          {isSettingsPage ? (
            <h1 className="text-2xl font-medium text-subText">Settings</h1>
          ) : (
            <span />
          )}
          <div className="flex gap-4 items-center">
            {isSettingsPage ? (
              // Settings mode - show close button
              <button
                onClick={handleCloseSettings}
                className="flex h-8 w-8 items-center justify-center rounded-full bg-primary transition-colors hover:bg-red-600 md:h-[48px] md:w-[48px]"
                title="Close settings"
              >
                <X
                  className="w-4 h-4 text-white sm:w-6 sm:h-6"
                  strokeWidth={3}
                />
              </button>
            ) : (
              // Normal mode - show regular buttons and user dropdown
              <>
                {/* Search Section */}
                <div className="hidden relative items-center">
                  {/* <div className="flex relative items-center"> */}
                  <AnimatePresence>
                    {isSearchOpen && (
                      <motion.div
                        initial={{ width: 0, opacity: 0 }}
                        animate={{ width: 300, opacity: 1 }}
                        exit={{ width: 0, opacity: 0 }}
                        transition={{
                          duration: 0.3,
                          ease: [0.4, 0, 0.2, 1],
                        }}
                        className="overflow-hidden absolute top-0 right-12"
                      >
                        <form
                          onSubmit={handleSearchSubmit}
                          className="relative"
                        >
                          <motion.input
                            ref={searchInputRef}
                            type="text"
                            value={searchQuery}
                            onChange={(e) => setSearchQuery(e.target.value)}
                            onKeyDown={handleSearchKeyDown}
                            placeholder="Search..."
                            initial={{ opacity: 0 }}
                            animate={{ opacity: 1 }}
                            transition={{ delay: 0.1 }}
                            className="h-[44px] w-full rounded-lg border border-gray-300 bg-white px-4 pr-10 text-sm focus:border-primary focus:outline-none focus:ring-2 focus:ring-primary/20"
                          />
                          <button
                            type="submit"
                            className="flex absolute right-2 top-1/2 justify-center items-center w-6 h-6 text-gray-400 rounded-lg -translate-y-1/2 hover:text-primary"
                          >
                            <Search className="w-4 h-4" />
                          </button>
                        </form>
                      </motion.div>
                    )}
                  </AnimatePresence>

                  <motion.button
                    onClick={toggleSearch}
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    className="flex h-[44px] items-center justify-center rounded-xl p-3 hover:bg-gray-100 transition-colors relative z-10"
                  >
                    <motion.div
                      animate={isSearchOpen ? { rotate: 180 } : { rotate: 0 }}
                      transition={{ duration: 0.2 }}
                    >
                      {isSearchOpen ? (
                        <X className="w-5 h-5" />
                      ) : (
                        <Search className="w-5 h-5" />
                      )}
                    </motion.div>
                  </motion.button>
                </div>

                <Button
                  className="flex items-center justify-center gap-2 h-[44px] rounded-md bg-primary hover:bg-orange-15 py-2.5 px-4 text-white transition-colors"
                  onClick={handleChatWithRegis}
                >
                  <img src={chatIcon2} alt="" />
                  <span className="mt-0.5 font-spartan">Chat with Regis</span>
                </Button>

                <Button className="h-[44px] hidden rounded-lg border border-primary bg-white p-4 text-primary">
                  Upgrade
                </Button>
                <UserDropdown />
              </>
            )}
          </div>
        </div>
      </div>
    </motion.header>
  );
}
