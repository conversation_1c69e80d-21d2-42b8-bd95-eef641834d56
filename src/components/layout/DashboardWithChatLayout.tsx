import React, { MutableRefObject, ReactNode } from 'react';

import EnhancedChatSidebar from '@/components/common/EnhancedChatSidebar';
import { ConnectionFlowContext } from '@/types/businessStack';

interface DashboardWithChatLayoutProps {
  children: ReactNode;
  connectionFlow?: ConnectionFlowContext;
  reloadChatHistoryRef?: MutableRefObject<(() => Promise<void>) | null>;
  externalMessage?: string;
  className?: string;
  chatClassName?: string;
}

/**
 * Reusable dashboard layout with integrated chat sidebar
 *
 * Features:
 * - Fixed width chat sidebar that doesn't shrink
 * - Proper overflow handling for main content
 * - Flexible content area with scroll support
 * - Chat integration with connection flows
 */
export const DashboardWithChatLayout: React.FC<
  DashboardWithChatLayoutProps
> = ({
  children,
  connectionFlow,
  reloadChatHistoryRef,
  externalMessage,
  className = '',
  chatClassName = '',
}) => {
  return (
    <div className={`flex h-[calc(100vh-70px)] overflow-hidden ${className}`}>
      {/* Chat Sidebar - Fixed width, won't shrink */}
      <EnhancedChatSidebar
        connectionFlow={connectionFlow}
        reloadChatHistoryRef={reloadChatHistoryRef}
        externalMessage={externalMessage}
        className={chatClassName}
      />

      {/* Main Content Area - Flexible, with proper overflow handling */}
      <div className="relative flex h-full min-w-0 flex-1 flex-col">
        <div className="min-h-0 flex-1 overflow-y-auto">{children}</div>
      </div>
    </div>
  );
};

/**
 * Reusable container for table pages that prevents horizontal overflow
 *
 * Features:
 * - Horizontal scroll for tables that exceed container width
 * - Maintains table within viewport boundaries
 * - Consistent padding and spacing
 */
export const TablePageContainer: React.FC<{
  children: ReactNode;
  className?: string;
  minTableWidth?: string;
}> = ({ children, className = '', minTableWidth = 'min-w-[800px]' }) => {
  return (
    <div className={`overflow-hidden px-8 ${className}`}>
      <div className="overflow-x-auto">
        <div className={minTableWidth}>{children}</div>
      </div>
    </div>
  );
};

export default DashboardWithChatLayout;
