import React, { useState, useEffect } from 'react';
import { Outlet, useLocation } from 'react-router-dom';
import { DismissibleBanner } from '../common/DismissibleBanner';
import { DashboardHeader } from './DashboardHeader';
import DashboardSidebar from '../sidebar/DashboardSidebar';

const DashboardShell: React.FC = () => {
  const location = useLocation();
  const isSettingsPage = location.pathname.includes('/settings');

  // Initialize sidebar state from session storage or default based on page type
  const [isCollapsed, setIsCollapsed] = useState<boolean>(() => {
    const savedState = sessionStorage.getItem('pivotl-sidebar-collapsed');
    if (savedState !== null) {
      return JSON.parse(savedState);
    }
    return !isSettingsPage;
  });

  // Save sidebar state to session storage whenever it changes
  useEffect(() => {
    sessionStorage.setItem(
      'pivotl-sidebar-collapsed',
      JSON.stringify(isCollapsed),
    );
  }, [isCollapsed]);

  // Update sidebar state when navigating between settings and non-settings pages
  useEffect(() => {
    const savedState = sessionStorage.getItem('pivotl-sidebar-collapsed');
    if (savedState === null) {
      setIsCollapsed(!isSettingsPage);
    }
  }, [isSettingsPage]);

  return (
    <div className="flex h-screen flex-col font-inter">
      <DismissibleBanner
        id="professional-trial"
        title="Welcome to your Professional trial!"
        message={
          <span className=" text-[#1FC16B">
            You have 13 days to try PivotL's{' '}
            <a
              href="#"
              className="font-normal text-primary underline hover:no-underline"
            >
              paid features
            </a>
            . Upgrade anytime for as low as $19.99 USD/month.
          </span>
        }
        variant="success"
        hide={true}
      />

      {/* Header */}
      <DashboardHeader isSidebarCollapsed={isCollapsed} />

      {/* Main Content Area */}
      <div className="flex flex-1 flex-row overflow-hidden">
        <DashboardSidebar
          isCollapsed={isCollapsed}
          toggleSidebar={() => setIsCollapsed(!isCollapsed)}
        />
        {/* Page Content */}
        <main className={`flex-1 overflow-y-auto bg-[#FAFAFA]`}>
          <Outlet />
        </main>
      </div>
    </div>
  );
};

export default DashboardShell;
