import { Outlet } from 'react-router-dom';

import { ErrorBoundary } from '../ui/ErrorBoundary';
import { Footer } from './Footer';
import { Navbar } from './Navbar';

const NavigationShell = () => {
  const currentPath = window.location.pathname;

  return (
    <ErrorBoundary>
      <div className="flex min-h-screen flex-col">
        <Navbar />
        <main className="flex-1">
          <Outlet />
        </main>
        {currentPath !== '/agents' && <Footer />}
      </div>
    </ErrorBoundary>
  );
};

export default NavigationShell;
