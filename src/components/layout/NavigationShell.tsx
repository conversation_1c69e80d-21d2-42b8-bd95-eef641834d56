import { Outlet } from "react-router-dom";
import { Navbar } from "./Navbar";
import { Footer } from "./Footer";
import { ErrorBoundary } from "../ui/ErrorBoundary";

const NavigationShell = () => {
  const currentPath = window.location.pathname;

  return (
    <ErrorBoundary>
      <div className="flex flex-col min-h-screen">
        <Navbar />
        <main className="flex-1">
          <Outlet />
        </main>
        {currentPath !== "/agents" && <Footer />}
      </div>
    </ErrorBoundary>
  );
};

export default NavigationShell;
