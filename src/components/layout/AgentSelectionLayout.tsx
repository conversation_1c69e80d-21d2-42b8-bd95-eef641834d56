import clsx from 'clsx';
import React, { useRef } from 'react';

import { useTenant } from '@/context/TenantContext';
import { AIAgentSuite } from '@/types/agents';

import { Icons } from '../../assets/icons/DashboardIcons';
import { useGetAIAgentSuites } from '../../hooks/useAgents';
import { AgentSuiteCard } from '../../pages/AiAgentsPage';
import EnhancedChatSidebar from '../common/EnhancedChatSidebar';
import AgentSuiteSkeletonLoader from '../ui/AgentSuiteSkeleton';

interface AgentSelectionLayoutProps {
  title: string;
  description: string;
  bgImage: string;
  pageType: 'knowledge-base' | 'business-stack' | 'dashboard';
  onAgentSuiteClick: (suite: AIAgentSuite) => void;
  className?: string;
}

const AgentSelectionLayout: React.FC<AgentSelectionLayoutProps> = ({
  title,
  description,
  bgImage,
  pageType,
  onAgentSuiteClick,
  className,
}) => {
  const reloadChatHistoryRef = useRef<(() => Promise<void>) | null>(null);
  const { data: agentSuites = [], isLoading, error } = useGetAIAgentSuites();
  const { activeAgent, setActiveAgent } = useTenant();

  // Handle agents suite card click
  const handleAgentSuiteClick = (suite: any) => {
    if (activeAgent === 'regis') {
      setActiveAgent(suite.availableAgents[0]?.agentKey);
    }
    onAgentSuiteClick(suite);
  };

  return (
    <div className="flex h-full">
      {/* Chat Interface - LHS */}
      <EnhancedChatSidebar reloadChatHistoryRef={reloadChatHistoryRef} />

      {/* Main Content - RHS */}
      <div className={clsx('flex flex-1 flex-col gap-6 overflow-y-auto p-8')}>
        <div className="flex flex-col gap-4 text-start">
          <div className="mb-2 flex items-center gap-2">
            {pageType === 'knowledge-base' && (
              <Icons.Knowledge className="h-6 w-6 text-primary" />
            )}
            {pageType === 'business-stack' && (
              <Icons.Stack className="h-6 w-6 text-primary" />
            )}
            {pageType === 'dashboard' && (
              <Icons.Dashboard className="h-6 w-6 text-primary" />
            )}
            <h1 className="text-2xl font-semibold text-blackOne">
              {pageType === 'knowledge-base'
                ? 'Knowledge Base'
                : pageType === 'business-stack'
                  ? 'Business Stack'
                  : 'Dashboard'}
            </h1>
          </div>

          {/* Hero Section */}
          <div
            className={clsx(
              'flex h-[140px] max-w-4xl items-center overflow-hidden rounded-2xl bg-cover bg-center text-white',
              className
            )}
            style={{
              backgroundImage: `url(${pageType === 'dashboard' ? '' : bgImage})`,
            }}
          >
            <div className="flex w-full items-center justify-between">
              <div className="p-6 text-left">
                <h2 className="mb-2 text-lg font-bold text-white">{title}</h2>
                <p className="text-sm text-white">{description}</p>
              </div>
            </div>
            <div className="relative mr-8 h-full w-[178px]">
              <img src={bgImage} alt="bg" className="h-full w-full" />
            </div>
          </div>
        </div>

        {/* Activate an agent to begin section */}
        <h3 className="text-lg font-medium text-blackOne">
          {pageType === 'knowledge-base'
            ? 'Select Suite -> Upload Knowledge Base'
            : pageType === 'business-stack'
              ? 'Select Suite -> Connect Business Systems'
              : 'Select Suite -> View Dashboard'}
        </h3>
        {/* Error State */}
        {error && (
          <div className="mb-4 rounded-lg border border-red-200 bg-red-50 p-4 text-red-700">
            <p>Error loading agent suites: {error.message}</p>
          </div>
        )}

        {/* Content */}
        <div className="w-fit">
          {isLoading ? (
            <AgentSuiteSkeletonLoader count={4} />
          ) : (
            <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-2">
              {agentSuites.map(suite => (
                <AgentSuiteCard
                  key={suite.agentSuiteKey}
                  suite={suite}
                  link={'#'}
                  onAgentSuiteClick={() => handleAgentSuiteClick(suite)}
                />
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default AgentSelectionLayout;
