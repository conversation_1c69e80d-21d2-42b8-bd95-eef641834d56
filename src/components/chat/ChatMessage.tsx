import { ChatMessage as ChatMessageType } from "../../types/agents";
import { User } from "lucide-react";
import moment from "moment";
import { useTenant } from "../../context/TenantContext";
import { UserBasicInfoPayload } from "../../types/user";
import { getAgentAvatar } from "../../utils/agentUtils";
import { useGetUserProfile } from "@/hooks/useUserProfile";

interface ChatMessageProps {
  message: ChatMessageType;
}

export const ChatMessage = ({ message }: ChatMessageProps) => {
  const isUser = message.sender === "user";
  const { activeAgent } = useTenant();
  const { data: userData } = useGetUserProfile<UserBasicInfoPayload>();

  const agentAvatar = getAgentAvatar(activeAgent, userData);

  return (
    <div className="flex gap-3 mb-6">
      {/* Avatar */}
      <div className="flex-shrink-0 w-10 h-10">
        {isUser ? (
          <div className="flex justify-center items-center w-full h-full bg-gray-200 rounded-full">
            <User className="w-5 h-5 text-gray-600" />
          </div>
        ) : (
          <div className="rounded-full bg-peachTwo">
            <img
              src={agentAvatar}
              alt={activeAgent ? `${activeAgent} Agent` : "Agent"}
              className="object-cover w-full h-full rounded-full"
            />
          </div>
        )}
      </div>

      {/* Message Content */}
      <div className="flex-1">
        {/* Header */}
        <div className="flex gap-2 items-center mb-1">
          <span className="font-semibold text-darkGray">
            {message.senderName}
          </span>
          <span className="text-sm text-gray-400">
            {moment(message.timestamp).format("h:mm A")}
          </span>
        </div>

        {/* Message Text */}
        <div className="p-3 rounded bg-gray-5 text-grayTwentyFour">
          {message.content}
        </div>
      </div>
    </div>
  );
};
