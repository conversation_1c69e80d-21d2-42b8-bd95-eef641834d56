import { useState, useRef, KeyboardEvent } from "react";
import { SendHorizontal } from "lucide-react";

interface ChatInputProps {
  onSendMessage: (message: string) => void;
  disabled?: boolean;
  placeholder?: string;
}

export const ChatInput = ({
  onSendMessage,
  disabled = false,
  placeholder = "I'm here — whenever you're ready.",
}: ChatInputProps) => {
  const [message, setMessage] = useState("");
  const [isFocused, setIsFocused] = useState(false);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  const handleInput = () => {
    if (textareaRef.current) {
      textareaRef.current.style.height = "auto";
      textareaRef.current.style.height = `${Math.min(
        textareaRef.current.scrollHeight,
        200 // max height in pixels
      )}px`;
    }
  };

  const handleSend = () => {
    if (message.trim() && !disabled) {
      onSendMessage(message.trim());
      setMessage("");
      if (textareaRef.current) {
        textareaRef.current.style.height = "auto";
      }
    }
  };

  const handleKeyDown = (e: KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSend();
    }
  };

  return (
    <div
      className={`relative flex w-full items-end rounded-md border bg-white font-inter ${
        isFocused ? "border-primary" : "border-grayNine"
      } px-2 py-1.5 transition-colors ${disabled ? "opacity-50" : ""}`}
    >
      <textarea
        ref={textareaRef}
        value={message}
        disabled={disabled}
        className="w-full resize-none bg-transparent text-grayTen outline-none [border:none] [box-shadow:none] placeholder:text-gray-400 focus:[border:none] focus:[box-shadow:none] active:[border:none] disabled:cursor-not-allowed"
        onChange={(e) => {
          setMessage(e.target.value);
          handleInput();
        }}
        onFocus={() => setIsFocused(true)}
        onBlur={() => setIsFocused(false)}
        onKeyDown={handleKeyDown}
        placeholder={placeholder}
        rows={1}
        style={{
          maxHeight: "200px",
          overflowY: "auto",
        }}
      />
      <button
        onClick={handleSend}
        disabled={!message.trim() || disabled}
        className={`ml-2 rounded-full p-2 transition-colors ${
          message.trim() && !disabled
            ? "text-primary hover:bg-primary/10"
            : "cursor-not-allowed text-grayNine"
        }`}
      >
        <SendHorizontal className="h-5 w-5" />
      </button>
    </div>
  );
};
