import { useEffect, useRef, useState } from "react";
import { User, ArrowDown } from "lucide-react";
import moment from "moment";
import ReactMarkdown from "react-markdown";
import { scyra } from "../../assets/images";
import { TypingIndicator } from "./TypingIndicator";
import {
  ScyraChatInterfaceProps,
  ScyraMessage,
} from "../../types/businessStack";
import { useTenant } from '../../context/TenantContext';
import { UserBasicInfoPayload } from "../../types/user";
import { getAgentAvatar } from "../../utils/agentUtils";
import { useGetUserProfile } from '@/hooks/useUserProfile';

const ScyraMessageComponent = ({ message }: { message: ScyraMessage }) => {
  const isUser = message.sender === 'user';
  const { activeAgent } = useTenant();
  const { data: userData } = useGetUserProfile<UserBasicInfoPayload>();

  const agentAvatar = getAgentAvatar(activeAgent, userData);

  return (
    <div className="flex gap-3 mb-6">
      {/* Avatar */}
      <div className="flex-shrink-0 w-10 h-10">
        {isUser ? (
          <div className="flex justify-center items-center w-full h-full rounded-full bg-peachTwo">
            <User className="w-5 h-5 text-gray-600" />
          </div>
        ) : (
          <div className="rounded-full bg-grayTwentySix">
            <img
              src={agentAvatar}
              alt={activeAgent ? `${activeAgent} Agent` : 'Agent'}
              className="object-cover w-full h-full rounded-full"
            />
          </div>
        )}
      </div>

      {/* Message Content */}
      <div className="flex-1">
        {/* Header */}
        <div className="flex gap-2 items-center mb-1">
          <span className="font-semibold text-darkGray">
            {message.senderName}
          </span>
          <span className="text-sm text-gray-400">
            {moment(message.timestamp).format('h:mm A')}
          </span>
        </div>

        {/* Message Text */}
        <div className="p-3 rounded-lg bg-gray-5 text-grayTwentyFour">
          <ReactMarkdown
            components={{
              // Customize markdown components to match existing styling
              p: ({ children }) => <p className="mb-2 last:mb-0">{children}</p>,
              h1: ({ children }) => (
                <h1 className="mb-2 text-lg font-bold">{children}</h1>
              ),
              h2: ({ children }) => (
                <h2 className="mb-2 text-base font-bold">{children}</h2>
              ),
              h3: ({ children }) => (
                <h3 className="mb-2 text-sm font-bold">{children}</h3>
              ),
              ul: ({ children }) => (
                <ul className="mb-2 ml-4 list-disc">{children}</ul>
              ),
              ol: ({ children }) => (
                <ol className="mb-2 ml-4 list-decimal">{children}</ol>
              ),
              li: ({ children }) => <li className="mb-2">{children}</li>,
              code: ({ children }) => (
                <code className="rounded bg-gray-200 px-1 py-0.5 font-mono text-sm">
                  {children}
                </code>
              ),
              pre: ({ children }) => (
                <pre className="overflow-x-auto p-2 mb-2 font-mono text-sm bg-gray-200 rounded">
                  {children}
                </pre>
              ),
              a: ({ children, href }) => (
                <a
                  href={href}
                  className="text-primary hover:underline"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  {children}
                </a>
              ),
              strong: ({ children }) => (
                <strong className="font-bold">{children}</strong>
              ),
              em: ({ children }) => <em className="italic">{children}</em>,
              // Custom text renderer to handle whitespace
              text: ({ children }) => {
                if (typeof children === 'string') {
                  // Remove excessive whitespace but preserve single spaces
                  return children.replace(/\s+/g, ' ');
                }
                return children;
              },
            }}
          >
            {message.content}
          </ReactMarkdown>
        </div>
      </div>
    </div>
  );
};

export const ScyraChatInterface = ({
  state,
  ChatInputComponent,
  groupedMessages,
}: ScyraChatInterfaceProps) => {
  const messagesContainerRef = useRef<HTMLDivElement>(null);
  const [showScrollToBottom, setShowScrollToBottom] = useState(false);
  const [isUserAtBottom, setIsUserAtBottom] = useState(true);

  const scrollToBottom = () => {
    if (messagesContainerRef.current) {
      messagesContainerRef.current.scrollTo({
        top: messagesContainerRef.current.scrollHeight,
        behavior: "smooth",
      });
    }
  };

  // Show/hide down-arrow when user scrolls up and track user position
  useEffect(() => {
    const container = messagesContainerRef.current;
    if (!container) return;
    const handleScroll = () => {
      // If user is not at the bottom (within 40px), show arrow
      const atBottom =
        container.scrollHeight - container.scrollTop - container.clientHeight <
        40;
      setShowScrollToBottom(!atBottom);
      setIsUserAtBottom(atBottom);
    };
    container.addEventListener("scroll", handleScroll);
    // Initial check
    handleScroll();
    return () => container.removeEventListener("scroll", handleScroll);
  }, [state.messages.length]);

  // Auto-scroll to bottom only when user is already at bottom and new messages arrive
  useEffect(() => {
    // Only auto-scroll if user is at the bottom or this is the initial load
    if (isUserAtBottom || state.messages.length === 0) {
      scrollToBottom();
    }
  }, [state.messages, isUserAtBottom]);

  // Handle loading state changes - only scroll if user is at bottom
  useEffect(() => {
    if (!state.isLoading && isUserAtBottom) {
      // Small delay to ensure DOM is updated after loading completes
      setTimeout(() => {
        scrollToBottom();
      }, 100);
    }
  }, [state.isLoading, isUserAtBottom]);

  return (
    <div className="flex relative flex-col h-full">
      {/* Messages Container */}
      <div
        ref={messagesContainerRef}
        className="overflow-y-auto flex-1 px-4 py-4"
        style={{ minHeight: 0 }} // Ensures flex-1 works properly
      >
        {Object.entries(groupedMessages || {}).map(([date, messages]) => (
          <div key={date}>
            <div className="mb-2 text-xs font-semibold text-center text-gray-400">
              {moment(date).calendar(null, {
                sameDay: '[Today]',
                lastDay: '[Yesterday]',
                lastWeek: 'dddd',
                sameElse: 'MMM D, YYYY',
              })}
            </div>
            {messages.map((message) => (
              <ScyraMessageComponent key={message.id} message={message} />
            ))}
          </div>
        ))}

        {/* Typing Indicator */}
        {state.isLoading && (
          <TypingIndicator agentImageSrc={scyra} agentName="Scyra" />
        )}
      </div>

      {/* Floating Down Arrow */}
      {showScrollToBottom && (
        <button
          className="flex absolute right-6 bottom-24 z-20 justify-center items-center w-10 h-10 bg-white rounded-full border border-gray-200 shadow-lg transition hover:bg-gray-50"
          onClick={scrollToBottom}
          aria-label="Scroll to latest message"
        >
          <ArrowDown className="w-6 h-6 text-primary" />
        </button>
      )}

      {/* Chat Input */}
      <div className="flex-shrink-0 px-4 py-4">
        <ChatInputComponent />
      </div>
    </div>
  );
};
