import clsx from 'clsx';
import React, { ReactNode, useMemo } from 'react';

import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/Avatar';

interface UserAvatarProps {
  fullName: string;
  profileImage?: string;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  isLabel?: boolean;
  maxNameLength?: number;
  labelComponent?: ReactNode;
  className?: string;
  onClick?: () => void;
  defaultColor?: string; // Hex, RGB, or CSS color value
  useRandomColor?: boolean; // Whether to use random color generation
  border?: boolean; // Whether to show border
  borderColor?: string; // Border color
  borderWidth?: number; // Border width in pixels
  textColor?: string; // Custom text color for initials
}

function generateColor(seed: string): string {
  let hash = 0;
  for (let i = 0; i < seed.length; i++) {
    hash = seed.charCodeAt(i) + ((hash << 5) - hash);
  }

  const h = hash % 360;
  const s = 50 + (hash % 30); // Saturation between 50% and 80%
  const l = 65 + (hash % 20); // Lightness between 65% and 85%

  return `hsl(${h}, ${s}%, ${l}%)`;
}

function getContrastColor(bgColor: string): string {
  const hsl = bgColor.match(/\d+/g)?.map(Number);
  if (!hsl || hsl.length !== 3) return '#000000';

  const l = hsl[2];
  return l > 60 ? '#000000' : '#FFFFFF';
}

const UserAvatar: React.FC<UserAvatarProps> = ({
  fullName = 'User',
  profileImage,
  size = 'md',
  className,
  isLabel = true,
  maxNameLength = 20,
  labelComponent,
  onClick,
  defaultColor,
  useRandomColor = true,
  border = false,
  borderColor = '#E5E7EB', // Default gray border
  borderWidth = 2,
  textColor: customTextColor, // Rename to avoid conflict
}) => {
  const truncatedName =
    fullName.length > maxNameLength
      ? `${fullName.slice(0, maxNameLength)}...`
      : fullName;

  const initials = fullName
    .split(' ')
    .map(name => name.charAt(0).toUpperCase())
    .join('');

  const backgroundColor = useMemo(() => {
    if (defaultColor) return defaultColor;
    if (useRandomColor) return generateColor(fullName);
    return '#6B7280'; // Fallback gray color
  }, [fullName, defaultColor, useRandomColor]);

  const finalTextColor = useMemo(() => {
    if (customTextColor) return customTextColor; // Use custom text color if provided
    return getContrastColor(backgroundColor); // Otherwise use auto-contrast
  }, [backgroundColor, customTextColor]);

  const sizeClasses = {
    sm: 'w-8 h-8 text-xs',
    md: 'w-11 h-11 text-sm',
    lg: 'w-14 h-14 text-base',
    xl: 'w-20 h-20 text-lg',
  };

  const borderStyles = border
    ? {
        border: `${borderWidth}px solid ${borderColor}`,
      }
    : {};

  return (
    <div
      className={clsx(
        'flex w-full cursor-pointer items-center gap-2.5',
        className
      )}
      onClick={onClick}
    >
      <Avatar
        className={sizeClasses[size]}
        style={{
          backgroundColor,
          ...borderStyles,
        }}
      >
        <AvatarImage
          src={profileImage}
          alt={fullName}
          className="object-cover"
        />
        <AvatarFallback
          style={{
            backgroundColor,
            color: finalTextColor,
            textTransform: 'uppercase',
            fontWeight: 500,
            marginTop: '0.5px',
          }}
        >
          {initials}
        </AvatarFallback>
      </Avatar>
      <>
        {labelComponent && labelComponent}
        {isLabel && <span className="text-sm">{truncatedName}</span>}
      </>
    </div>
  );
};

export default UserAvatar;
