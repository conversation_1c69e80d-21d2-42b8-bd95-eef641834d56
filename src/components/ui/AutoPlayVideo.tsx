import React, { useRef, useEffect } from 'react';

interface AutoPlayVideoProps {
  videoSrc: string;
  fallbackImage?: string;
  className?: string;
  altText?: string;
}

const AutoPlayVideo: React.FC<AutoPlayVideoProps> = ({
  videoSrc,
  fallbackImage,
  className = '',
  altText = 'Video content',
}) => {
  const videoRef = useRef<HTMLVideoElement>(null);

  // Handle play/pause when in/out of viewport
  useEffect(() => {
    const observer = new IntersectionObserver(
      entries => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            videoRef.current
              ?.play()
              .catch(e => console.log('Autoplay prevented:', e));
          } else {
            videoRef.current?.pause();
          }
        });
      },
      { threshold: 0.5 },
    );

    const currentVideo = videoRef.current;
    if (currentVideo) {
      observer.observe(currentVideo);
    }

    return () => {
      if (currentVideo) {
        observer.unobserve(currentVideo);
      }
    };
  }, []);

  return (
    <div className={`overflow-hidden ${className}`}>
      <video
        ref={videoRef}
        autoPlay
        loop
        muted
        playsInline
        className="h-auto w-full rounded-sm"
      >
        <source src={videoSrc} type={`video/${videoSrc.split('.').pop()}`} />
        {fallbackImage ? (
          <img src={fallbackImage} alt={altText} className="h-auto w-full" />
        ) : (
          <div className="bg-gray-100 p-4 text-center">
            Your browser does not support the video tag.
          </div>
        )}
      </video>
    </div>
  );
};

export default AutoPlayVideo;
