import React from 'react';
import { motion } from 'framer-motion';
import clsx from 'clsx';

export interface Tab {
  id: string;
  label: string | React.ReactNode;
  content?: React.ReactNode;
}

interface AnimatedTabsProps {
  tabs: Tab[];
  activeTab: string;
  onTabChange: (tabId: string) => void;
  className?: string;
  tabClassName?: string;
  activeTabClassName?: string;
  contentClassName?: string;
  showContent?: boolean;
}

const AnimatedTabs: React.FC<AnimatedTabsProps> = ({
  tabs,
  activeTab,
  onTabChange,
  className = '',
  tabClassName = '',
  contentClassName = '',
  showContent = true,
}) => {
  const activeTabContent = tabs.find((tab) => tab.id === activeTab)?.content;

  return (
    <div className={clsx('w-fit', className)}>
      {/* Tab Headers */}
      <div className="flex relative gap-6 border-b border-[#EBEEF2]">
        {tabs.map((tab) => (
          <button
            key={tab.id}
            onClick={() => onTabChange(tab.id)}
            className={clsx(
              'relative py-2 pl-1 text-sm sm:text-base font-medium transition-colors',
              activeTab === tab.id
                ? clsx('text-primary')
                : clsx('text-grayNine hover:text-blackOne', tabClassName)
            )}
          >
            {typeof tab.label === 'string' ? tab.label : tab.label}
            {activeTab === tab.id && (
              <motion.div
                layoutId="activeTabIndicator"
                className="absolute bottom-0 left-0 right-0 h-0.5 bg-primary"
                initial={false}
                transition={{
                  type: 'spring',
                  stiffness: 500,
                  damping: 30,
                }}
              />
            )}
          </button>
        ))}
      </div>

      {/* Tab Content */}
      {showContent && activeTabContent && (
        <motion.div
          key={activeTab}
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -10 }}
          transition={{
            duration: 0.2,
            ease: 'easeInOut',
          }}
          className={clsx('mt-4', contentClassName)}
        >
          {activeTabContent}
        </motion.div>
      )}
    </div>
  );
};

export default AnimatedTabs;