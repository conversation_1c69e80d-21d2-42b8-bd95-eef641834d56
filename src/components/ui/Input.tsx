import { InputHTMLAttributes, forwardRef } from 'react';
import { clsx } from 'clsx';

export interface InputProps extends InputHTMLAttributes<HTMLInputElement> {
  label?: string;
  error?: string;
  helperText?: string;
  fullWidth?: boolean;
  startIcon?: React.ReactNode;
  endIcon?: React.ReactNode;
}

const Input = forwardRef<HTMLInputElement, InputProps>(
  (
    {
      className,
      label,
      error,
      helperText,
      fullWidth = false,
      startIcon,
      endIcon,
      id,
      ...props
    },
    ref,
  ) => {
    const inputId = id || `input-${Math.random().toString(36).substr(2, 9)}`;

    const baseClasses =
      'block px-3 py-2 border rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-offset-0 sm:text-sm transition-colors';

    const stateClasses = error
      ? 'border-red-300 text-red-900 focus:ring-red-500 focus:border-red-500'
      : 'border-gray-300 focus:ring-orange-500 focus:border-orange-500';

    const classes = clsx(
      baseClasses,
      stateClasses,
      fullWidth && 'w-full',
      startIcon && 'pl-10',
      endIcon && 'pr-10',
      className
    );

    return (
      <div className={clsx('relative', fullWidth && 'w-full')}>
        {label && (
          <label
            htmlFor={inputId}
            className="block mb-1 text-sm font-medium text-gray-700"
          >
            {label}
          </label>
        )}

        <div className="relative">
          {startIcon && (
            <div className="flex absolute inset-y-0 left-0 items-center pl-3 pointer-events-none">
              <div className="w-5 h-5 text-gray-400">{startIcon}</div>
            </div>
          )}

          <input ref={ref} id={inputId} className={classes} {...props} />

          {endIcon && (
            <div className="flex absolute inset-y-0 right-0 items-center pr-3 pointer-events-none">
              <div className="w-5 h-5 text-gray-400">{endIcon}</div>
            </div>
          )}
        </div>

        {error && <p className="mt-1 text-sm text-red-600">{error}</p>}

        {helperText && !error && (
          <p className="mt-1 text-sm text-gray-500">{helperText}</p>
        )}
      </div>
    );
  },
);

Input.displayName = 'Input';

export { Input };
