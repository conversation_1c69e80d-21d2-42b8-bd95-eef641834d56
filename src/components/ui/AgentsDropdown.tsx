import React from 'react';
import { motion } from 'framer-motion';
import { ChevronDown, ChevronUp } from 'lucide-react';

export interface DropdownOption {
  id: string;
  name: string;
  icon: string;
}

interface AgentsDropdownProps {
  isOpen: boolean;
  onToggle: () => void;
  currentItem?: DropdownOption;
  options: DropdownOption[];
  onItemSelect: (option: DropdownOption) => void;
  placeholder: string;
  noOptionsMessage: string;
  onImageError?: (event: React.SyntheticEvent<HTMLImageElement, Event>) => void;
  getOptionImageError?: (
    option: DropdownOption
  ) => (event: React.SyntheticEvent<HTMLImageElement, Event>) => void;
}

const AgentsDropdown: React.FC<AgentsDropdownProps> = ({
  isOpen,
  onToggle,
  currentItem,
  options,
  onItemSelect,
  placeholder,
  noOptionsMessage,
  onImageError,
  getOptionImageError,
}) => {
  return (
    <div>
      <button
        onClick={onToggle}
        className="flex h-[48px] items-center gap-3 rounded-[10px] bg-white p-2 focus:border-transparent focus:outline-none focus:ring-2 focus:ring-primary md:w-fit border border-gray-200"
      >
        {currentItem && currentItem.icon ? (
          <img
            src={currentItem?.icon || ''}
            alt={currentItem?.name || placeholder}
            className="object-cover w-6 h-6 rounded-lg sm:h-8 sm:w-8 bg-peachTwo"
            onError={onImageError}
          />
        ) : (
          <div className="w-8 h-8 rounded-lg bg-peachTwo" />
        )}
        <span className="text-sm font-semibold text-blackOne">
          {currentItem?.name || placeholder}
        </span>
        {isOpen ? (
          <ChevronUp className="w-4 h-4 text-gray-500" />
        ) : (
          <ChevronDown className="w-4 h-4 text-gray-500" />
        )}
      </button>

      {isOpen && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -10 }}
          className="absolute left-0 top-full z-50 mt-1 w-64 rounded-xl border border-gray-200 bg-white shadow-[0px_8px_16px_-2px_#1B212C1F]"
        >
          <div className="py-1">
            {options.length > 1 ? (
              options
                .filter((option) => option.id !== currentItem?.id)
                .map((option) => (
                  <button
                    key={option.id}
                    onClick={() => onItemSelect(option)}
                    className="flex gap-2 items-center px-4 py-2 w-full text-sm font-medium text-left transition-colors hover:bg-gray-50 sm:text-base"
                  >
                    <img
                      src={option.icon}
                      alt={option.name}
                      className="object-cover w-6 h-6 rounded-lg sm:h-8 sm:w-8 bg-peachTwo"
                      onError={getOptionImageError?.(option)}
                    />
                    {option.name}
                  </button>
                ))
            ) : (
              <div className="px-4 py-2 text-sm text-gray-500">
                {noOptionsMessage}
              </div>
            )}
          </div>
        </motion.div>
      )}
    </div>
  );
};

export default AgentsDropdown;
