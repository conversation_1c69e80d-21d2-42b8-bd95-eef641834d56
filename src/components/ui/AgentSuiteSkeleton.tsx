import React from 'react';

const AgentSuiteSkeleton: React.FC = () => {
  return (
    <div className="flex cursor-pointer flex-col items-center overflow-hidden rounded-xl border">
      <div className="w-[290px]">
        {/* Image skeleton */}
        <div className="h-56 w-full animate-pulse bg-gray-200"></div>
        <div className="flex flex-col gap-4 p-4">
          {/* Agent name badge skeleton */}
          <div className="h-6 w-20 animate-pulse rounded bg-gray-200"></div>
          {/* Description skeleton */}
          <div className="h-6 w-full animate-pulse rounded bg-gray-200"></div>
          {/* Role description skeleton - multiple lines */}
          <div className="space-y-2">
            <div className="h-4 w-full animate-pulse rounded bg-gray-200"></div>
            <div className="h-4 w-3/4 animate-pulse rounded bg-gray-200"></div>
          </div>
        </div>
      </div>
    </div>
  );
};

const AgentSuiteSkeletonLoader: React.FC<{ count?: number }> = ({
  count = 2,
}) => {
  return (
    <div className="flex flex-wrap justify-start gap-8">
      {Array.from({ length: count }, (_, index) => (
        <AgentSuiteSkeleton key={`skeleton-${index}`} />
      ))}
    </div>
  );
};

export { AgentSuiteSkeleton, AgentSuiteSkeletonLoader };
export default AgentSuiteSkeletonLoader;
