import { Link } from "react-router-dom";
import { <PERSON>ousLogo } from "../../assets/images";
import { ROUTES } from "../../constants/routes";

const Logo = () => {
  return (
    <Link to={ROUTES.HOME}>
      <div className="flex items-center space-x-2">
        <img
          className="max-w-[120px] sm:max-w-[150px] h-auto"
          loading="lazy"
          src={AgentousLogo}
          alt="Agentous Logo"
        />

        <div className="border flex items-center justify-center border-grayNine rounded text-grayNine text-xs font-medium h-[21px] w-[38px] -mt-[0.75px] font-spartan pt-[2px]">
          Beta
        </div>
      </div>
    </Link>
  );
};

export default Logo;
