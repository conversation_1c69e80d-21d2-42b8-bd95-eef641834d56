import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { createPortal } from 'react-dom';
import {
  MoreVertical,
  Edit,
  Trash2,
  Eye,
  Download,
  FileText,
} from 'lucide-react';
import classNames from 'classnames';

interface ActionDropdownProps {
  actions?: Array<{
    label: string;
    icon: React.ReactNode;
    onClick: () => void;
    variant?: 'default' | 'danger' | 'success' | 'info';
    isDisabled?: boolean;
    isLoading?: boolean;
    closeOnClick?: boolean | undefined;
  }>;
}

const ActionDropdown: React.FC<ActionDropdownProps> = ({ actions }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [position, setPosition] = useState({ top: 0, left: 0 });
  const dropdownRef = useRef<HTMLDivElement>(null);
  const buttonRef = useRef<HTMLButtonElement>(null);

  const defaultActions = [
    {
      label: 'View',
      icon: <Eye className="w-4 h-4" />,
      onClick: () => console.log('View'),
      variant: 'default' as const,
      isDisabled: false,
      isLoading: false,
      closeOnClick: true,
    },
    {
      label: 'Replace',
      icon: <Download className="w-4 h-4" />,
      onClick: () => console.log('Replace'),
      variant: 'default' as const,
      isDisabled: false,
      isLoading: false,
      closeOnClick: true,
    },
    {
      label: 'Edit',
      icon: <Edit className="w-4 h-4" />,
      onClick: () => console.log('Edit'),
      variant: 'default' as const,
      isDisabled: false,
      isLoading: false,
      closeOnClick: true,
    },
    {
      label: 'Delete',
      icon: <Trash2 className="w-4 h-4" />,
      onClick: () => console.log('Delete'),
      variant: 'danger' as const,
      isDisabled: false,
      isLoading: false,
      closeOnClick: true,
    },
    {
      label: 'Audit Log',
      icon: <FileText className="w-4 h-4" />,
      onClick: () => console.log('Audit Log'),
      variant: 'default' as const,
      isDisabled: false,
      isLoading: false,
      closeOnClick: true,
    },
  ];

  const menuActions = actions || defaultActions;

  // Update position when opened
  const updatePosition = () => {
    if (buttonRef.current) {
      const rect = buttonRef.current.getBoundingClientRect();
      setPosition({
        top: rect.bottom + window.scrollY + 4,
        left: rect.right + window.scrollX - 192, // 192px = w-48
      });
    }
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node) &&
        buttonRef.current &&
        !buttonRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      updatePosition();
      document.addEventListener('mousedown', handleClickOutside);
      window.addEventListener('resize', updatePosition);
      window.addEventListener('scroll', updatePosition);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      window.removeEventListener('resize', updatePosition);
      window.removeEventListener('scroll', updatePosition);
    };
  }, [isOpen]);

  const dropdownPortal = isOpen ? (
    <AnimatePresence>
      <motion.div
        ref={dropdownRef}
        initial={{ opacity: 0, scale: 0.95, y: -10 }}
        animate={{ opacity: 1, scale: 1, y: 0 }}
        exit={{ opacity: 0, scale: 0.95, y: -10 }}
        transition={{ duration: 0.1 }}
        className="fixed z-[999999] w-48 rounded-xl border border-gray-200 bg-white shadow-[0px_5px_40px_0px_#0000001A]"
        style={{
          top: position.top,
          left: position.left,
        }}
      >
        <div className="py-1">
          {menuActions.map((action, index) => {
            const closeOnClick =
              action.closeOnClick === undefined ? true : action.closeOnClick;
            return (
              <button
                key={index}
                onClick={() => {
                  action.onClick();
                  closeOnClick && setIsOpen(false);
                }}
                disabled={action.isDisabled}
                className={classNames(
                  'flex w-full items-center px-4 py-2 text-sm transition-colors duration-150',
                  {
                    'text-red-600 hover:bg-red-50': action.variant === 'danger',
                    'text-greenOne bg-greenFade': action.variant === 'success',
                    'text-blue-600 hover:bg-blue-50': action.variant === 'info',
                    'text-subText hover:bg-[#FFECE3]':
                      action.variant === 'default',
                    'opacity-50 cursor-not-allowed': action.isDisabled,
                  }
                )}
              >
                {action.icon}
                <span className="ml-2">{action.label}</span>
              </button>
            );
          })}
        </div>
      </motion.div>
    </AnimatePresence>
  ) : null;

  return (
    <>
      <button
        ref={buttonRef}
        onClick={() => setIsOpen(!isOpen)}
        className="p-2 rounded-xl transition-colors duration-150 hover:bg-gray-100 hover:text-gray-600"
      >
        <MoreVertical className="w-5 h-5 text-grayTen" strokeWidth={3} />
      </button>

      {typeof document !== 'undefined' &&
        createPortal(dropdownPortal, document.body)}
    </>
  );
};

export default ActionDropdown;
