import { useMemo, useEffect, MutableRefObject } from "react";
import { EnhancedScyraChatInterface } from "../chat/EnhancedScyraChatInterface";
import { ChatInput } from "../chat/ChatInput";
import { eclipse, halfEclipse } from "../../assets/images";
import { useScyraChat } from "../../hooks/useScyraChat";
import { ConnectionFlowContext } from "../../types/businessStack";

interface EnhancedChatSidebarProps {
  connectionFlow?: ConnectionFlowContext;
  reloadChatHistoryRef?: MutableRefObject<(() => Promise<void>) | null>;
  externalMessage?: string;
  className?: string;
}

const EnhancedChatSidebar = ({
  connectionFlow,
  reloadChatHistoryRef,
  externalMessage,
  className,
}: EnhancedChatSidebarProps) => {
  const {
    state: chatState,
    sendMessage,
    groupMessagesByDate,
    reloadChatHistory,
  } = useScyraChat(externalMessage);
  const groupedMessages = groupMessagesByDate();

  // Expose reloadChatHistory to parent component
  useEffect(() => {
    if (reloadChatHistoryRef) {
      reloadChatHistoryRef.current = reloadChatHistory;
    }
  }, [reloadChatHistoryRef, reloadChatHistory]);

  // Create a chat input component that can be enhanced
  const ChatInputComponent = useMemo(() => {
    const EnhancedChatInput = (props: any) => (
      <ChatInput
        onSendMessage={props.onSendMessage || sendMessage}
        placeholder={props.placeholder || "I'm here — whenever you're ready."}
        disabled={props.disabled || chatState.isLoading}
        {...props}
      />
    );
    return EnhancedChatInput;
  }, [sendMessage, chatState.isLoading]);

  return (
    <div
      className={`flex relative flex-col px-6 py-4 bg-gradient-to-br to-orange-100 h-[95vh] from-orange-50/50 md:h-full md:w-1/3 md:min-w-[400px] md:max-w-[500px] flex-shrink-0 ${className}`}
    >
      {/* Enhanced Scyra Chat Interface */}
      <div className="flex overflow-hidden z-10 flex-col flex-1 h-full bg-white bg-opacity-40 rounded-2xl min-h-0">
        <EnhancedScyraChatInterface
          state={chatState}
          ChatInputComponent={ChatInputComponent}
          groupedMessages={groupedMessages}
          connectionFlow={connectionFlow}
          originalSendMessage={sendMessage}
        />
      </div>

      {/* Background Objects */}
      <div
        className="pointer-events-none absolute inset-0 z-0 -mt-48 bg-[right] bg-no-repeat"
        style={{
          backgroundImage: `url(${halfEclipse})`,
          backgroundSize: 'auto',
        }}
      />
      <div
        className="pointer-events-none absolute inset-0 z-0 bg-[right_bottom] bg-no-repeat"
        style={{
          backgroundImage: `url(${eclipse})`,
          backgroundSize: 'auto',
        }}
      />
    </div>
  );
};

export default EnhancedChatSidebar;
