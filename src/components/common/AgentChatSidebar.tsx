import { useMemo } from 'react';
import { ChatInput } from '../chat/ChatInput';
import { eclipse, halfEclipse } from '../../assets/images';
import { AgentChatInterface } from '../chat/AgentChatInterface';
import { IndividualAgent } from '../../data/constants';

interface AgentChatSidebarProps {
  agent: IndividualAgent;
  state: any; // Chat state
  sendMessage: (message: string) => void;
  groupedMessages: any;
}

const AgentChatSidebar = ({
  agent,
  state,
  sendMessage,
  groupedMessages,
}: AgentChatSidebarProps) => {
  // Memoize the chat input component to prevent recreation on every render
  const ChatInputComponent = useMemo(() => {
    return () => (
      <ChatInput
        onSendMessage={sendMessage}
        placeholder="I'm here — whenever you're ready."
        disabled={state.isLoading}
      />
    );
  }, [sendMessage, state.isLoading]);

  return (
    <div className="relative mx-auto flex h-full w-full max-w-[486px] flex-shrink-0 flex-col bg-gradient-to-br from-orange-50/50 to-orange-100 px-6 py-4">
      {/* Agent Profile */}
      <div className="z-10 mb-4 hidden items-center justify-center gap-3">
        <div className="h-12 w-12 flex-shrink-0 rounded-full bg-white p-1">
          <img
            src={agent.image}
            alt={agent.name}
            className="h-full w-full rounded-full object-cover"
          />
        </div>
        <div>
          <h3 className="font-semibold text-blackOne">{agent.name}</h3>
          <p className="text-sm text-gray-600">{agent.role}</p>
        </div>
      </div>

      {/* Agent Chat Interface */}
      <div className="z-10 flex-1 overflow-hidden rounded-2xl bg-white bg-opacity-40">
        <AgentChatInterface
          agent={agent}
          state={state}
          ChatInputComponent={ChatInputComponent}
          groupedMessages={groupedMessages}
        />
      </div>

      {/* Background Objects */}
      <div
        className="pointer-events-none absolute inset-0 z-0 -mt-48 bg-[right] bg-no-repeat"
        style={{
          backgroundImage: `url(${halfEclipse})`,
          backgroundSize: 'auto',
        }}
      />
      <div
        className="pointer-events-none absolute inset-0 z-0 bg-[right_bottom] bg-no-repeat"
        style={{
          backgroundImage: `url(${eclipse})`,
          backgroundSize: 'auto',
        }}
      />
    </div>
  );
};

export default AgentChatSidebar;
