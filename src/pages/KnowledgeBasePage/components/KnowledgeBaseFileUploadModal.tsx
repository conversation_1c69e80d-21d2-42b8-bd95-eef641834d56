import React, { useCallback, useState } from 'react';
import {
  CheckC<PERSON>cle,
  Trash2,
  X,
  XCircle,
  FileText,
  File,
} from 'lucide-react';
import AnimatedModal from '@/components/common/AnimatedModal';
import { Icons } from '@/assets/icons/DashboardIcons';

interface KnowledgeBaseFileUploadModalProps {
  isOpen: boolean;
  onClose: () => void;
  onFilesUpload: (files: File[]) => void;
  title: string;
  multiple?: boolean;
}

// Knowledge base specific file validation
const ALLOWED_FILE_TYPES = ['.pdf', '.doc', '.docx'];
const ALLOWED_MIME_TYPES = [
  'application/pdf',
  'application/msword',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
];
const MAX_FILE_SIZE = 25 * 1024 * 1024; // 25MB in bytes

const validateKnowledgeBaseFile = (file: File): string | null => {
  // Check file type
  const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase();
  const isValidType = ALLOWED_FILE_TYPES.includes(fileExtension) || 
                     ALLOWED_MIME_TYPES.includes(file.type);
  
  if (!isValidType) {
    return 'Only PDF and Word documents (.pdf, .doc, .docx) are allowed for knowledge base uploads.';
  }

  // Check file size
  if (file.size > MAX_FILE_SIZE) {
    return 'File size must be less than 25MB for knowledge base uploads.';
  }

  return null;
};

const KnowledgeBaseFileUploadModal: React.FC<KnowledgeBaseFileUploadModalProps> = ({
  isOpen,
  onClose,
  title,
  onFilesUpload,
  multiple = false,
}) => {
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
  const [uploadedFiles] = useState<string[]>([]);
  const [uploadingFiles] = useState<{ name: string; progress: number }[]>([]);
  const [errorFiles, setErrorFiles] = useState<string[]>([]);
  const [dragActive, setDragActive] = useState<boolean>(false);
  const [validationErrors, setValidationErrors] = useState<{ [key: string]: string }>({});

  const handleFileSelect = useCallback(
    (files: File[]) => {
      const newValidationErrors: { [key: string]: string } = {};
      const validFiles: File[] = [];

      files.forEach((file) => {
        const error = validateKnowledgeBaseFile(file);
        if (error) {
          newValidationErrors[file.name] = error;
        } else {
          validFiles.push(file);
        }
      });

      setValidationErrors(newValidationErrors);
      
      if (validFiles.length > 0) {
        setSelectedFiles((prevFiles) =>
          multiple ? [...prevFiles, ...validFiles] : validFiles.slice(0, 1)
        );
      }
    },
    [multiple]
  );

  const handleFileDrop = useCallback(
    (e: React.DragEvent<HTMLDivElement>) => {
      e.preventDefault();
      setDragActive(false);
      const droppedFiles = Array.from(e.dataTransfer.files);
      handleFileSelect(droppedFiles);
    },
    [handleFileSelect]
  );

  const handleBrowseFiles = useCallback(() => {
    const input = document.createElement("input");
    input.type = "file";
    input.multiple = multiple;
    input.accept = ".pdf,.doc,.docx";
    input.onchange = (e) => {
      const files = Array.from((e.target as HTMLInputElement).files || []);
      handleFileSelect(files);
    };
    input.click();
  }, [handleFileSelect, multiple]);

  const handleUpload = useCallback(() => {
    if (selectedFiles.length > 0) {
      onFilesUpload(selectedFiles);
      setSelectedFiles([]);
      setValidationErrors({});
      onClose();
    }
  }, [selectedFiles, onFilesUpload, onClose]);

  const removeFile = useCallback((index: number) => {
    setSelectedFiles(prevFiles => prevFiles.filter((_, i) => i !== index));
  }, []);

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getFileIcon = (fileName: string) => {
    const extension = fileName.split('.').pop()?.toLowerCase();
    switch (extension) {
      case 'pdf':
        return <FileText className="h-6 w-6 text-red-500" />;
      case 'doc':
      case 'docx':
        return <File className="h-6 w-6 text-blue-500" />;
      default:
        return <File className="h-6 w-6 text-gray-500" />;
    }
  };

  const getFileType = (fileName: string): string => {
    const extension = fileName.split('.').pop()?.toLowerCase();
    return extension?.toUpperCase() || 'FILE';
  };

  const countPdfPages = async (file: File): Promise<number> => {
    if (getFileType(file.name) !== 'PDF') {
      return 0;
    }

    try {
      // For demo purposes, return a simulated page count
      // In a real implementation, you'd use a PDF parsing library
      const sizeInMB = file.size / (1024 * 1024);
      // Rough estimate: ~1 page per 100KB for typical PDFs
      return Math.max(1, Math.round(sizeInMB * 10));
    } catch (error) {
      console.error('Error counting PDF pages:', error);
      return 0;
    }
  };

  const getFileInfo = async (file: File): Promise<string> => {
    const fileType = getFileType(file.name);
    const size = formatFileSize(file.size);

    if (fileType === 'PDF') {
      const pages = await countPdfPages(file);
      return `${pages} pages • ${size} • ${fileType}`;
    }

    return `${size} • ${fileType}`;
  };

  const [fileInfos, setFileInfos] = useState<{ [key: string]: string }>({});

  React.useEffect(() => {
    selectedFiles.forEach(async (file) => {
      const info = await getFileInfo(file);
      setFileInfos(prev => ({ ...prev, [file.name]: info }));
    });
  }, [selectedFiles]);

  const handleClose = () => {
    setSelectedFiles([]);
    setValidationErrors({});
    setFileInfos({});
    onClose();
  };

  return (
    <AnimatedModal
      isOpen={isOpen}
      onClose={handleClose}
      showCloseButton={false}
      maxWidth="xl"
    >
      <div className="mx-auto w-full space-y-6 py-10 md:w-[445px]">
        <div className="flex flex-col gap-6 justify-center items-center text-center">
          <div className="flex items-center">
            <h3 className="text-[32px] font-bold text-black">{title}</h3>
          </div>
          <div>
            <h3 className="mb-6 text-[22px] font-bold text-black">Upload</h3>

            {/* Drag & Drop Area */}
            <div
              className={`relative w-full rounded-md border-2 border-dashed p-12 transition-colors ${
                dragActive
                  ? 'border-primary bg-primary/5'
                  : 'border-[#384EB74D] bg-[#F8F8FF]'
              }`}
              onDragEnter={(e) => {
                e.preventDefault();
                setDragActive(true);
              }}
              onDragLeave={(e) => {
                e.preventDefault();
                setDragActive(false);
              }}
              onDragOver={(e) => e.preventDefault()}
              onDrop={handleFileDrop}
            >
              <div className="flex flex-col items-center">
                <Icons.Upload className="mb-6 h-[59.58695602416992px] w-[68.78290557861328px]" />
                <p className="mb-2 text-base text-black">
                  Drag & drop {multiple ? "files" : "a file"} or{" "}
                  <button
                    className="font-semibold underline text-primary hover:text-primary/80"
                    onClick={handleBrowseFiles}
                  >
                    Browse
                  </button>
                </p>
                <p className="text-sm text-grayTen">
                  Supported formats: PDF, Word documents (.pdf, .doc, .docx)
                </p>
                <p className="text-xs text-gray-500 mt-1">
                  Maximum file size: 25MB
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Validation Errors */}
        {Object.keys(validationErrors).length > 0 && (
          <div className="space-y-2">
            {Object.entries(validationErrors).map(([fileName, error]) => (
              <div key={fileName} className="flex items-center gap-2 p-3 bg-red-50 border border-red-200 rounded-md">
                <XCircle className="h-5 w-5 text-red-500 flex-shrink-0" />
                <div className="flex-1">
                  <p className="text-sm font-medium text-red-800">{fileName}</p>
                  <p className="text-xs text-red-600">{error}</p>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Selected Files */}
        {selectedFiles.length > 0 && (
          <div className="space-y-3">
            <h4 className="text-lg font-semibold">Selected Files</h4>
            {selectedFiles.map((file, index) => (
              <div key={index} className="flex items-center justify-between rounded-lg border p-3">
                <div className="flex items-center gap-3">
                  {getFileIcon(file.name)}
                  <div>
                    <p className="font-medium">{file.name}</p>
                    <p className="text-sm text-gray-500">
                      {fileInfos[file.name] || 'Loading...'}
                    </p>
                  </div>
                </div>
                <button
                  onClick={() => removeFile(index)}
                  className="rounded-full p-1 text-gray-400 hover:bg-gray-100 hover:text-gray-600"
                >
                  <Trash2 className="h-4 w-4" />
                </button>
              </div>
            ))}
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex gap-3 justify-center">
          <button
            onClick={handleClose}
            className="rounded-md border border-gray-300 px-6 py-2 text-gray-700 hover:bg-gray-50"
          >
            Cancel
          </button>
          <button
            onClick={handleUpload}
            disabled={selectedFiles.length === 0}
            className="rounded-md bg-primary px-6 py-2 text-white hover:bg-primary/90 disabled:bg-gray-300 disabled:cursor-not-allowed"
          >
            Upload {selectedFiles.length > 0 && `(${selectedFiles.length})`}
          </button>
        </div>
      </div>
    </AnimatedModal>
  );
};

export default KnowledgeBaseFileUploadModal;
