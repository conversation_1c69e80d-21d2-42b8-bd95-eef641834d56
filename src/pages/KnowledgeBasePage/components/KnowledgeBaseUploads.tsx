import clsx from 'clsx';
import React, { useEffect, useRef, useState } from 'react';

import { alertLine, cloudSyncComplete, gotoDoc, upload } from '@/assets/icons';
import { kbCurlLines } from '@/assets/images';
import AgentsDropdown from '@/components/ui/AgentsDropdown';
import { kbIcons } from '@/data/constants';
import { useOnClickOutside } from '@/hooks/useOnClickOutside';

import { useTenant } from '../../../context/TenantContext';
import { useGetUserProfile } from '../../../hooks/useUserProfile';
import {
  KnowledgeBaseResponse,
  useAgentKnowledgeBaseApi,
  useSuiteKnowledgeBaseApi,
} from '../../../services/knowledgeBaseService';
import { UserBasicInfoPayload } from '../../../types/user';
import KnowledgeBaseFileUploadModal from './KnowledgeBaseFileUploadModal';
import { UploadLevel } from './LevelSelector';

interface KnowledgeBaseDocument {
  iconUrl: string;
  name: string;
  key: string;
  fileRef?: string;
  description: string;
  hasFile?: boolean;
  fileName?: string;
  url?: string;
  size?: string;
  createdAt?: string;
}

interface KnowledgeBaseUploadsProps {
  selectedLevel: UploadLevel;
  onLevelSelect: (level: UploadLevel) => void;
  onDocumentSelect: (document: KnowledgeBaseDocument, index?: number) => void;
  onBack: () => void;
  onDocumentUpload?: (documentKey: string, files: File[]) => Promise<void>;
  onDocumentReplace?: (documentKey: string, files: File[]) => Promise<void>;
  onRefreshDocuments?: () => void;
}

const kbTabs = { suite: 'Suite Knowledge Base', agent: 'Agent Knowledge Base' };

const KnowledgeBaseUploads: React.FC<KnowledgeBaseUploadsProps> = ({
  selectedLevel,
  onLevelSelect,
  onDocumentSelect,
  // onBack,
  onDocumentUpload,
  onDocumentReplace,
  onRefreshDocuments,
}) => {
  const [activeTab, setActiveTab] = useState<UploadLevel>(selectedLevel);
  const [selectedSuite, setSelectedSuite] = useState<string>('');
  const [selectedAgent, setSelectedAgent] = useState<string>('');
  const [isSuiteDropdownOpen, setIsSuiteDropdownOpen] = useState(false);
  const [isAgentDropdownOpen, setIsAgentDropdownOpen] = useState(false);
  const [uploadModalOpen, setUploadModalOpen] = useState(false);
  const [selectedDocument, setSelectedDocument] =
    useState<KnowledgeBaseDocument | null>(null);
  const [isLoadingDocuments, setIsLoadingDocuments] = useState(false);
  const [uploadedDocuments, setUploadedDocuments] = useState<{
    [key: string]: any;
  }>({});
  const [uploadingDocuments, setUploadingDocuments] = useState<{
    [key: string]: boolean;
  }>({});

  const { data: userData } = useGetUserProfile<UserBasicInfoPayload>();
  const { activeAgent, setActiveAgent } = useTenant();

  // Initialize API hooks
  const suiteKnowledgeBaseApi = useSuiteKnowledgeBaseApi();
  const agentKnowledgeBaseApi = useAgentKnowledgeBaseApi();

  // Get suite options from user data
  const suiteOptions =
    userData?.userInfo?.tenant?.claimedAgentSuites?.map(suite => ({
      id: suite.suite.agentSuiteKey,
      name: suite.suite.agentSuiteName,
      icon: suite.suite.avatar,
      fileData: suite.suite.fileData,
    })) || [];

  // Get all agents from all suites
  const allAgents =
    userData?.userInfo?.tenant?.claimedAgentSuites?.flatMap(suite =>
      suite.suite.availableAgents.map(agent => ({
        ...agent,
        suiteKey: suite.suite.agentSuiteKey,
      }))
    ) || [];

  // Fetch existing documents from API
  const fetchExistingDocuments = async () => {
    if (!activeTab) return;

    setIsLoadingDocuments(true);
    try {
      let response: KnowledgeBaseResponse;

      if (activeTab === 'suite' && selectedSuite) {
        response =
          await suiteKnowledgeBaseApi.checkSuiteKnowledgeBase(selectedSuite);
      } else if (activeTab === 'agent' && selectedAgent) {
        response =
          await agentKnowledgeBaseApi.checkAgentKnowledgeBase(selectedAgent);
      } else {
        return;
      }

      if (response.status && response.data?.knowledgeBaseFiles) {
        const documentsMap: { [key: string]: any } = {};
        response.data.knowledgeBaseFiles.forEach((file: any) => {
          documentsMap[file.fileTag] = file;
        });
        setUploadedDocuments(documentsMap);
      } else {
        setUploadedDocuments({});
      }
    } catch (error) {
      console.error('Error fetching existing documents:', error);
      setUploadedDocuments({});
    } finally {
      setIsLoadingDocuments(false);
    }
  };

  // Get agent documents based on selected agent
  const getAgentDocuments = (): KnowledgeBaseDocument[] => {
    if (!selectedAgent) return [];

    const agent = allAgents.find(a => a.agentKey === selectedAgent);
    if (!agent?.fileData) return [];

    return agent.fileData.map(file => ({
      iconUrl: file.iconUrl,
      name: file.name,
      key: file.key,
      description: file.description,
      hasFile: !!uploadedDocuments[file.key],
      fileName: uploadedDocuments[file.key]?.name,
      fileRef: uploadedDocuments[file.key]?.fileRef,
      url: uploadedDocuments[file.key]?.url,
      size: uploadedDocuments[file.key]?.size,
      createdAt: uploadedDocuments[file.key]?.createdAt,
    }));
  };

  // Get suite documents with uploaded status
  const getSuiteDocuments = (): KnowledgeBaseDocument[] => {
    if (!selectedSuite) return [];

    const suite = suiteOptions.find(s => s.id === selectedSuite);
    if (!suite?.fileData) return [];

    return suite.fileData.map(file => ({
      iconUrl: file.iconUrl,
      name: file.name,
      key: file.key,
      description: file.description,
      hasFile: !!uploadedDocuments[file.key],
      fileName: uploadedDocuments[file.key]?.name,
      fileRef: uploadedDocuments[file.key]?.fileRef,
      url: uploadedDocuments[file.key]?.url,
      size: uploadedDocuments[file.key]?.size,
      createdAt: uploadedDocuments[file.key]?.createdAt,
    }));
  };

  // Initialize selections
  useEffect(() => {
    if (suiteOptions.length > 0 && !selectedSuite) {
      setSelectedSuite(suiteOptions[0].id);
    }
    if (allAgents.length > 0 && !selectedAgent) {
      setSelectedAgent(activeAgent || allAgents[0].agentKey);
    }
  }, [suiteOptions, allAgents, selectedSuite, selectedAgent]);

  // Fetch documents when component mounts or selections change
  useEffect(() => {
    if (activeTab === 'suite' && selectedSuite) {
      fetchExistingDocuments();
    } else if (activeTab === 'agent' && selectedAgent) {
      fetchExistingDocuments();
    }
  }, [activeTab, selectedSuite, selectedAgent]);

  // Fetch documents when tab changes
  useEffect(() => {
    fetchExistingDocuments();
  }, [activeTab]);

  const suiteDropdownRef = useRef<HTMLDivElement>(null);
  const agentDropdownRef = useRef<HTMLDivElement>(null);
  useOnClickOutside(suiteDropdownRef, () => setIsSuiteDropdownOpen(false));
  useOnClickOutside(agentDropdownRef, () => setIsAgentDropdownOpen(false));

  const handleTabLevelChange = (level: UploadLevel) => {
    onLevelSelect(level);
    setActiveTab(level);
  };

  // Handle agent selection change
  const handleAgentChange = (agentKey: string) => {
    setSelectedAgent(agentKey);
    setActiveAgent(agentKey);
    setIsAgentDropdownOpen(false);
  };

  const handleDocumentCardClick = (
    doc: KnowledgeBaseDocument,
    index?: number
  ) => {
    onDocumentSelect(doc, index);
  };

  const handleUploadClick = (doc: KnowledgeBaseDocument) => {
    setSelectedDocument(doc);
    setUploadModalOpen(true);
  };

  const handleFilesUpload = async (files: File[]) => {
    if (!selectedDocument) return;

    // Set loading state for this document
    setUploadingDocuments(prev => ({
      ...prev,
      [selectedDocument.key]: true,
    }));

    try {
      // Close modal immediately to show loading state
      setUploadModalOpen(false);

      // Await the upload/replace operation
      if (selectedDocument?.hasFile && selectedDocument.fileRef) {
        if (onDocumentReplace && selectedDocument.fileRef) {
          await onDocumentReplace(selectedDocument.fileRef, files);
        }
      } else if (onDocumentUpload && selectedDocument) {
        await onDocumentUpload(selectedDocument.key, files);
      }

      // Refresh documents after successful upload
      await fetchExistingDocuments();
      onRefreshDocuments?.();
    } catch (error) {
      console.error('Upload failed:', error);
    } finally {
      // Clear loading state for this document
      setUploadingDocuments(prev => ({
        ...prev,
        [selectedDocument.key]: false,
      }));
    }
  };

  const renderDocumentCard = (doc: KnowledgeBaseDocument, index: number) => (
    <div
      key={doc.key}
      onClick={() => handleDocumentCardClick(doc, index)}
      className="relative h-[360px] w-[312px] cursor-pointer rounded-xl border border-grayTwentyThree px-4 py-6 transition-shadow hover:shadow-md"
    >
      <div
        className="h-[124px] rounded-2xl bg-blue-midnight p-4"
        style={{
          backgroundImage: `url(${kbCurlLines})`,
          backgroundSize: 'cover',
          backgroundPosition: 'center',
        }}
      >
        <img
          className="h-8 w-8"
          src={doc.iconUrl}
          alt={doc.name}
          onError={e => {
            (e.target as HTMLImageElement).src =
              kbIcons[index % kbIcons.length];
          }}
        />

        <h3 className="mt-4 font-spartan text-base font-semibold text-white">
          {doc.name}
        </h3>
      </div>
      <div className="mt-4 text-sm text-subText">{doc.description}</div>
      <div className="absolute bottom-[86px] flex items-center gap-3 text-sm text-subText">
        Status:
        <div className="flex items-center gap-2 rounded bg-[#EEF7FE] px-4 py-1">
          <img src={doc.hasFile ? cloudSyncComplete : alertLine} alt="status" />
          <span>{doc.hasFile ? 'Completed' : 'Missing'}</span>
        </div>
      </div>
      {doc.hasFile ? (
        <div className="absolute bottom-6 flex w-[280px] flex-col gap-2">
          <button
            onClick={() => {
              handleDocumentCardClick(doc, index);
            }}
            className="upload-button flex h-10 w-full items-center justify-center gap-2 rounded-full border border-primary px-3.5 py-1.5 text-sm font-medium text-primary transition-colors hover:border-primary hover:bg-lightOrangeTwo"
          >
            <img src={gotoDoc} alt="View Document" />
            View Document
          </button>
        </div>
      ) : (
        <button
          onClick={e => {
            e.stopPropagation();
            handleUploadClick(doc);
          }}
          disabled={uploadingDocuments[doc.key]}
          className={`upload-button absolute bottom-6 flex h-10 w-[280px] items-center justify-center gap-2 rounded-full px-3.5 py-1.5 text-sm font-medium transition-colors ${
            uploadingDocuments[doc.key]
              ? 'cursor-not-allowed bg-gray-400 text-gray-600'
              : 'bg-primary text-white hover:bg-orange-15'
          }`}
        >
          {uploadingDocuments[doc.key] ? (
            <>
              <div className="h-4 w-4 animate-spin rounded-full border-b-2 border-white"></div>
              Uploading...
            </>
          ) : (
            <>
              <img src={upload} alt="Upload Document" />
              Upload Document
            </>
          )}
        </button>
      )}
    </div>
  );

  return (
    <div className="space-y-6">
      {/* Back Button */}
      {/* <div className="flex justify-between items-center">
        <button
          onClick={onBack}
          className="flex gap-2 items-center transition-colors text-subText hover:text-blackOne"
        >
          ← Back
        </button>
        <div />
      </div> */}

      {/* Tab Selectors with Dropdowns */}
      <div className="flex items-center justify-between md:w-[648px]">
        <div className="flex w-fit space-x-1 border-b border-gray-200">
          {(Object.keys(kbTabs) as UploadLevel[]).map(tab => (
            <button
              key={tab}
              onClick={() => handleTabLevelChange(tab)}
              className={clsx(
                'px-4 py-2 text-sm font-medium transition-colors',
                activeTab === tab
                  ? 'border-b-2 border-primary text-primary'
                  : 'text-gray-600 hover:text-blackOne'
              )}
            >
              {kbTabs[tab]}
            </button>
          ))}
        </div>

        {/* Dropdowns */}
        <div className="flex gap-4">
          {activeTab === 'suite' && (
            <div className="relative" ref={suiteDropdownRef}>
              <AgentsDropdown
                isOpen={isSuiteDropdownOpen}
                onToggle={() => setIsSuiteDropdownOpen(!isSuiteDropdownOpen)}
                currentItem={suiteOptions.find(s => s.id === selectedSuite)}
                options={suiteOptions}
                onItemSelect={suite => {
                  setSelectedSuite(suite.id);
                  setIsSuiteDropdownOpen(false);
                }}
                placeholder="Suite"
                noOptionsMessage="No other suites available"
              />
            </div>
          )}

          {activeTab === 'agent' && (
            <div className="relative" ref={agentDropdownRef}>
              <AgentsDropdown
                isOpen={isAgentDropdownOpen}
                onToggle={() => setIsAgentDropdownOpen(!isAgentDropdownOpen)}
                currentItem={allAgents
                  .map(a => ({
                    id: a.agentKey,
                    name: a.agentName,
                    icon: a.avatar,
                  }))
                  .find(a => a.id === selectedAgent)}
                options={allAgents.map(a => ({
                  id: a.agentKey,
                  name: a.agentName,
                  icon: a.avatar,
                }))}
                onItemSelect={agent => handleAgentChange(agent.id)}
                placeholder="Agent"
                noOptionsMessage="No other agents available"
              />
            </div>
          )}
        </div>
      </div>

      {/* Document Grid */}
      <div className="grid w-fit grid-cols-1 gap-6 md:grid-cols-2">
        {isLoadingDocuments
          ? // Skeleton loading placeholders
            Array.from({ length: 4 }).map((_, index) => (
              <div
                key={index}
                className="relative h-[360px] w-[312px] animate-pulse rounded-xl border border-grayTwentyThree p-6"
              >
                <div className="flex items-start gap-2">
                  <div className="h-8 w-8 rounded bg-gray-200"></div>
                  <div className="h-6 w-32 rounded bg-gray-200"></div>
                </div>
                <div className="mt-2.5 space-y-2">
                  <div className="h-4 w-full rounded bg-gray-200"></div>
                  <div className="h-4 w-3/4 rounded bg-gray-200"></div>
                </div>
                <div className="absolute bottom-6 h-9 w-[280px] rounded-[30px] bg-gray-200"></div>
              </div>
            ))
          : activeTab === 'suite'
            ? getSuiteDocuments().map((doc, index) =>
                renderDocumentCard(doc, index)
              )
            : getAgentDocuments().map((doc, index) =>
                renderDocumentCard(doc, index)
              )}
      </div>

      {/* Upload Modal */}
      <KnowledgeBaseFileUploadModal
        multiple={false}
        isOpen={uploadModalOpen}
        onClose={() => setUploadModalOpen(false)}
        title={selectedDocument?.name || 'Upload Document'}
        onFilesUpload={handleFilesUpload}
      />
    </div>
  );
};

export default KnowledgeBaseUploads;
