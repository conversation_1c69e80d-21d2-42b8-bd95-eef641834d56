import React from 'react';
import { useNavigate } from 'react-router-dom';

import { businessStackBg } from '@/assets/images';
import { useGetUserProfile } from '@/hooks/useUserProfile';

import AgentSelectionLayout from '../../../components/layout/AgentSelectionLayout';
import { ROUTES } from '../../../constants/routes';

const BusinessStackSelectAgentPage: React.FC = () => {
  const navigate = useNavigate();
  const { data: userData, isLoading } = useGetUserProfile();

  // Check if user has claimed the specific agents suite
  const isAgentSuiteClaimed = (suiteKey: string) => {
    return userData?.userInfo?.tenant?.claimedAgentSuites?.some(
      claimedSuite => claimedSuite.suite.agentSuiteKey === suiteKey
    );
  };

  return (
    <AgentSelectionLayout
      title="Seamless System Connections"
      description="Link agents to your CRM, communication tools, and workflows with secure per-agent authentication."
      bgImage={businessStackBg}
      pageType="business-stack"
      onAgentSuiteClick={suite => {
        if (!isAgentSuiteClaimed(suite.agentSuiteKey)) {
          navigate(
            ROUTES.DASHBOARD_BUSINESS_STACK_ACTIVATE_SUITE(suite.agentSuiteKey)
          );
        } else {
          navigate(ROUTES.DASHBOARD_BUSINESS_STACK);
        }
      }}
    />
  );
};

export default BusinessStackSelectAgentPage;
