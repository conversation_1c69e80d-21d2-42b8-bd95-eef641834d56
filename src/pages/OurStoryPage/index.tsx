import React, { useState } from 'react';
import { motion, useInView } from 'framer-motion';
import randyAvatar from '@/assets/images/randy-avatar.jpg';
import adamAvatar from '@/assets/images/adam-avatar.jpg';
import jean<PERSON>ouisAvatar from '@/assets/images/jean-louis-avatar.jpg';
import louisAvatar from '@/assets/images/louis-avatar.jpg';
import ourStoryImg from '@/assets/images/our-story.jpg';
import ourTransformationImg from '@/assets/images/our-transformation.webp';
import adahAvatar from '@/assets/images/adah-avatar.jpg';
import ourStoryHeroBg from '@/assets/images/our-story-hero-bg.png';

type Advisor = {
  id: string;
  name: string;
  bio: string;
  image: string; // url
};

type Founder = {
  id: string;
  name: string;
  bioParagraphs: string[];
  image: string;
};

// Animation variants
const fadeInUp = {
  hidden: { opacity: 0, y: 30 },
  visible: { 
    opacity: 1, 
    y: 0,
    transition: {
      duration: 0.6,
      ease: "easeOut"
    }
  }
};

const fadeInLeft = {
  hidden: { opacity: 0, x: -30 },
  visible: { 
    opacity: 1, 
    x: 0,
    transition: {
      duration: 0.7,
      ease: "easeOut"
    }
  }
};

const fadeInRight = {
  hidden: { opacity: 0, x: 30 },
  visible: { 
    opacity: 1, 
    x: 0,
    transition: {
      duration: 0.7,
      ease: "easeOut"
    }
  }
};

const staggerContainer = {
  hidden: {},
  visible: {
    transition: {
      staggerChildren: 0.1
    }
  }
};

// Custom hook for scroll-triggered animations
const useScrollAnimation = () => {
  const ref = React.useRef(null);
  const isInView = useInView(ref, { once: true, margin: "-50px" });
  return { ref, isInView };
};

// Custom hook for image loading
const useImageLoading = () => {
  const [loadedImages, setLoadedImages] = useState<Set<string>>(new Set());

  const handleImageLoad = (src: string) => {
    setLoadedImages((prev) => new Set(prev).add(src));
  };

  const isImageLoaded = (src: string) => loadedImages.has(src);

  return { handleImageLoad, isImageLoaded };
};

// Skeleton component
const ImageSkeleton: React.FC<{ className: string }> = ({ className }) => (
  <div
    className={`flex justify-center items-center bg-gray-200 rounded-lg animate-pulse ${className}`}
  >
    <div className="text-gray-400">
      <svg
        className="w-8 h-8 md:w-12 md:h-12"
        fill="currentColor"
        viewBox="0 0 20 20"
      >
        <path
          fillRule="evenodd"
          d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z"
          clipRule="evenodd"
        />
      </svg>
    </div>
  </div>
);

// Optimized Image component with skeleton loading
const OptimizedImage: React.FC<{
  src: string;
  alt: string;
  className: string;
  onLoad: (src: string) => void;
  isLoaded: boolean;
}> = ({ src, alt, className, onLoad, isLoaded }) => (
  <div className="relative">
    {!isLoaded && <ImageSkeleton className={className} />}
    <img
      src={src}
      alt={alt}
      className={`${className} ${!isLoaded ? 'absolute top-0 left-0 opacity-0' : 'opacity-100'} transition-opacity duration-300`}
      onLoad={() => onLoad(src)}
      loading="lazy"
    />
  </div>
);

const advisors: Advisor[] = [
  {
    id: 'randy-cohen',
    name: 'Dr. Randy Cohen',
    bio: 'Professor at Harvard Business School and co-founder of PEO Investments, Randy has taught the preeminent Field X and Field Y entrepreneurship courses for decades. These programs launched some of the most successful global startups and are regarded as the cornerstone of entrepreneurship education at HBS. A leading authority in entrepreneurial finance and liquid private equity investing, he has combined academic rigor with deep experience guiding ventures from idea to scale. He holds degrees from Harvard (Mathematics) and the University of Chicago (PhD, Finance).',
    image: randyAvatar,
  },
  {
    id: 'adam-benowitz',
    name: 'Adam Benowitz',
    bio: 'CEO and co-founder of Vox Funding, Adam built his career at the intersection of entrepreneurship, finance, and enterprise growth. He co-founded Vision Capital Advisors, an SEC-registered investment advisor to private funds, after beginning his career at Susquehanna International Group. With more than two decades of experience funding and scaling companies, Adam has brought an operator’s perspective to capital deployment and growth strategy — helping ventures turn early traction into enterprise value.',
    image: adamAvatar,
  },
  {
    id: 'jean-louis-lelogeais',
    name: 'Jean-Louis Lelogeais',
    bio: 'Co-founder of PEO Investments, Jean-Louis previously co-founded SVP Global, a $17.5B distressed debt and restructuring firm managing portfolios worth more than a trillion dollars. He began his career at Booz Allen Hamilton, rose to partner, and led wholesale banking and capital markets consulting. With advanced degrees from Ecole des Mines and MIT, Jean-Louis has repeatedly delivered enterprise transformation, value creation, and operational turnaround — while also serving as chairman of the Cerebral Palsy Alliance Research Foundation USA.',
    image: jeanLouisAvatar,
  },
  {
    id: 'lou-calderone',
    name: 'Lou Calderone',
    bio: 'President, COO, and co-founder of Vox Funding, Lou spent more than two decades at Aegis Capital, where he rose to President and scaled the firm into a premier full-service investment house. He also led companies through acquisitions, including guiding Converge Media Group to its successful sale to Zealot Networks. Lou has been known for operational rigor and enterprise-building under pressure, bringing a proven track record of growth and value creation to Agentous.',
    image: louisAvatar,
  },
];

const founders: Founder[] = [
  {
    id: 'adah-ojile',
    name: 'Adah Ojile',
    bioParagraphs: [
      'Adah Ojile has spent his career leading enterprise-scale transformation. At JP Morgan Chase, he led 300 engineers and technologists with a $50M annual budget in its enterprise-wide credit risk tech transformation initiative across the Investment Bank, Commercial Bank, and Asset & Wealth Management divisions—with a significant portion of his transformation budget dedicated to AI initiatives.',
      'He also served as Global Head of Platforms at MoneyGram International, CIO at FSX after its LexisNexis divestiture, and in lead roles at Target Corp. and Thomson Reuters, where his teams built intelligent logistics systems, applied semantic algorithms to the GNIP Twitter feed, and launched and managed platforms adopted worldwide.',
      'In 2006, Adah was among the first to scrape Wikipedia into self-funded servers, pioneering large-scale context indexing that anticipated today’s large language models. At Harvard, his graduate research project applied neural networks to RNA gene expression data to predict melanoma subtypes.',
      'Adah is an Aeronautical Engineering graduate of the U.S. Air Force Academy, an honoree of the National Honor Society for Aerospace Engineers, and holds graduate degrees from Cornell and Harvard.',
    ],
    image: adahAvatar,
  },
];

const OurStoryPage: React.FC = () => {
  const { handleImageLoad, isImageLoaded } = useImageLoading();
  return (
    <div className="w-full">
      {/* Hero */}
      <motion.section
        className="flex h-72 pt-10 sm:pt-0 sm:h-[396px] bg-cover bg-no-repeat sm:bg-contain bg-center justify-center items-center text-center"
        style={{
          background: `url(${ourStoryHeroBg})`,
        }}
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.8 }}
      >
        <motion.div 
          className="max-w-[712px] mx:px-0 px-4"
          variants={staggerContainer}
          initial="hidden"
          animate="visible"
        >
          <motion.h1 
            className="mb-4 text-[32px] sm:text-4xl font-spartan leading-[120%] sm:leading-[150%] font-semibold text-blackOne md:text-[64px]"
            variants={fadeInUp}
          >
            The New Age of Agentic Enterprises
          </motion.h1>
          <motion.p 
            className="text-sm sm:text-base font-normal text-subText md:text-lg leading-[150%] font-inter"
            variants={fadeInUp}
          >
            At Agentous, we build AI agents that autonomously run core business
            functions reducing costs, multiplying capacity, and accelerating
            growth.
          </motion.p>
        </motion.div>
      </motion.section>

      {/* Our Transformation */}
      <TransformationSection 
        handleImageLoad={handleImageLoad} 
        isImageLoaded={isImageLoaded} 
        ourStoryImg={ourStoryImg} 
      />

      {/* Our Story (two-column with header background) */}
      <OurStorySection 
        handleImageLoad={handleImageLoad} 
        isImageLoaded={isImageLoaded} 
        ourTransformationImg={ourTransformationImg} 
      />

      {/* Strategic Advisors */}
      <AdvisorsSection 
        advisors={advisors} 
        handleImageLoad={handleImageLoad} 
        isImageLoaded={isImageLoaded} 
      />

      {/* Founder */}
      <FounderSection 
        founders={founders} 
        handleImageLoad={handleImageLoad} 
        isImageLoaded={isImageLoaded} 
      />

      {/* Our Mission */}
      <MissionSection />
    </div>
  );
};

// Animated Section Components
const TransformationSection: React.FC<{
  handleImageLoad: (src: string) => void;
  isImageLoaded: (src: string) => boolean;
  ourStoryImg: string;
}> = ({ handleImageLoad, isImageLoaded, ourStoryImg }) => {
  const { ref, isInView } = useScrollAnimation();
  
  return (
    <motion.section 
      ref={ref}
      className="px-4 py-10 md:py-16"
      initial="hidden"
      animate={isInView ? "visible" : "hidden"}
      variants={fadeInUp}
    >
      <div className="mx-auto max-w-[993.16064453125px]">
        <motion.div 
          className="flex flex-col gap-6 items-center md:flex-row"
          variants={staggerContainer}
        >
          <motion.div className="flex-shrink-0 order-2 md:order-1" variants={fadeInLeft}>
            <OptimizedImage
              src={ourStoryImg}
              alt="Our Story"
              className="h-[200px] w-[200px] md:h-[260px] md:w-[233px] rounded object-contain md:object-cover mx-auto"
              onLoad={handleImageLoad}
              isLoaded={isImageLoaded(ourStoryImg)}
            />
          </motion.div>
          <motion.div className="order-1 md:order-2 md:col-span-2" variants={fadeInRight}>
            <motion.div 
              className="inline-block px-4 py-2.5 mb-3 w-full font-semibold lg:text-2xl text-white rounded bg-[#01040B]"
              variants={fadeInUp}
            >
              A World In Transformation
            </motion.div>
            <motion.div 
              className="space-y-2.5 text-sm font-normal leading-6 font-inter text-blackOne md:text-base"
              variants={staggerContainer}
            >
              <motion.p variants={fadeInUp}>
                For decades, enterprises poured billions into transformation.
                Yet most efforts yielded only incremental progress. Manual
                processes persisted, costs mounted, and silos stifled growth.
              </motion.p>
              <motion.div className="space-y-2.5" variants={staggerContainer}>
                <motion.p variants={fadeInUp}>
                  Today, a new force is breaking that cycle:{' '}
                  <span className="font-semibold">
                    the Agentic Enterprise.
                  </span>
                </motion.p>
                <motion.p variants={fadeInUp}>
                  And Agentous is leading the way. We build AI agents that
                  autonomously run core business functions with minimal
                  oversight.
                </motion.p>
                <motion.p variants={fadeInUp}>
                  These agents reduce costs, accelerate value creation,
                  multiply capacity, and exponentially enhance capabilities
                  for growth.
                </motion.p>
              </motion.div>
            </motion.div>
          </motion.div>
        </motion.div>
        <motion.div 
          className="mt-6 space-y-2.5 text-sm font-normal leading-6 font-inter text-blackOne md:text-base"
          variants={staggerContainer}
          initial="hidden"
          animate={isInView ? "visible" : "hidden"}
        >
          <motion.p variants={fadeInUp}>
            They don&apos;t just support teams — they become part of them.
            Agents reconcile accounts before issues surface, manage workflows
            in hours instead of weeks, and safeguard compliance in real time.
          </motion.p>
          <motion.p variants={fadeInUp}>
            The outcome is clear: costs fall, growth accelerates, and
            businesses scale without excess overhead. The next decade belongs
            to enterprises that place AI agents at the heart of their
            operations — unlocking 100x productivity and value creation.
          </motion.p>
        </motion.div>
      </div>
    </motion.section>
  );
};

const OurStorySection: React.FC<{
  handleImageLoad: (src: string) => void;
  isImageLoaded: (src: string) => boolean;
  ourTransformationImg: string;
}> = ({ handleImageLoad, isImageLoaded, ourTransformationImg }) => {
  const { ref, isInView } = useScrollAnimation();
  
  return (
    <motion.section 
      ref={ref}
      className="px-4 py-10 md:py-16"
      initial="hidden"
      animate={isInView ? "visible" : "hidden"}
      variants={fadeInUp}
    >
      <div className="flex flex-col md:flex-row items-center justify-center gap-6 mx-auto max-w-[993.16064453125px]">
        <motion.div className="flex flex-col" variants={fadeInLeft}>
          <motion.div 
            className="inline-block px-4 py-3 mb-4 w-full font-semibold lg:text-2xl text-white rounded bg-[#01040B]"
            variants={fadeInUp}
          >
            Our Story
          </motion.div>
          <motion.div 
            className="space-y-3 text-sm font-normal leading-6 font-inter text-blackOne md:text-base"
            variants={staggerContainer}
          >
            <motion.p variants={fadeInUp}>
              Agentous was born out of ideation on the Harvard campus, with a
              vision to build the hub for AI transformation agents —
              autonomous systems designed to break down silos, accelerate
              enterprise change, and deliver value at the speed today&apos;s
              markets demand.
            </motion.p>
            <motion.p variants={fadeInUp}>
              Our team brings deep experience from the highest levels of
              technology, finance, and operations. We have deployed AI that
              delivered measurable results inside global companies, built
              intelligent systems that scaled across industries, and created
              platforms used worldwide.
            </motion.p>
            <motion.p variants={fadeInUp}>
              Now, we are applying that expertise to the next frontier: making{' '}
              <span className="italic font-semibold">
                Agentic AI the operating core of the enterprise.
              </span>
            </motion.p>
          </motion.div>
        </motion.div>
        <motion.div className="flex-shrink-0" variants={fadeInRight}>
          <OptimizedImage
            src={ourTransformationImg}
            alt="Our Transformation"
            className="h-[200px] w-[200px] md:h-[300px] md:w-[233px] rounded object-contain md:object-cover"
            onLoad={handleImageLoad}
            isLoaded={isImageLoaded(ourTransformationImg)}
          />
        </motion.div>
      </div>
    </motion.section>
  );
};

const AdvisorsSection: React.FC<{
  advisors: Advisor[];
  handleImageLoad: (src: string) => void;
  isImageLoaded: (src: string) => boolean;
}> = ({ advisors, handleImageLoad, isImageLoaded }) => {
  const { ref, isInView } = useScrollAnimation();
  
  return (
    <motion.section 
      ref={ref}
      className="px-4 py-10 md:py-16"
      initial="hidden"
      animate={isInView ? "visible" : "hidden"}
      variants={fadeInUp}
    >
      <div className="mx-auto max-w-[993.16064453125px]">
        <motion.h2 
          className="mb-6 text-2xl sm:text-[32px] font-inter font-semibold text-blackOne md:text-3xl"
          variants={fadeInUp}
        >
          Strategic Advisors
        </motion.h2>
        <motion.div 
          className="space-y-8"
          variants={staggerContainer}
        >
          {advisors.map((advisor, index) => {
            const isEven = index % 2 === 0;
            return (
              <motion.div
                key={advisor.id}
                className={`flex flex-col gap-6 py-6 px-4 md:px-8 lg:px-12 rounded-lg items-center border border-[#DFEAF2] ${
                  isEven ? 'md:flex-row' : 'md:flex-row-reverse'
                }`}
                variants={fadeInUp}
                whileHover={{ scale: 1.02 }}
                transition={{ type: "spring", stiffness: 300 }}
              >
                <motion.div 
                  className="flex flex-shrink-0 justify-center w-full md:w-auto"
                  variants={isEven ? fadeInLeft : fadeInRight}
                >
                  <OptimizedImage
                    src={advisor.image}
                    alt={advisor.name}
                    className="h-[200px] w-[200px] md:h-[280px] md:w-[220px] lg:h-[300px] lg:w-[233px] object-contain md:object-cover rounded-lg"
                    onLoad={handleImageLoad}
                    isLoaded={isImageLoaded(advisor.image)}
                  />
                </motion.div>
                <motion.div 
                  className="flex-1 text-center md:text-left"
                  variants={isEven ? fadeInRight : fadeInLeft}
                >
                  <motion.h3 
                    className="mb-3 text-base font-semibold md:mb-4 text-blackOne lg:text-lg"
                    variants={fadeInUp}
                  >
                    {advisor.name}
                  </motion.h3>
                  <motion.p 
                    className="text-sm font-normal leading-[150%] font-inter text-blackOne md:text-base"
                    variants={fadeInUp}
                  >
                    {advisor.bio}
                  </motion.p>
                </motion.div>
              </motion.div>
            );
          })}
        </motion.div>
      </div>
    </motion.section>
  );
};

const FounderSection: React.FC<{
  founders: Founder[];
  handleImageLoad: (src: string) => void;
  isImageLoaded: (src: string) => boolean;
}> = ({ founders, handleImageLoad, isImageLoaded }) => {
  const { ref, isInView } = useScrollAnimation();
  
  return (
    <motion.section 
      ref={ref}
      className="px-4 py-10 md:py-16"
      initial="hidden"
      animate={isInView ? "visible" : "hidden"}
      variants={fadeInUp}
    >
      <div className="mx-auto max-w-[993.16064453125px]">
        <motion.h2 
          className="mb-6 text-2xl sm:text-[32px] font-inter font-semibold text-blackOne md:text-3xl"
          variants={fadeInUp}
        >
          Founder
        </motion.h2>
        <motion.div 
          className="space-y-8"
          variants={staggerContainer}
        >
          {founders.map((f) => (
            <motion.div
              key={f.id}
              className="flex flex-col md:flex-row items-center gap-6 md:gap-8 rounded-lg border border-[#DFEAF2] bg-white py-6 px-4 md:px-8 lg:px-12"
              variants={fadeInUp}
              whileHover={{ scale: 1.01 }}
              transition={{ type: "spring", stiffness: 300 }}
            >
              <motion.div 
                className="flex flex-shrink-0 justify-center w-full md:w-auto"
                variants={fadeInLeft}
              >
                <OptimizedImage
                  src={f.image}
                  alt={f.name}
                  className="h-[200px] w-[200px] md:h-[280px] md:w-[220px] lg:h-[300px] lg:w-[233px] object-contain md:object-cover rounded-lg"
                  onLoad={handleImageLoad}
                  isLoaded={isImageLoaded(f.image)}
                />
              </motion.div>
              <motion.div 
                className="flex-1 text-center md:text-left"
                variants={fadeInRight}
              >
                <motion.h3 
                  className="mb-3 text-lg font-semibold md:mb-4 text-blackOne md:text-lg"
                  variants={fadeInUp}
                >
                  {f.name}
                </motion.h3>
                <motion.div 
                  className="space-y-4"
                  variants={staggerContainer}
                >
                  {f.bioParagraphs.map((p, idx) => (
                    <motion.p
                      key={idx}
                      className="text-sm font-normal leading-[150%] font-inter text-blackOne md:text-base"
                      variants={fadeInUp}
                    >
                      {p}
                    </motion.p>
                  ))}
                </motion.div>
              </motion.div>
            </motion.div>
          ))}
        </motion.div>
      </div>
    </motion.section>
  );
};

const MissionSection: React.FC = () => {
  const { ref, isInView } = useScrollAnimation();
  
  return (
    <motion.section 
      ref={ref}
      className="px-4 py-10 mb-20 md:py-10"
      initial="hidden"
      animate={isInView ? "visible" : "hidden"}
      variants={fadeInUp}
    >
      <div className="mx-auto max-w-[993.16064453125px]">
        <motion.div 
          className="inline-block px-4 py-3 mb-4 w-full font-semibold lg:text-2xl text-white rounded bg-[#01040B]"
          variants={fadeInUp}
        >
          Our Mission
        </motion.div>
        <motion.div 
          className="space-y-4 text-sm font-normal leading-6 font-inter text-blackOne md:text-base"
          variants={staggerContainer}
        >
          <motion.p variants={fadeInUp}>
            Our mission is to make{' '}
            <span className="font-semibold">
              agentic intelligence the operating core of the enterprise.
            </span>
          </motion.p>
          <motion.p variants={fadeInUp}>
            Platforms and tools have optimized pieces of the business. Agents
            unify them. They break through silos, run functions end-to-end,
            and transform how organizations grow, compete, and deliver value.
          </motion.p>
          <motion.p variants={fadeInUp}>
            Agentous exists to lead that shift — turning enterprises into
            living systems of autonomous agents that scale capacity,
            accelerate change, and unlock growth at unprecedented speed.
          </motion.p>
        </motion.div>
      </div>
    </motion.section>
  );
};

export default OurStoryPage;
