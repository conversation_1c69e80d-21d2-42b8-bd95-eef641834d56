import {
    visaIcon,
    masterCardIcon,
    americanExpressIcon,
  } from '@/assets/images';
  import React, { useRef, useLayoutEffect, useState } from 'react';
  import { motion } from 'framer-motion';
  
  const tabs = [
    { id: 'subscription', label: 'Subscription' },
    { id: 'billing', label: 'Billing settings' },
    { id: 'security', label: 'Security' },
  ];
  
  export const BillingSettings: React.FC = () => {
    const [activeTab, setActiveTab] = useState('subscription');
    const [twoFactorEnabled, setTwoFactorEnabled] = useState(true);
  
    // Animated tab underline logic
    const tabRefs = useRef<(HTMLButtonElement | null)[]>([]);
    const [underlineProps, setUnderlineProps] = useState({ left: 0, width: 0 });
    const activeTabIndex = tabs.findIndex(tab => tab.id === activeTab);
    useLayoutEffect(() => {
      if (activeTabIndex !== -1 && tabRefs.current[activeTabIndex]) {
        const el = tabRefs.current[activeTabIndex];
        setUnderlineProps({
          left: el.offsetLeft - 31,
          width: el.offsetWidth,
        });
      }
    }, [activeTab, tabs]);
  
    const renderTabContent = () => {
      switch (activeTab) {
        case 'subscription':
          return (
            <div className="w-[536px] space-y-6 rounded-md bg-white p-4">
              {/* SetIQ Trial Info */}
              <div className="flex flex-col gap-2 items-start">
                <div className="flex gap-2 items-center">
                  <div className="flex justify-center items-center w-9 h-9 text-xs bg-black rounded-full">
                    <img src="/" alt="logo" />
                  </div>
                  <div className="flex gap-2 items-center">
                    <h3 className="font-semibold text-black md:text-lg xl:text-2xl">
                      SetIQ
                    </h3>
                    <span className="px-2 py-1 text-xs text-black bg-transparent border border-black">
                      Trial
                    </span>
                  </div>
                </div>{' '}
                <p className="text-sm text-black md:text-base">
                  Collection Services AI Agents Suite
                </p>
              </div>
  
              {/* Usage Stats */}
              <div className="space-y-2">
                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-subText">Activities</span>
                    <span className="text-sm font-medium text-subText">
                      0 / 1,000
                    </span>
                  </div>
                  <div className="h-2 w-full rounded-full bg-[#E8E7E4]">
                    <div
                      className="h-2 rounded-full bg-primary"
                      style={{ width: '0%' }}
                    ></div>
                  </div>
                  <p className="text-sm font-meidum text-subText">
                    Trial ends on December 08, 2025
                  </p>
                  {/* Description */}
                  <p className="text-sm text-subText md:text-base">
                    Resolve accounts with precision, empathy, speed, and
                    compliance.
                  </p>
                </div>
  
                {/* Actions */}
                <div className="flex gap-3 justify-between items-center">
                  <button className="text-sm underline font-spartan text-primary">
                    Compare AI Agents Plans
                  </button>
                  <button className="px-4 py-2 text-sm rounded-md transition-colors bg-peach-5 text-primary hover:bg-primary/40">
                    Upgrade
                  </button>
                </div>
              </div>
            </div>
          );
  
        case 'billing':
          return (
            <div className="w-[536px] space-y-6 rounded-md bg-white p-4">
              {/* Payment Information */}
              <h3 className="font-semibold text-paragraph sm:text-lg">
                Payment information
              </h3>
              <p className="text-xs text-black sm:text-[13px]">
                Add a payment method to make purchases and keep your account
                active.
              </p>
              <div className="flex justify-between items-center">
                {/* Payment Method Icons */}
                <div className="flex gap-3">
                  <img
                    src={americanExpressIcon}
                    alt="AMEX"
                    className="object-contain w-12 h-8 rounded"
                  />
                  <img
                    src={masterCardIcon}
                    alt="MC"
                    className="object-contain w-12 h-8 rounded"
                  />
                  <img
                    src={visaIcon}
                    alt="VISA"
                    className="object-contain w-12 h-8 rounded"
                  />
                </div>
  
                <button className="px-4 py-2 text-sm rounded-md transition-colors bg-peach-5 text-primary hover:bg-primary/10">
                  Add payment method
                </button>
              </div>
            </div>
          );
  
        case 'security':
          return (
            <div className="w-[374px] space-y-6 rounded-md bg-white p-4">
              {/* Two-factor Authentication */}
              <div>
                <h3 className="mb-4 text-sm font-medium text-paragraph">
                  Two-factor Authentication
                </h3>
  
                <div className="flex gap-4 justify-start items-center mb-6">
                  <div className="relative">
                    <input
                      type="checkbox"
                      id="two-factor"
                      className="sr-only"
                      checked={twoFactorEnabled}
                      onChange={() => setTwoFactorEnabled(!twoFactorEnabled)}
                    />
                    <label
                      htmlFor="two-factor"
                      className="flex items-center cursor-pointer"
                    >
                      <div
                        className={`h-6 w-12 rounded-full shadow-inner transition-colors ${
                          twoFactorEnabled ? 'bg-primary' : 'bg-gray-300'
                        }`}
                      >
                        <div
                          className={`h-6 w-6 transform rounded-full bg-white shadow transition-transform ${
                            twoFactorEnabled ? 'translate-x-6' : 'translate-x-0'
                          }`}
                        ></div>
                      </div>
                    </label>
                  </div>
                  <span className="text-[13px] text-black">
                    Enable or disable two factor authentication
                  </span>
                </div>
              </div>
  
              {/* Current Password */}
              <div>
                <h3 className="mb-3 text-sm font-medium text-black">
                  Current Password
                </h3>
                <div className="p-3 w-full max-w-md bg-gray-50 rounded-md border border-gray-200">
                  <span className="text-gray-400">••••••••••</span>
                </div>
                <button className="mt-2 text-sm text-primary hover:underline">
                  Change Password
                </button>
              </div>
            </div>
          );
  
        default:
          return null;
      }
    };
  
    return (
      <div className="space-y-6">
        <h2 className="text-xl font-semibold text-black">Billing and Usage</h2>
  
        {/* Tabs */}
        <div className="border-b border-gray-200">
          <nav className="flex relative space-x-8">
            {tabs.map((tab, idx) => (
              <button
                key={tab.id}
                ref={el => (tabRefs.current[idx] = el)}
                onClick={() => setActiveTab(tab.id)}
                className={`px-1 py-2 text-sm font-medium transition-colors ${
                  activeTab === tab.id
                    ? 'text-primary'
                    : 'text-gray-500 hover:text-black'
                }`}
              >
                {tab.label}
              </button>
            ))}
            {/* Animated underline */}
            {activeTabIndex !== -1 && (
              <motion.div
                layout
                className="absolute bottom-0 h-0.5 rounded bg-primary"
                style={{ left: underlineProps.left, width: underlineProps.width }}
                transition={{ type: 'spring', stiffness: 400, damping: 30 }}
              />
            )}
          </nav>
        </div>
  
        {/* Tab Content */}
        <div>{renderTabContent()}</div>
      </div>
    );
  };
  