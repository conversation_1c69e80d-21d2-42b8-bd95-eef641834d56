import React from 'react';
import { Navigate, Route, Routes } from 'react-router-dom';

import NotFoundPage from '../NotFoundPage';
import { BillingSettings } from './BillingSettings';
import { MembersSettings } from './MembersSettings';
import InviteMembersPage from './MembersSettings/InviteMembersPage';
import { NotificationSettings } from './NotificationSettings';
import { ProfileSettings } from './ProfileSettings';

const PivotlSettingsPage: React.FC = () => {
  return (
    <Routes>
      <Route path="/" element={<Navigate to="profile" replace />} />
      <Route path="profile/*" element={<ProfileSettings />} />
      <Route path="notifications" element={<NotificationSettings />} />
      <Route path="billing" element={<BillingSettings />} />
      <Route path="members" element={<MembersSettings />} />
      <Route path="members/invite" element={<InviteMembersPage />} />
      <Route path="*" element={<NotFoundPage />} />
    </Routes>
  );
};

export default PivotlSettingsPage;
