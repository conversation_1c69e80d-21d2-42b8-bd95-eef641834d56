import React from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { ProfileSettings } from './ProfileSettings';
import { NotificationSettings } from './NotificationSettings';
import { BillingSettings } from './BillingSettings';
import { MembersSettings } from './MembersSettings';
import InviteMembersPage from './MembersSettings/InviteMembersPage';
import AppContainer from '../../components/common/AppContainer';
import NotFoundPage from '../NotFoundPage';

const PivotlSettingsPage: React.FC = () => {
  return (
    <div className="overflow-y-auto h-full">
      <AppContainer>
        <Routes>
          <Route path="/" element={<Navigate to="profile" replace />} />
          <Route path="profile/*" element={<ProfileSettings />} />
          <Route path="notifications" element={<NotificationSettings />} />
          <Route path="billing" element={<BillingSettings />} />
          <Route path="members" element={<MembersSettings />} />
          <Route path="members/invite" element={<InviteMembersPage />} />
          <Route path="*" element={<NotFoundPage />} />
        </Routes>
      </AppContainer>
    </div>
  );
};

export default PivotlSettingsPage;
