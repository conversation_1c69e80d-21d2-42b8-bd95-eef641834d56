import { yupResolver } from '@hookform/resolvers/yup';
import { ArrowLeft, Eye, EyeOff, X } from 'lucide-react';
import React, { useEffect, useRef, useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { useNavigate } from 'react-router-dom';
import * as yup from 'yup';

import EnhancedChatSidebar from '@/components/common/EnhancedChatSidebar';
import { Spinner } from '@/components/common/Loader';
import { Input } from '@/components/ui';
import { useTenant } from '@/context/TenantContext';
import { useChangePasswordMutation } from '@/hooks/useUserProfile';

interface ChangePasswordFormData {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

const changePasswordSchema = yup.object().shape({
  currentPassword: yup.string().required('Current password is required'),
  newPassword: yup
    .string()
    .required('New password is required')
    .min(8, 'Password must be at least 8 characters')
    .matches(
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/,
      'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character'
    ),
  confirmPassword: yup
    .string()
    .required('Please confirm your password')
    .oneOf([yup.ref('newPassword')], 'Passwords must match'),
});

export const ChangePassword: React.FC = () => {
  const navigate = useNavigate();
  const { setActiveAgent } = useTenant();
  const changePasswordMutation = useChangePasswordMutation();
  const [submitError, setSubmitError] = useState<string | null>(null);
  const [submitSuccess, setSubmitSuccess] = useState<string | null>(null);
  const [showPasswords, setShowPasswords] = useState({
    current: false,
    new: false,
    confirm: false,
  });
  const [chatMessage, setChatMessage] = useState<string>('');
  const reloadChatHistoryRef = useRef<(() => Promise<void>) | null>(null);

  // Set active agent to Regis when component mounts
  useEffect(() => {
    setActiveAgent('regis');
  }, [setActiveAgent]);

  const {
    control,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<ChangePasswordFormData>({
    resolver: yupResolver(changePasswordSchema),
    defaultValues: {
      currentPassword: '',
      newPassword: '',
      confirmPassword: '',
    },
  });

  const togglePasswordVisibility = (field: keyof typeof showPasswords) => {
    setShowPasswords(prev => ({
      ...prev,
      [field]: !prev[field],
    }));
  };

  const onSubmit = async (data: ChangePasswordFormData) => {
    setSubmitError(null);
    setSubmitSuccess(null);

    try {
      await changePasswordMutation.mutateAsync({
        token: '', // TODO: Get token from auth context
        oldPassword: data.currentPassword,
        newPassword: data.newPassword,
      });

      setSubmitSuccess('Password changed successfully!');
      reset();

      // Navigate back after 2 seconds
      setTimeout(() => {
        navigate('/dashboard/settings/profile');
      }, 2000);
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Failed to change password';
      setSubmitError(errorMessage);
    }
  };

  const handleForgotPassword = () => {
    // TODO: Implement forgot password flow
    console.log('Forgot password clicked');
  };

  return (
    <div className="flex h-full">
      {/* Chat Sidebar - LEFT SIDE */}
      <EnhancedChatSidebar
        reloadChatHistoryRef={reloadChatHistoryRef}
        externalMessage={chatMessage}
      />

      {/* Main Content - RIGHT SIDE */}
      <div className="flex-1 overflow-y-auto">
        <div className="flex w-full max-w-[850px] flex-col gap-4 p-8">
          <div className="flex items-center space-x-4">
            <button
              onClick={() => navigate('/dashboard/settings/profile')}
              className="flex h-8 w-8 items-center justify-center rounded-full hover:bg-gray-100"
            >
              <ArrowLeft className="h-4 w-4" />
            </button>
            <h1 className="text-2xl font-semibold text-blackOne">
              Change Password
            </h1>
          </div>

          {submitError && (
            <div className="relative overflow-hidden rounded-lg border border-[#fecaca] bg-gradient-to-r from-[#fef2f2] to-[#fee2e2] p-4 shadow-sm">
              <div className="flex items-start gap-3">
                <div className="flex-shrink-0">
                  <svg
                    className="h-5 w-5 text-[#dc2626]"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fillRule="evenodd"
                      d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                      clipRule="evenodd"
                    />
                  </svg>
                </div>
                <div className="flex-1">
                  <p className="text-sm font-medium text-[#991b1b]">
                    {submitError}
                  </p>
                </div>
                <button
                  onClick={() => setSubmitError(null)}
                  className="flex-shrink-0 text-[#f87171] transition-colors hover:text-[#dc2626]"
                >
                  <X className="h-4 w-4" />
                </button>
              </div>
            </div>
          )}

          {submitSuccess && (
            <div className="relative overflow-hidden rounded-xl border border-[#bbf7d0] bg-gradient-to-r from-[#f0fdf4] to-[#dcfce7] p-4">
              <div className="flex items-start gap-3">
                <div className="flex-shrink-0">
                  <svg
                    className="h-6 w-6 text-[#16a34a]"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fillRule="evenodd"
                      d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                      clipRule="evenodd"
                    />
                  </svg>
                </div>
                <div className="flex-1">
                  <p className="text-sm font-medium text-[#166534]">
                    {submitSuccess}
                  </p>
                </div>
              </div>
            </div>
          )}

          <div className="max-w-md">
            <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
              <div>
                <label className="mb-2 block text-sm font-medium text-subText">
                  Verify Current Password
                </label>
                <div className="relative">
                  <Controller
                    name="currentPassword"
                    control={control}
                    render={({ field }) => (
                      <Input
                        {...field}
                        type={showPasswords.current ? 'text' : 'password'}
                        placeholder="Enter current password"
                        className={`h-10 w-full rounded-md border px-3 py-2 pr-10 focus:border-transparent focus:outline-none focus:ring-2 focus:ring-primary ${
                          errors.currentPassword
                            ? 'border-red-500'
                            : 'border-[#DFEAF2]'
                        }`}
                        disabled={changePasswordMutation.isPending}
                      />
                    )}
                  />
                  <button
                    type="button"
                    onClick={() => togglePasswordVisibility('current')}
                    className="absolute right-3 top-1/2 -translate-y-1/2 transform text-gray-400 hover:text-gray-600"
                  >
                    {showPasswords.current ? (
                      <EyeOff className="h-4 w-4" />
                    ) : (
                      <Eye className="h-4 w-4" />
                    )}
                  </button>
                </div>
                {errors.currentPassword && (
                  <p className="mt-1 text-xs text-red-500">
                    {errors.currentPassword.message}
                  </p>
                )}
              </div>

              <div>
                <label className="mb-2 block text-sm font-medium text-subText">
                  New Password
                </label>
                <div className="relative">
                  <Controller
                    name="newPassword"
                    control={control}
                    render={({ field }) => (
                      <Input
                        {...field}
                        type={showPasswords.new ? 'text' : 'password'}
                        placeholder="Enter new password"
                        className={`h-10 w-full rounded-md border px-3 py-2 pr-10 focus:border-transparent focus:outline-none focus:ring-2 focus:ring-primary ${
                          errors.newPassword
                            ? 'border-red-500'
                            : 'border-[#DFEAF2]'
                        }`}
                        disabled={changePasswordMutation.isPending}
                      />
                    )}
                  />
                  <button
                    type="button"
                    onClick={() => togglePasswordVisibility('new')}
                    className="absolute right-3 top-1/2 -translate-y-1/2 transform text-gray-400 hover:text-gray-600"
                  >
                    {showPasswords.new ? (
                      <EyeOff className="h-4 w-4" />
                    ) : (
                      <Eye className="h-4 w-4" />
                    )}
                  </button>
                </div>
                {errors.newPassword && (
                  <p className="mt-1 text-xs text-red-500">
                    {errors.newPassword.message}
                  </p>
                )}
              </div>

              <div>
                <label className="mb-2 block text-sm font-medium text-subText">
                  Confirm New Password
                </label>
                <div className="relative">
                  <Controller
                    name="confirmPassword"
                    control={control}
                    render={({ field }) => (
                      <Input
                        {...field}
                        type={showPasswords.confirm ? 'text' : 'password'}
                        placeholder="Confirm new password"
                        className={`h-10 w-full rounded-md border px-3 py-2 pr-10 focus:border-transparent focus:outline-none focus:ring-2 focus:ring-primary ${
                          errors.confirmPassword
                            ? 'border-red-500'
                            : 'border-[#DFEAF2]'
                        }`}
                        disabled={changePasswordMutation.isPending}
                      />
                    )}
                  />
                  <button
                    type="button"
                    onClick={() => togglePasswordVisibility('confirm')}
                    className="absolute right-3 top-1/2 -translate-y-1/2 transform text-gray-400 hover:text-gray-600"
                  >
                    {showPasswords.confirm ? (
                      <EyeOff className="h-4 w-4" />
                    ) : (
                      <Eye className="h-4 w-4" />
                    )}
                  </button>
                </div>
                {errors.confirmPassword && (
                  <p className="mt-1 text-xs text-red-500">
                    {errors.confirmPassword.message}
                  </p>
                )}
              </div>

              <div className="text-sm">
                <button
                  type="button"
                  onClick={handleForgotPassword}
                  className="text-primary hover:underline"
                >
                  Forgot or never set up your password?
                </button>
                <br />
                <span className="cursor-pointer text-primary hover:underline">
                  Request a new password here.
                </span>
              </div>

              <button
                type="submit"
                disabled={changePasswordMutation.isPending}
                className="w-full rounded-lg bg-primary px-4 py-2 text-white hover:bg-primary/90 disabled:cursor-not-allowed disabled:opacity-50"
              >
                {changePasswordMutation.isPending ? (
                  <div className="flex items-center justify-center">
                    <Spinner className="h-4 w-4" />
                  </div>
                ) : (
                  'Save Changes'
                )}
              </button>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
};
