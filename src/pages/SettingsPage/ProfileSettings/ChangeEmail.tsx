import React, { useState, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { ArrowLeft, X } from 'lucide-react';
import { Input } from '@/components/ui';
import { Spinner } from '@/components/common/Loader';
import { useAuth } from '@/context/AuthContext';
import EnhancedChatSidebar from '@/components/common/EnhancedChatSidebar';

export const ChangeEmail: React.FC = () => {
  const { user } = useAuth();
  const currentEmail = user?.email || '';
  const navigate = useNavigate();
  const [step, setStep] = useState<'verification' | 'form'>('verification');
  const [newEmail, setNewEmail] = useState('');
  const [verificationCode, setVerificationCode] = useState(['', '', '', '', '', '']);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [chatMessage, setChatMessage] = useState<string>('');
  const reloadChatHistoryRef = useRef<(() => Promise<void>) | null>(null);

  const handleEmailSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError(null);

    try {
      // TODO: Implement email change request
      console.log('Changing email to:', newEmail);
      // Navigate back to profile after success
      navigate('/dashboard/settings/profile');
    } catch (err) {
      setError('Failed to change email. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleVerificationCodeChange = (index: number, value: string) => {
    if (value.length <= 1 && /^\d*$/.test(value)) {
      const newCodes = [...verificationCode];
      newCodes[index] = value;
      setVerificationCode(newCodes);
      
      // Auto-focus next input
      if (value && index < 5) {
        const nextInput = document.getElementById(`code-${index + 1}`);
        nextInput?.focus();
      }
    }
  };

  const handleVerificationSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    const code = verificationCode.join('');
    if (code.length !== 6) {
      setError('Please enter all 6 digits');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      // TODO: Implement verification
      console.log('Verifying code:', code);
      setStep('form');
    } catch (err) {
      setError('Invalid verification code. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleResendCode = async () => {
    setIsLoading(true);
    try {
      // TODO: Implement resend logic
      console.log('Resending verification code');
    } catch (err) {
      setError('Failed to resend code. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  if (step === 'verification') {
    return (
      <div className="flex h-full">
        {/* Chat Sidebar - LEFT SIDE */}
        <EnhancedChatSidebar
          reloadChatHistoryRef={reloadChatHistoryRef}
          externalMessage={chatMessage}
        />

        {/* Main Content - RIGHT SIDE */}
        <div className="flex-1 overflow-y-auto">
          <div className="max-w-[850px] w-full flex flex-col gap-4 p-8">
            <div className="flex items-center space-x-4">
              <button
                onClick={() => navigate('/dashboard/settings/profile')}
                className="flex justify-center items-center w-8 h-8 rounded-full hover:bg-gray-100"
              >
                <ArrowLeft className="w-4 h-4" />
              </button>
              <h1 className="text-2xl font-semibold text-blackOne">Email Confirmation</h1>
            </div>

        {error && (
          <div className="relative overflow-hidden rounded-lg border border-[#fecaca] bg-gradient-to-r from-[#fef2f2] to-[#fee2e2] p-4 shadow-sm">
            <div className="flex gap-3 items-start">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-[#dc2626]" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="flex-1">
                <p className="text-sm font-medium text-[#991b1b]">{error}</p>
              </div>
              <button onClick={() => setError(null)} className="flex-shrink-0 text-[#f87171] transition-colors hover:text-[#dc2626]">
                <X className="w-4 h-4" />
              </button>
            </div>
          </div>
        )}

        <div className="max-w-md">
          <p className="mb-6 text-sm text-subText">
            For security, please confirm it's you by inputting the 6 digit code sent to your email before we continue.
          </p>

          <form onSubmit={handleVerificationSubmit} className="space-y-6">
            <div>
              <label className="block mb-4 text-sm font-medium text-blackOne">
                Enter a 6-digit code to sent to your email to verify your identity
              </label>
              <div className="flex gap-3">
                {verificationCode.map((code, index) => (
                  <input
                    key={index}
                    id={`code-${index}`}
                    type="text"
                    maxLength={1}
                    value={code}
                    onChange={(e) => handleVerificationCodeChange(index, e.target.value)}
                    className="w-12 h-12 text-center text-lg font-medium border border-[#DFEAF2] rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                    disabled={isLoading}
                  />
                ))}
              </div>
            </div>

            <div className="text-sm text-subText">
              Didn't get a message?{' '}
              <button
                type="button"
                onClick={handleResendCode}
                disabled={isLoading}
                className="text-primary hover:underline disabled:opacity-50"
              >
                Resend Code
              </button>
            </div>

            <button
              type="submit"
              disabled={isLoading || verificationCode.some(code => !code)}
              className="px-4 py-2 w-full text-white rounded-lg bg-primary hover:bg-primary/90 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading ? (
                <div className="flex justify-center items-center">
                  <Spinner className="w-4 h-4" />
                </div>
              ) : (
                'Proceed'
              )}
            </button>
          </form>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex h-full">
      {/* Chat Sidebar - LEFT SIDE */}
      <EnhancedChatSidebar
        reloadChatHistoryRef={reloadChatHistoryRef}
        externalMessage={chatMessage}
      />

      {/* Main Content - RIGHT SIDE */}
      <div className="flex-1 overflow-y-auto">
        <div className="max-w-[850px] w-full flex flex-col gap-4 p-8">
          <div className="flex items-center space-x-4">
            <button
              onClick={() => setStep('verification')}
              className="flex justify-center items-center w-8 h-8 rounded-full hover:bg-gray-100"
            >
              <ArrowLeft className="w-4 h-4" />
            </button>
            <h1 className="text-2xl font-semibold text-blackOne">Change Email</h1>
          </div>

      {error && (
        <div className="relative overflow-hidden rounded-lg border border-[#fecaca] bg-gradient-to-r from-[#fef2f2] to-[#fee2e2] p-4 shadow-sm">
          <div className="flex gap-3 items-start">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-[#dc2626]" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="flex-1">
              <p className="text-sm font-medium text-[#991b1b]">{error}</p>
            </div>
            <button onClick={() => setError(null)} className="flex-shrink-0 text-[#f87171] transition-colors hover:text-[#dc2626]">
              <X className="w-4 h-4" />
            </button>
          </div>
        </div>
      )}

      <div className="max-w-md">
        <form onSubmit={handleEmailSubmit} className="space-y-6">
          <div>
            <label className="block mb-2 text-sm font-medium text-subText">
              Current Email
            </label>
            <Input
              type="email"
              value={currentEmail}
              disabled
              className="h-10 w-full cursor-not-allowed rounded-md bg-gray-50 border border-[#DFEAF2] px-3 py-2 text-gray-500 focus:outline-none"
            />
          </div>

          <div>
            <label className="block mb-2 text-sm font-medium text-subText">
              New Email Address
            </label>
            <Input
              type="email"
              value={newEmail}
              onChange={(e) => setNewEmail(e.target.value)}
              placeholder="Enter new email address"
              className="h-10 w-full rounded-md border border-[#DFEAF2] px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
              required
              disabled={isLoading}
            />
          </div>

          <button
            type="submit"
            disabled={isLoading || !newEmail || newEmail === currentEmail}
            className="px-4 py-2 w-full text-white rounded-lg bg-primary hover:bg-primary/90 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isLoading ? (
              <div className="flex justify-center items-center">
                <Spinner className="w-4 h-4" />
              </div>
            ) : (
              'Save Changes'
            )}
          </button>
        </form>
        </div>
        </div>
      </div>
    </div>
  );
};