import React from 'react';
import { Routes, Route } from 'react-router-dom';
import { ProfileForm } from './ProfileForm';
import { ChangeEmail } from './ChangeEmail';
import { ChangePassword } from './ChangePassword';
import NotFoundPage from '../../NotFoundPage';

export const ProfileSettings: React.FC = () => {
  return (
    <Routes>
      <Route index element={<ProfileForm />} />
      <Route path="change-email" element={<ChangeEmail />} />
      <Route path="change-password" element={<ChangePassword />} />
      <Route path="*" element={<NotFoundPage />} />
    </Routes>
  );
};
