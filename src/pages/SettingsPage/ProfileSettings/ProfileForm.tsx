import { yupResolver } from '@hookform/resolvers/yup';
import { Loader2, Pencil, X } from 'lucide-react';
import React, { useEffect, useRef, useState } from 'react';
import { Controller, SubmitHandler, useForm } from 'react-hook-form';
import Select from 'react-select';

import { profilePlaceholder } from '@/assets/images';
import { Spinner } from '@/components/common/Loader';
import { DashboardWithChatLayout } from '@/components/layout/DashboardWithChatLayout';
import { Input } from '@/components/ui';
import { useTenant } from '@/context/TenantContext';

import { useAuth } from '../../../context/AuthContext';
import { useAvatarUpload } from '../../../hooks/useAvatarUpload';
import { useTimezones } from '../../../hooks/useTimezones';
import {
  useGetUserFunctions,
  useUpdateUserInfoMutation,
} from '../../../hooks/useUserProfile';
import { profileSettingsSchema } from '../../../lib/yup/profileValidations';
import { ProfileFormData } from '../../../types/profile';
import { AccountInformation } from './AccountInformation';

export const ProfileForm: React.FC = () => {
  const { user, isLoadingUserInitials, isError } = useAuth();
  const { setActiveAgent } = useTenant();
  const updateUserInfoMutation = useUpdateUserInfoMutation();
  const { isUploading, uploadError, handleFileSelect } = useAvatarUpload();
  const {
    timezoneOptions: apiTimezoneOptions,
    isLoading: isLoadingTimezones,
    isError: isTimezoneError,
  } = useTimezones();

  // Fetch user functions/roles from API
  const {
    data: userFunctionsData,
    isLoading: isLoadingUserFunctions,
    isError: isUserFunctionsError,
  } = useGetUserFunctions();

  const fileInputRef = useRef<HTMLInputElement>(null);
  const reloadChatHistoryRef = useRef<(() => Promise<void>) | null>(null);
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [updateError, setUpdateError] = useState<string | null>(null);
  const [updateSuccess, setUpdateSuccess] = useState<string | null>(null);
  const [chatMessage, setChatMessage] = useState<string>('');

  // Auto-clear messages after 5 seconds
  useEffect(() => {
    let timeoutId: NodeJS.Timeout;

    if (updateError || updateSuccess) {
      timeoutId = setTimeout(() => {
        setUpdateError(null);
        setUpdateSuccess(null);
      }, 5000);
    }

    return () => {
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
    };
  }, [updateError, updateSuccess]);

  const userData = user;

  const getInitialValues = () => ({
    firstName: userData?.firstName || '',
    lastName: userData?.lastName || '',
    email: userData?.email || '',
    role: userData?.roleInCompany || '',
    timezone: userData?.timezone || '', // No default timezone
  });

  // Personal information form configuration
  const {
    control,
    handleSubmit,
    formState: { errors, isDirty },
    reset,
  } = useForm<ProfileFormData>({
    resolver: yupResolver(profileSettingsSchema),
    defaultValues: getInitialValues(),
  });

  // Set active agent to Regis when component mounts
  useEffect(() => {
    setActiveAgent('regis');
  }, [setActiveAgent]);

  // Update forms when user data or tenant data changes
  useEffect(() => {
    if (userData) {
      reset(getInitialValues());
    }
  }, [userData, reset]);

  // Personal information form submission handler
  const onSubmit: SubmitHandler<ProfileFormData> = async data => {
    setIsSubmitting(true);
    setUpdateError(null);
    setUpdateSuccess(null);
    try {
      await updateUserInfoMutation.mutateAsync({
        firstname: data.firstName,
        lastname: data.lastName,
        timezone: data.timezone,
        roleInCompany: data.role,
      });

      setUpdateSuccess('Profile updated successfully!');

      // Reset form dirty state after successful submission
      reset(data);
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Failed to update profile';
      setUpdateError(errorMessage);
      console.error('Failed to update profile:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Dynamic role options from API
  const roleOptions = userFunctionsData?.data
    ? userFunctionsData.data.map((role: string) => ({
        value: role,
        label: role.charAt(0).toUpperCase() + role.slice(1).toLowerCase(),
      }))
    : [];

  // Fallback timezone options if API fails
  const fallbackTimezoneOptions: { value: string; label: string }[] = [];

  const timezoneOptions = isTimezoneError
    ? fallbackTimezoneOptions
    : apiTimezoneOptions;

  // Custom styles for react-select
  const selectStyles = {
    control: (provided: any) => ({
      ...provided,
      height: '40px',
      border: '1px solid #DFEAF2',
      borderRadius: '6px',
      boxShadow: 'none',
      '&:hover': {
        border: '1px solid #DFEAF2',
      },
    }),
    option: (provided: any, state: any) => ({
      ...provided,
      backgroundColor: state.isSelected
        ? '#FF5C02'
        : state.isFocused
          ? '#FFF5F0'
          : 'white',
      color: state.isSelected ? 'white' : '#374151',
      '&:hover': {
        backgroundColor: state.isSelected ? '#FF5C02' : '#FFF5F0',
      },
    }),
    menu: (provided: any) => ({
      ...provided,
      border: '1px solid #DFEAF2',
      borderRadius: '6px',
      boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
    }),
  };

  if (isLoadingUserInitials) {
    return (
      <div className="flex h-64 items-center justify-center">
        <div className="flex flex-col items-center space-y-4">
          <Spinner className="h-8 w-8" />
          <p className="text-sm text-gray-500">Loading profile...</p>
        </div>
      </div>
    );
  }

  if (isError) {
    return (
      <div className="flex h-64 items-center justify-center">
        <div className="text-center">
          <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-red-100">
            <svg
              className="h-8 w-8 text-red-600"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
              />
            </svg>
          </div>
          <h3 className="mb-2 text-lg font-medium text-blackOne">
            Failed to load profile
          </h3>
          <p className="mb-4 text-sm text-gray-500">
            Unable to load your profile information. Please try refreshing the
            page.
          </p>
          <button
            onClick={() => window.location.reload()}
            className="rounded-md bg-primary px-4 py-2 text-sm text-white hover:bg-primary/90"
          >
            Refresh Page
          </button>
        </div>
      </div>
    );
  }

  return (
    <DashboardWithChatLayout
      reloadChatHistoryRef={reloadChatHistoryRef}
      externalMessage={chatMessage}
    >
      <div className="px-8 py-6">
        <div className="space-y-8">
          {/* Error and Success Messages - Top of Page */}
          {updateError && (
            <div className="relative overflow-hidden rounded-lg border border-[#fecaca] bg-gradient-to-r from-[#fef2f2] to-[#fee2e2] p-4 shadow-sm">
              <div className="flex items-start gap-3">
                <div className="flex-shrink-0">
                  <svg
                    className="h-5 w-5 text-[#dc2626]"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fillRule="evenodd"
                      d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                      clipRule="evenodd"
                    />
                  </svg>
                </div>
                <div className="flex-1">
                  <p className="text-sm font-medium text-[#991b1b]">
                    {updateError}
                  </p>
                </div>
                <button
                  onClick={() => setUpdateError(null)}
                  className="flex-shrink-0 text-[#f87171] transition-colors hover:text-[#dc2626]"
                >
                  <X className="h-4 w-4" />
                </button>
              </div>
              {/* Progress bar */}
              <div className="absolute bottom-0 left-0 h-1 animate-pulse bg-[#fca5a5]"></div>
            </div>
          )}

          {updateSuccess && (
            <div className="relative overflow-hidden rounded-xl border border-[#bbf7d0] bg-gradient-to-r from-[#f0fdf4] to-[#dcfce7] p-4">
              <div className="flex items-start gap-3">
                <div className="flex-shrink-0">
                  <svg
                    className="h-6 w-6 text-[#16a34a]"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fillRule="evenodd"
                      d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                      clipRule="evenodd"
                    />
                  </svg>
                </div>
                <div className="flex-1">
                  <p className="text-sm font-medium text-[#166534]">
                    {updateSuccess}
                  </p>
                </div>
                <button
                  onClick={() => setUpdateSuccess(null)}
                  className="flex-shrink-0 text-[#4ade80] transition-colors hover:text-[#16a34a]"
                >
                  <X className="h-4 w-4" />
                </button>
              </div>
              {/* Progress bar */}
              <div className="absolute bottom-0 left-0 h-1 animate-pulse bg-[#86efac]"></div>
            </div>
          )}

          {/* Personal Information Section */}
          <div>
            <h2 className="mb-6 text-xl font-semibold text-blackOne">
              Personal Information
            </h2>

            <div className="flex flex-col items-start space-y-6">
              {/* Profile Picture */}
              <div className="flex w-full flex-col items-start gap-2">
                <div className="relative shrink-0">
                  <div className="flex h-20 w-20 items-center justify-center overflow-hidden rounded-full lg:h-[150px] lg:w-[150px] ">
                    <img
                      src={userData?.profilePicture || profilePlaceholder}
                      alt="Profile"
                      className="h-full w-full flex-shrink-0 object-cover"
                    />
                    {isUploading && (
                      <div className="absolute inset-0 flex items-center justify-center rounded-full bg-black bg-opacity-50">
                        <Spinner className="h-6 w-6 text-white" />
                      </div>
                    )}
                  </div>
                  <button
                    onClick={() => fileInputRef.current?.click()}
                    disabled={isUploading}
                    className="absolute bottom-2 right-1 flex h-[25px] w-[25px] items-center justify-center rounded-full bg-primary text-xs text-white disabled:bg-gray-400"
                  >
                    <Pencil className="h-3 w-3" />
                  </button>
                  <input
                    ref={fileInputRef}
                    type="file"
                    accept="image/*"
                    onChange={async e => {
                      await handleFileSelect(e);
                      // Clear the input to allow selecting the same file again
                      e.target.value = '';
                    }}
                    className="hidden"
                  />
                </div>{' '}
                {uploadError && (
                  <p className="text-xs text-red-500">{uploadError}</p>
                )}
              </div>

              {/* Personal Information Form */}
              <form
                onSubmit={handleSubmit(onSubmit)}
                className="w-full max-w-2xl space-y-4"
              >
                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                  <div>
                    <label className="mb-2 block text-[13px] text-subText">
                      First name
                    </label>
                    <Controller
                      name="firstName"
                      control={control}
                      render={({ field }) => (
                        <Input
                          {...field}
                          type="text"
                          className={`h-10 w-full rounded-md border px-3 py-2 focus:outline-none ${
                            errors.firstName
                              ? 'border-red-500'
                              : 'border-[#DFEAF2]'
                          }`}
                          placeholder="Enter first name"
                        />
                      )}
                    />
                    {errors.firstName && (
                      <p className="mt-1 text-xs text-red-500">
                        {errors.firstName.message}
                      </p>
                    )}
                  </div>

                  <div>
                    <label className="mb-2 block text-[13px] text-subText">
                      Job Function
                    </label>
                    <Controller
                      name="role"
                      control={control}
                      render={({ field }) => (
                        <Select
                          options={roleOptions}
                          styles={{
                            ...selectStyles,
                            control: (provided: any) => ({
                              ...provided,
                              height: '40px',
                              border: errors.role
                                ? '1px solid #ef4444'
                                : '1px solid #DFEAF2',
                              borderRadius: '6px',
                              boxShadow: 'none',
                              '&:hover': {
                                border: errors.role
                                  ? '1px solid #ef4444'
                                  : '1px solid #DFEAF2',
                              },
                            }),
                          }}
                          value={
                            roleOptions.find(
                              (option: { value: string; label: string }) =>
                                option.value === field.value
                            ) || null
                          }
                          onChange={(
                            selectedOption: {
                              value: string;
                              label: string;
                            } | null
                          ) => {
                            console.log(
                              'Role selected:',
                              selectedOption?.value
                            );
                            field.onChange(selectedOption?.value || '');
                          }}
                          components={{
                            IndicatorSeparator: () => null,
                          }}
                          placeholder={
                            isLoadingUserFunctions
                              ? 'Loading roles...'
                              : isUserFunctionsError
                                ? 'Select role (limited options)'
                                : 'Select role'
                          }
                          isSearchable={false}
                          isLoading={isLoadingUserFunctions}
                        />
                      )}
                    />
                    {errors.role && (
                      <p className="mt-1 text-xs text-red-500">
                        {errors.role.message}
                      </p>
                    )}
                  </div>
                </div>

                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                  <div>
                    <label className="mb-2 block text-[13px] text-subText">
                      Last name
                    </label>
                    <Controller
                      name="lastName"
                      control={control}
                      render={({ field }) => (
                        <Input
                          {...field}
                          type="text"
                          className={`h-10 w-full rounded-md border px-3 py-2 focus:outline-none ${
                            errors.lastName
                              ? 'border-red-500'
                              : 'border-[#DFEAF2]'
                          }`}
                          placeholder="Enter last name"
                        />
                      )}
                    />
                    {errors.lastName && (
                      <p className="mt-1 text-xs text-red-500">
                        {errors.lastName.message}
                      </p>
                    )}
                  </div>

                  <div>
                    <label className="mb-2 block text-[13px] text-subText">
                      Timezone
                    </label>
                    <Controller
                      name="timezone"
                      control={control}
                      render={({ field }) => (
                        <Select
                          options={timezoneOptions}
                          styles={{
                            ...selectStyles,
                            control: (provided: any) => ({
                              ...provided,
                              height: '40px',
                              border: errors.timezone
                                ? '1px solid #ef4444'
                                : '1px solid #DFEAF2',
                              borderRadius: '6px',
                              boxShadow: 'none',
                              '&:hover': {
                                border: errors.timezone
                                  ? '1px solid #ef4444'
                                  : '1px solid #DFEAF2',
                              },
                            }),
                          }}
                          value={timezoneOptions.find(
                            (option: { value: string; label: string }) =>
                              option.value === field.value
                          )}
                          onChange={selectedOption =>
                            field.onChange(selectedOption?.value)
                          }
                          components={{
                            IndicatorSeparator: () => null,
                          }}
                          placeholder={
                            isLoadingTimezones
                              ? 'Loading timezones...'
                              : isTimezoneError
                                ? 'Select timezone (limited options)'
                                : 'Select timezone'
                          }
                          isSearchable={true}
                        />
                      )}
                    />
                    {errors.timezone && (
                      <p className="mt-1 text-xs text-red-500">
                        {errors.timezone.message}
                      </p>
                    )}
                  </div>
                </div>

                <div className="flex justify-start">
                  <button
                    type="submit"
                    disabled={!isDirty || isSubmitting}
                    className="h-10 w-fit rounded-lg bg-grayTen px-6 py-2 text-white transition-colors hover:bg-gray-900 disabled:opacity-50"
                  >
                    {isSubmitting ? (
                      <div className="flex items-center justify-center">
                        <Loader2 className="h-4 w-4" />{' '}
                        <span className="text-sm">Saving...</span>
                      </div>
                    ) : (
                      'Save changes'
                    )}
                  </button>
                </div>
              </form>
            </div>
          </div>

          {/* account information section */}
          <AccountInformation email={userData?.email || ''} />
        </div>
      </div>
    </DashboardWithChatLayout>
  );
};
