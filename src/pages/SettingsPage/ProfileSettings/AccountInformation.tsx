import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Input } from '@/components/ui';
import { ROUTES } from '@/constants/routes';

interface AccountInformationProps {
  email: string;
}

export const AccountInformation: React.FC<AccountInformationProps> = ({
  email,
}) => {
  const navigate = useNavigate();
  return (
    <div className="w-full max-w-2xl">
      <h2 className="mb-6 text-xl font-semibold text-blackOne">
        Account Information
      </h2>
      <div className="space-y-6">
        <div className="grid grid-cols-1 gap-y-6 gap-x-8 md:grid-cols-2">
          <div>
            <label className="mb-2 block text-[13px] text-subText">Email</label>
            <Input
              type="email"
              value={email}
              disabled
              className="h-10 w-full cursor-not-allowed rounded-md bg-white border border-[#DFEAF2] px-3 py-2 text-gray-500 focus:outline-none"
            />
            <button
              type="button"
              disabled
              onClick={() => navigate(ROUTES.SETTINGS_CHANGE_EMAIL)}
              className="mt-2 text-sm text-primary hover:underline"
            >
              Change Email
            </button>
          </div>
          <div>
            <label className="mb-2 block text-[13px] text-subText">
              Password
            </label>
            <Input
              type="password"
              value="************"
              disabled
              className="h-10 w-full cursor-not-allowed rounded-md border border-[#DFEAF2] disabled:bg-white px-3 py-2 text-gray-500 focus:outline-none"
            />
            <button
              type="button"
              disabled
              onClick={() => navigate(ROUTES.SETTINGS_CHANGE_PASSWORD)}
              className="mt-2 text-sm text-primary hover:underline"
            >
              Change Password
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};
