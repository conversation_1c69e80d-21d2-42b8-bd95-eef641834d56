import React, { useMemo, useState, useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { Plus } from 'lucide-react';
import AnimatedTabs from '../../../components/ui/AnimatedTabs';
import AlertBanner from '../../DashboardPage/AnalyticsDashboard/TaskLogsPage/components/AlertBanner';
import { ROUTES } from '@/constants/routes';
import AgentsDropdown, { DropdownOption } from '@/components/ui/AgentsDropdown';
import { useGetUserProfile } from '@/hooks/useUserProfile';
import { MembersTab } from './MembersTab';
import { InvitesTab } from './InvitesTab';

export const MembersSettings: React.FC = () => {
  const navigate = useNavigate();

  const [searchParams, setSearchParams] = useSearchParams();
  const [membersSearchQuery, setMembersSearchQuery] = useState<string>('');
  const [invitesSearchQuery, setInvitesSearchQuery] = useState<string>('');

  const [isSuiteDropdownOpen, setIsSuiteDropdownOpen] = useState(false);
  const [currentSuite, setCurrentSuite] = useState<DropdownOption | undefined>(
    undefined
  );
  const { data: userData } = useGetUserProfile();

  // Get tab from URL params or default to 'members'
  const activeTab =
    (searchParams.get('tab') as 'members' | 'invites') || 'members';

  const suiteOptions = useMemo<DropdownOption[]>(() => {
    const claimedSuites = userData?.userInfo?.tenant?.claimedAgentSuites || [];
    return claimedSuites.map((suite) => ({
      id: suite.suite.agentSuiteKey,
      name: suite.suite.agentSuiteName,
      icon: suite.suite.avatar || '',
    }));
  }, [userData]);

  useEffect(() => {
    if (suiteOptions.length > 0 && !currentSuite) {
      setCurrentSuite(suiteOptions[0]);
    }
  }, [suiteOptions, currentSuite]);

  const agentSuiteKey = currentSuite?.id;
  const [alertMessage, setAlertMessage] = useState<string>('');
  const [alertType, setAlertType] = useState<'error' | 'success' | 'warning'>(
    'success'
  );

  // Initialize search queries from URL params
  useEffect(() => {
    const membersQuery = searchParams.get('msq') || '';
    const invitesQuery = searchParams.get('isq') || '';
    setMembersSearchQuery(membersQuery);
    setInvitesSearchQuery(invitesQuery);
  }, [searchParams]);

  const currentUserRole =
    userData?.userInfo?.tenant?.claimedAgentSuites?.[0]?.members?.find(
      (member) => member?.user?.userId === userData?.userInfo?.userId
    )?.memberRoles?.[0] || 'member';

  const canManageMembers =
    currentUserRole?.toLowerCase() === 'manager' ||
    currentUserRole?.toLowerCase() === 'lead';

  // Auto-select first suite if only one is available or if none is selected
  useEffect(() => {
    if (suiteOptions.length > 0 && !currentSuite) {
      setCurrentSuite(suiteOptions[0]);
    }
  }, [suiteOptions, currentSuite]);

  const showAlert = (
    message: string,
    type: 'error' | 'success' | 'warning'
  ) => {
    setAlertMessage(message);
    setAlertType(type);
    setTimeout(() => setAlertMessage(''), 5000);
  };

  const handleTabChange = (tabId: string) => {
    const newParams = new URLSearchParams(searchParams.toString());
    newParams.set('tab', tabId);
    setSearchParams(newParams);
  };

  const handleMembersSearchChange = (query: string) => {
    setMembersSearchQuery(query);
    const newParams = new URLSearchParams(searchParams.toString());
    if (query) {
      newParams.set('msq', query);
    } else {
      newParams.delete('msq');
    }
    setSearchParams(newParams);
  };

  const handleInvitesSearchChange = (query: string) => {
    setInvitesSearchQuery(query);
    const newParams = new URLSearchParams(searchParams.toString());
    if (query) {
      newParams.set('isq', query);
    } else {
      newParams.delete('isq');
    }
    setSearchParams(newParams);
  };

  const handleInviteMembers = () => {
    if (agentSuiteKey) {
      // Preserve current search params for return navigation
      const currentParams = searchParams.toString();
      navigate(ROUTES.DASHBOARD_SETTINGS_MEMBERS_INVITE, {
        state: { agentSuiteKey, returnParams: currentParams },
      });
    }
  };

  // Tab configuration
  const tabs = [
    {
      id: 'members',
      label: (
        <div className="flex gap-2 items-center">
          <span>Members</span>
        </div>
      ),
    },
    {
      id: 'invites',
      label: (
        <div className="flex gap-2 items-center">
          <span>Invites</span>
        </div>
      ),
    },
  ];

  return (
    <>
      <div className="flex relative flex-col gap-6">
        {/* Header */}
        <div className="flex gap-4 justify-between items-center">
          <div className="flex items-center space-x-6">
            <h2 className="font-bold md:text-lg text-blackOne">Members</h2>
            <div className="relative">
              <AgentsDropdown
                isOpen={isSuiteDropdownOpen}
                onToggle={() => setIsSuiteDropdownOpen(!isSuiteDropdownOpen)}
                currentItem={currentSuite}
                options={suiteOptions}
                onItemSelect={(suite) => {
                  setCurrentSuite(suite);
                  setIsSuiteDropdownOpen(false);
                }}
                placeholder="Select Suite"
                noOptionsMessage="No other suites available"
              />
            </div>
          </div>
          {canManageMembers && (
            <button
              onClick={handleInviteMembers}
              className="flex gap-2 items-center px-4 h-[42px] text-sm font-normal text-white rounded-md transition-colors bg-primary hover:bg-primary/90"
            >
              <Plus className="w-4 h-4" strokeWidth={3} /> Add Members
            </button>
          )}
        </div>

        {/* Tabs */}
        <AnimatedTabs
          tabs={tabs}
          activeTab={activeTab}
          onTabChange={handleTabChange}
          showContent={false}
        />
        <div className="flex relative flex-col gap-6">
          {/* Alert Banner - Under tabs */}
          {alertMessage && (
            <AlertBanner
              message={alertMessage}
              type={alertType}
              showIcon={false}
              width="lg"
              onClose={() => setAlertMessage('')}
            />
          )}

          {/* Tab Content */}
          {activeTab === 'members' && agentSuiteKey ? (
            <MembersTab
              agentSuiteKey={agentSuiteKey}
              searchQuery={membersSearchQuery}
              onSearchChange={handleMembersSearchChange}
              onShowAlert={showAlert}
            />
          ) : activeTab === 'invites' && agentSuiteKey ? (
            <InvitesTab
              agentSuiteKey={agentSuiteKey}
              searchQuery={invitesSearchQuery}
              onSearchChange={handleInvitesSearchChange}
              onShowAlert={showAlert}
            />
          ) : null}
        </div>
      </div>
    </>
  );
};
