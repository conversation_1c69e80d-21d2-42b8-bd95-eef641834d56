import { Plus } from 'lucide-react';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';

import { DashboardWithChatLayout } from '@/components/layout/DashboardWithChatLayout';
import AgentsDropdown, { DropdownOption } from '@/components/ui/AgentsDropdown';
import { ROUTES } from '@/constants/routes';
import { useTenant } from '@/context/TenantContext';
import { useGetAIAgentSuites } from '@/hooks/useAgents';
import { useGetUserProfile } from '@/hooks/useUserProfile';

import AnimatedTabs from '../../../components/ui/AnimatedTabs';
import AlertBanner from '../../DashboardPage/AnalyticsDashboard/TaskLogsPage/components/AlertBanner';
import { InvitesTab } from './InvitesTab';
import { MembersTab } from './MembersTab';

export const MembersSettings: React.FC = () => {
  const navigate = useNavigate();
  const { setActiveAgent } = useTenant();
  const reloadChatHistoryRef = useRef<(() => Promise<void>) | null>(null);

  const [searchParams, setSearchParams] = useSearchParams();
  const [membersSearchQuery, setMembersSearchQuery] = useState<string>('');
  const [invitesSearchQuery, setInvitesSearchQuery] = useState<string>('');

  const [isSuiteDropdownOpen, setIsSuiteDropdownOpen] = useState(false);
  const [currentSuite, setCurrentSuite] = useState<DropdownOption | undefined>(
    undefined
  );
  const { data: userData } = useGetUserProfile();
  const { data: allAgentSuites = [] } = useGetAIAgentSuites();

  // Get tab from URL params or default to 'members'
  const activeTab =
    (searchParams.get('tab') as 'members' | 'invites') || 'members';

  const suiteOptions = useMemo<DropdownOption[]>(() => {
    const claimedSuites = userData?.userInfo?.tenant?.claimedAgentSuites || [];

    // If user has claimed suites, use them
    if (claimedSuites.length > 0) {
      return claimedSuites.map(suite => ({
        id: suite.suite.agentSuiteKey,
        name: suite.suite.agentSuiteName,
        icon: suite.suite.avatar || '',
      }));
    }

    // Fallback to all available agent suites from API
    return allAgentSuites.map(suite => ({
      id: suite.agentSuiteKey,
      name: suite.agentSuiteName,
      icon: suite.avatar || '',
    }));
  }, [userData, allAgentSuites]);

  useEffect(() => {
    if (suiteOptions.length > 0 && !currentSuite) {
      setCurrentSuite(suiteOptions[0]);
    }
  }, [suiteOptions, currentSuite]);

  // Set active agent to Regis when component mounts
  useEffect(() => {
    // Small delay to ensure TenantContext is fully initialized
    const timer = setTimeout(() => {
      setActiveAgent('regis');
    }, 100);

    return () => clearTimeout(timer);
  }, [setActiveAgent]);

  const agentSuiteKey = currentSuite?.id;
  const [alertMessage, setAlertMessage] = useState<string>('');
  const [alertType, setAlertType] = useState<'error' | 'success' | 'warning'>(
    'success'
  );

  // Initialize search queries from URL params
  useEffect(() => {
    const membersQuery = searchParams.get('msq') || '';
    const invitesQuery = searchParams.get('isq') || '';
    setMembersSearchQuery(membersQuery);
    setInvitesSearchQuery(invitesQuery);
  }, [searchParams]);

  const currentUserRole =
    userData?.userInfo?.tenant?.claimedAgentSuites?.[0]?.members?.find(
      member => member?.user?.userId === userData?.userInfo?.userId
    )?.memberRoles?.[0] || 'member';

  const canManageMembers =
    currentUserRole?.toLowerCase() === 'manager' ||
    currentUserRole?.toLowerCase() === 'lead';

  // Auto-select first suite if only one is available or if none is selected
  useEffect(() => {
    if (suiteOptions.length > 0 && !currentSuite) {
      setCurrentSuite(suiteOptions[0]);
    }
  }, [suiteOptions, currentSuite]);

  const showAlert = (
    message: string,
    type: 'error' | 'success' | 'warning'
  ) => {
    setAlertMessage(message);
    setAlertType(type);
    setTimeout(() => setAlertMessage(''), 5000);
  };

  const handleTabChange = (tabId: string) => {
    const newParams = new URLSearchParams(searchParams.toString());
    newParams.set('tab', tabId);
    setSearchParams(newParams);
  };

  const handleMembersSearchChange = (query: string) => {
    setMembersSearchQuery(query);
    const newParams = new URLSearchParams(searchParams.toString());
    if (query) {
      newParams.set('msq', query);
    } else {
      newParams.delete('msq');
    }
    setSearchParams(newParams);
  };

  const handleInvitesSearchChange = (query: string) => {
    setInvitesSearchQuery(query);
    const newParams = new URLSearchParams(searchParams.toString());
    if (query) {
      newParams.set('isq', query);
    } else {
      newParams.delete('isq');
    }
    setSearchParams(newParams);
  };

  const handleInviteMembers = () => {
    if (agentSuiteKey) {
      // Preserve current search params for return navigation
      const currentParams = searchParams.toString();
      navigate(ROUTES.DASHBOARD_SETTINGS_MEMBERS_INVITE, {
        state: { agentSuiteKey, returnParams: currentParams },
      });
    }
  };

  // Tab configuration
  const tabs = [
    {
      id: 'members',
      label: (
        <div className="flex items-center gap-2">
          <span>Members</span>
        </div>
      ),
    },
    {
      id: 'invites',
      label: (
        <div className="flex items-center gap-2">
          <span>Invites</span>
        </div>
      ),
    },
  ];

  return (
    <DashboardWithChatLayout reloadChatHistoryRef={reloadChatHistoryRef}>
      <div className="relative flex flex-col gap-6 p-6">
        {/* Header */}
        <div className="flex items-center justify-between gap-4">
          <div className="flex items-center space-x-6">
            <h2 className="font-bold text-blackOne md:text-lg">Members</h2>
            <div className="relative">
              <AgentsDropdown
                isOpen={isSuiteDropdownOpen}
                onToggle={() => setIsSuiteDropdownOpen(!isSuiteDropdownOpen)}
                currentItem={currentSuite}
                options={suiteOptions}
                onItemSelect={suite => {
                  setCurrentSuite(suite);
                  setIsSuiteDropdownOpen(false);
                }}
                placeholder="Select Suite"
                noOptionsMessage="No other suites available"
              />
            </div>
          </div>
          {canManageMembers && (
            <button
              onClick={handleInviteMembers}
              className="flex h-[42px] items-center gap-2 rounded-md bg-primary px-4 text-sm font-normal text-white transition-colors hover:bg-primary/90"
            >
              <Plus className="h-4 w-4" strokeWidth={3} /> Add Members
            </button>
          )}
        </div>

        {/* Tabs */}
        <AnimatedTabs
          tabs={tabs}
          activeTab={activeTab}
          onTabChange={handleTabChange}
          showContent={false}
        />
        <div className="relative flex flex-col gap-6">
          {/* Alert Banner - Under tabs */}
          {alertMessage && (
            <AlertBanner
              message={alertMessage}
              type={alertType}
              showIcon={false}
              width="lg"
              onClose={() => setAlertMessage('')}
            />
          )}

          {/* Tab Content */}
          {activeTab === 'members' && agentSuiteKey ? (
            <MembersTab
              agentSuiteKey={agentSuiteKey}
              searchQuery={membersSearchQuery}
              onSearchChange={handleMembersSearchChange}
              onShowAlert={showAlert}
            />
          ) : activeTab === 'invites' && agentSuiteKey ? (
            <InvitesTab
              agentSuiteKey={agentSuiteKey}
              searchQuery={invitesSearchQuery}
              onSearchChange={handleInvitesSearchChange}
              onShowAlert={showAlert}
            />
          ) : null}
        </div>
      </div>
    </DashboardWithChatLayout>
  );
};
