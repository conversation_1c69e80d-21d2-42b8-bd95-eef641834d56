import React from 'react';
import { Loader2 } from 'lucide-react';
import { Invitation } from '../../../types/members';
import AnimatedModal from '../../../components/common/AnimatedModal';
import { Button } from '@/components/ui';

interface CancelInvitationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onCancel: (invitation: Invitation) => void;
  invitation: Invitation | null;
  loading?: boolean;
}

const CancelInvitationModal: React.FC<CancelInvitationModalProps> = ({
  isOpen,
  onClose,
  onCancel,
  invitation,
  loading = false,
}) => {
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!invitation) return;
    onCancel(invitation);
  };

  return (
    <AnimatedModal
      isOpen={isOpen && !!invitation}
      onClose={onClose}
      maxWidth="lg"
      showCloseButton={false}
    >
      <div className="p-6 h-[270px] gap-5 flex flex-col justify-center items-center text-center">
        <h3 className="text-lg font-semibold text-blackOne">
          Cancel invitation for {invitation?.firstname} {invitation?.lastname}?
        </h3>
        <p className="text-sm sm:text-base text-subText">
          This person will not be able to join your team unless you invite them
          again.
        </p>
        <form onSubmit={handleSubmit}>
          <div className="flex justify-center space-x-3">
            <Button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-sm font-medium text-white rounded-lg border bg-graySixteen hover:bg-graySixteen/80 border-graySixteen"
              disabled={loading}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              className="flex justify-center items-center px-4 py-2 text-sm font-medium text-white rounded-lg border border-delete bg-delete hover:bg-red-700 disabled:opacity-50"
              disabled={loading}
            >
              {loading ? (
                <>
                  <Loader2 className="w-4 h-4 animate-spin" />
                  <span className="ml-2">Cancelling...</span>
                </>
              ) : (
                'Cancel Invitation'
              )}
            </Button>
          </div>
        </form>
      </div>
    </AnimatedModal>
  );
};

export default CancelInvitationModal;