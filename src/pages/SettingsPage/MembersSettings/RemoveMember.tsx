import React from 'react';
import { Loader2 } from 'lucide-react';
import { Member, MemberRole } from '../../../types/members';
import AnimatedModal from '../../../components/common/AnimatedModal';
import { Button } from '@/components/ui';

interface RemoveMemberModalProps {
  isOpen: boolean;
  onClose: () => void;
  onRemoveMember: (memberId: string) => void;
  member: {
    id: string;
    name: string;
    role: MemberRole;
  } | null;
  loading?: boolean;
}

const RemoveMemberModal: React.FC<RemoveMemberModalProps> = ({
  isOpen,
  onClose,
  onRemoveMember,
  member,
  loading = false,
}) => {
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!member) return;
    onRemoveMember(member.id);
  };

  return (
    <AnimatedModal
      isOpen={isOpen && !!member}
      onClose={onClose}
      maxWidth="xl"
      showCloseButton={false}
    >
      <div className="p-6 h-[300px] gap-5 flex flex-col justify-center items-center text-center">
        
        {/* Title */}
          <h3 className="text-lg font-semibold text-blackOne">
            Remove {member?.name}?
          </h3>
        
        {/* Description */}
          <p className="text-sm sm:text-base text-subText">
            {member?.name} will no longer have access to SetIQ's features and dashboards.
          </p>

        {/* Actions */}
        <form onSubmit={handleSubmit} >
          <div className="flex justify-center space-x-3">
            <Button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-sm font-medium text-white rounded-lg border bg-graySixteen hover:bg-graySixteen/80 border-graySixteen"
              disabled={loading}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              className="flex justify-center items-center px-4 py-2 text-sm font-medium text-white rounded-lg border border-delete bg-delete hover:bg-red-700 disabled:opacity-50"
              disabled={loading}
            >
              {loading ? (
                <>
                  <Loader2 className="w-4 h-4 animate-spin" />
                  <span className="ml-2">Removing...</span>
                </>
              ) : (
                'Remove Member'
              )}
            </Button>
          </div>
        </form>
      </div>
    </AnimatedModal>
  );
};

export default RemoveMemberModal;