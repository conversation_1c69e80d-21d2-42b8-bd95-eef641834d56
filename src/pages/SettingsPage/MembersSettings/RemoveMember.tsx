import { Loader2 } from 'lucide-react';
import React from 'react';

import { Button } from '@/components/ui';

import AnimatedModal from '../../../components/common/AnimatedModal';
import { MemberRole } from '../../../types/members';

interface RemoveMemberModalProps {
  isOpen: boolean;
  onClose: () => void;
  onRemoveMember: (memberId: string) => void;
  member: {
    id: string;
    name: string;
    role: MemberRole;
  } | null;
  loading?: boolean;
}

const RemoveMemberModal: React.FC<RemoveMemberModalProps> = ({
  isOpen,
  onClose,
  onRemoveMember,
  member,
  loading = false,
}) => {
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!member) return;
    onRemoveMember(member.id);
  };

  return (
    <AnimatedModal
      isOpen={isOpen && !!member}
      onClose={onClose}
      maxWidth="xl"
      showCloseButton={false}
    >
      <div className="flex h-[300px] flex-col items-center justify-center gap-5 p-6 text-center">
        {/* Title */}
        <h3 className="text-lg font-semibold text-blackOne">
          Remove {member?.name}?
        </h3>

        {/* Description */}
        <p className="text-sm text-subText sm:text-base">
          {member?.name} will no longer have access to SetIQ's features and
          dashboards.
        </p>

        {/* Actions */}
        <form onSubmit={handleSubmit}>
          <div className="flex justify-center space-x-3">
            <Button
              type="button"
              onClick={onClose}
              className="hover:bg-graySixteen/80 rounded-lg border border-graySixteen bg-graySixteen px-4 py-2 text-sm font-medium text-white"
              disabled={loading}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              className="flex items-center justify-center rounded-lg border border-delete bg-delete px-4 py-2 text-sm font-medium text-white hover:bg-red-700 disabled:opacity-50"
              disabled={loading}
            >
              {loading ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin" />
                  <span className="ml-2">Removing...</span>
                </>
              ) : (
                'Remove Member'
              )}
            </Button>
          </div>
        </form>
      </div>
    </AnimatedModal>
  );
};

export default RemoveMemberModal;
