import { Loader2, Search } from 'lucide-react';
import React, { useState } from 'react';

import Pagination from '@/components/common/Pagination';
import { Input } from '@/components/ui';
import { useDebounce } from '@/hooks/useDebounce';
import {
  useCancelSuiteInviteMutation,
  useInviteSuiteMemberMutation,
  useSuiteInvites,
  useUpdateSuiteInviteRoleMutation,
} from '@/hooks/useMembers';
import { useGetUserProfile } from '@/hooks/useUserProfile';

import ActionDropdown from '../../../components/common/ActionDropdown';
import DataTable, { Column } from '../../../components/ui/tables/DataTable';
import {
  Invitation,
  mapUiRoleToApiRole,
  MemberRole,
  SuiteMemberRoleApi,
} from '../../../types/members';
import {
  generateMemberInitials,
  getRoleColor,
  getRoleDisplayName,
} from '../../../utils/members';
import CancelInvitationModal from './CancelInvitation';
import UpdateRoleModal from './UpdateRole';

interface InvitesTabProps {
  agentSuiteKey: string;
  searchQuery: string;
  onSearchChange: (query: string) => void;
  onShowAlert: (message: string, type: 'error' | 'success' | 'warning') => void;
}

export const InvitesTab: React.FC<InvitesTabProps> = ({
  agentSuiteKey,
  searchQuery,
  onSearchChange,
  onShowAlert,
}) => {
  const [page, setPage] = useState<number>(1);
  const [pageSize] = useState<number>(10);
  const debouncedSearchQuery = useDebounce(searchQuery, 500);
  const resendInvitationMutation = useInviteSuiteMemberMutation();

  const [isUpdateRoleModalOpen, setIsUpdateRoleModalOpen] = useState(false);
  const [isCancelInvitationModalOpen, setIsCancelInvitationModalOpen] =
    useState(false);
  const [selectedInvitation, setSelectedInvitation] =
    useState<Invitation | null>(null);

  const { data: userData } = useGetUserProfile();

  const { data: invitationsData, isLoading: isLoadingInvitations } =
    useSuiteInvites(
      agentSuiteKey,
      debouncedSearchQuery,
      page,
      pageSize,
      !!agentSuiteKey
    );

  const invitations = invitationsData?.invites || [];

  const updateInvitationRoleMutation = useUpdateSuiteInviteRoleMutation();
  const cancelInvitationMutation = useCancelSuiteInviteMutation();

  const handleUpdateInvitationRole = async (
    invitationId: string,
    newRole: MemberRole
  ) => {
    try {
      await updateInvitationRoleMutation.mutateAsync({
        inviteId: invitationId,
        payload: {
          agentSuiteKey: agentSuiteKey,
          memberRole: mapUiRoleToApiRole(newRole),
        },
      });
      onShowAlert('Role updated successfully', 'success');
      setIsUpdateRoleModalOpen(false);
      setSelectedInvitation(null);
    } catch (error: any) {
      const errorMessage =
        error?.response?.data?.message || 'Failed to update role';
      onShowAlert(errorMessage, 'error');
      setIsUpdateRoleModalOpen(false);
      setSelectedInvitation(null);
    }
  };

  const handleCancelInvitation = async (invitation: Invitation) => {
    try {
      await cancelInvitationMutation.mutateAsync({
        inviteId: invitation.id,
        agentSuiteKey: agentSuiteKey,
      });
      onShowAlert(
        `Invitation to ${invitation.email} has been cancelled`,
        'success'
      );
    } catch (error: any) {
      const errorMessage =
        error?.response?.data?.message || 'Failed to cancel invitation';
      onShowAlert(errorMessage, 'error');
    } finally {
      setIsCancelInvitationModalOpen(false);
      setSelectedInvitation(null);
    }
  };

  const resendInvitation = async (invitation: Invitation) => {
    try {
      await resendInvitationMutation.mutateAsync({
        agentSuiteKey: agentSuiteKey,
        firstname: invitation.firstname.trim(),
        lastname: invitation.lastname.trim(),
        email: invitation.email.trim(),
        role: invitation.role as SuiteMemberRoleApi,
      });
    } catch (error: any) {
      const errorMessage =
        error?.response?.data?.message || 'Failed to resend invitation';
      onShowAlert(errorMessage, 'error');
    }
  };

  // Invitation columns
  const invitationColumns: Column<Invitation>[] = [
    {
      key: 'email',
      label: 'Name',
      render: (_, invitation) => (
        <div className="flex items-center">
          <div className="mr-3 flex h-10 w-10 items-center justify-center rounded-full bg-blackOne text-sm font-medium text-white">
            {generateMemberInitials(invitation.firstname)}
            {generateMemberInitials(invitation.lastname)}
          </div>
          <div>
            <div className="font-medium text-blackOne">
              {invitation.firstname} {invitation.lastname}
            </div>
            <div className="text-sm text-subText">{invitation.email}</div>
          </div>
        </div>
      ),
    },
    {
      key: 'role',
      label: 'Role',
      render: (_, invitation) => (
        <span
          className={`flex h-[34px] w-28 items-center justify-center rounded-lg px-4 text-xs font-medium ${
            getRoleColor(invitation.role) || 'bg-gray-30 text-white'
          }`}
        >
          {getRoleDisplayName(invitation.role) || 'N/A'}
        </span>
      ),
    },
    {
      key: 'id',
      label: '',
      render: (_, invitation) => {
        const actions = [
          {
            label: 'Change Role',
            icon: null,
            onClick: () => {
              setSelectedInvitation(invitation);
              setIsUpdateRoleModalOpen(true);
            },
            variant: 'default' as const,
          },
          {
            label: resendInvitationMutation.isPending
              ? 'Sending...'
              : resendInvitationMutation.isSuccess
                ? (() => {
                    // Show "Invitation Sent" for 3 seconds, then revert to "Resend Invitation"
                    if (resendInvitationMutation.isSuccess) {
                      setTimeout(() => {
                        resendInvitationMutation.reset &&
                          resendInvitationMutation.reset();
                      }, 3000);
                      return 'Invitation Sent';
                    }
                    return 'Invitation Sent';
                  })()
                : 'Resend Invitation',
            icon: resendInvitationMutation.isPending ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : null,
            onClick: () => {
              resendInvitation(invitation);
            },

            isDisabled: resendInvitationMutation.isPending,
            isLoading: resendInvitationMutation.isPending,
            variant: (() => {
              // Show "Invitation Sent" for 3 seconds, then revert to "Resend Invitation"
              if (resendInvitationMutation.isSuccess) {
                setTimeout(() => {
                  resendInvitationMutation.reset &&
                    resendInvitationMutation.reset();
                }, 3000);
                return 'success' as const;
              }
              return 'default' as const;
            })(),
            closeOnClick: false,
          },
          {
            label: 'Cancel Invitation',
            icon: null,
            onClick: () => {
              setSelectedInvitation(invitation);
              setIsCancelInvitationModalOpen(true);
            },
            variant: 'danger' as const,
          },
        ];

        const currentUserRole =
          userData?.userInfo?.tenant?.claimedAgentSuites
            ?.find(suite => suite?.suite?.agentSuiteKey === agentSuiteKey)
            ?.members?.find(m => m?.user?.userId === userData?.userInfo?.userId)
            ?.memberRoles?.[0]?.toLowerCase() || 'member';

        const canManageMembers =
          currentUserRole === 'manager' || currentUserRole === 'lead';

        return canManageMembers ? <ActionDropdown actions={actions} /> : null;
      },
      className: 'text-right',
    },
  ];

  return (
    <>
      <div className="space-y-6">
        {/* Search */}
        <div className="flex items-center gap-3">
          <Input
            type="text"
            placeholder="Search by name or email"
            className="h-10 w-[300px] rounded-[10px] border border-[#BAB9B9] py-2 pl-4 pr-10 text-sm placeholder:text-grayTen focus:outline-none focus:ring-0"
            endIcon={<Search className="mt-0.5 h-4 w-4" />}
            value={searchQuery}
            onChange={e => onSearchChange(e.target.value)}
          />
        </div>

        {/* Table */}
        <div className="flex flex-col gap-4 overflow-x-auto">
          <DataTable
            data={invitations || []}
            columns={invitationColumns}
            loading={isLoadingInvitations}
            emptyMessage="No invitations found"
            rowColoring={true}
            rowColoringType="odd"
            getRowId={invitation => invitation.id}
          />
          {!isLoadingInvitations && invitations && invitations?.length > 0 && (
            <Pagination
              currentPage={page}
              totalPages={
                invitationsData
                  ? Math.ceil(invitationsData.total / invitationsData.pageSize)
                  : 0
              }
              onPageChange={page => setPage(page)}
            />
          )}
        </div>
      </div>

      {/* Modals */}
      <UpdateRoleModal
        isOpen={isUpdateRoleModalOpen}
        onClose={() => {
          setIsUpdateRoleModalOpen(false);
          setSelectedInvitation(null);
        }}
        onUpdateRole={(newRole: MemberRole) => {
          handleUpdateInvitationRole(selectedInvitation?.id || '', newRole);
        }}
        userDetails={
          selectedInvitation
            ? {
                id: selectedInvitation.id,
                name: `${selectedInvitation.firstname} ${selectedInvitation.lastname}`,
                role: selectedInvitation.role.toLowerCase() as MemberRole,
              }
            : null
        }
        loading={updateInvitationRoleMutation.isPending}
      />

      <CancelInvitationModal
        isOpen={isCancelInvitationModalOpen}
        onClose={() => {
          setIsCancelInvitationModalOpen(false);
          setSelectedInvitation(null);
        }}
        onCancel={handleCancelInvitation}
        invitation={selectedInvitation}
        loading={cancelInvitationMutation.isPending}
      />
    </>
  );
};
