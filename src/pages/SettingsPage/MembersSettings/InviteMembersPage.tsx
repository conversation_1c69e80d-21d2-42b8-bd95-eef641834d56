import React, { useEffect, useState } from 'react';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import { useForm, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { ChevronRight, Shield } from 'lucide-react';
import {
  MemberRole,
  ROLE_DESCRIPTIONS,
  mapUiRoleToApiRole,
} from '../../../types/members';
import AlertBanner from '../../DashboardPage/AnalyticsDashboard/TaskLogsPage/components/AlertBanner';
import { Input } from '@/components/ui';
import { useInviteSuiteMemberMutation } from '@/hooks/useMembers';
import { ROUTES } from '@/constants/routes';
import { useGetUserProfile } from '@/hooks/useUserProfile';

interface InviteMemberFormData {
  firstName: string;
  lastName: string;
  email: string;
  role: MemberRole;
}

const inviteMemberSchema = yup.object().shape({
  firstName: yup.string().required('First name is required'),
  lastName: yup.string().required('Last name is required'),
  email: yup
    .string()
    .email('Enter a valid email')
    .required('Email is required'),
  role: yup
    .string()
    .required('Role is required') as yup.StringSchema<MemberRole>,
});

const InviteMembersPage: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [selectedRole, setSelectedRole] = useState<MemberRole>('member');

  const agentSuiteKey = location.state?.agentSuiteKey;
  const returnParams = location.state?.returnParams || '';

  const {
    control,
    handleSubmit,
    reset,
    formState: { errors, isValid },
  } = useForm<InviteMemberFormData>({
    resolver: yupResolver(inviteMemberSchema),
    defaultValues: {
      firstName: '',
      lastName: '',
      email: '',
      role: 'member',
    },
    mode: 'onChange',
  });
  const { data: userData, isLoading: isLoadingUser } = useGetUserProfile();

  const inviteMutation = useInviteSuiteMemberMutation();

  const [alertMessage, setAlertMessage] = useState<string>('');
  const [alertType, setAlertType] = useState<'error' | 'success'>('success');

  const showAlert = (message: string, type: 'error' | 'success') => {
    setAlertMessage(message);
    setAlertType(type);
    setTimeout(() => setAlertMessage(''), 5000);
  };

  const onSubmit = async (data: InviteMemberFormData) => {
    if (!agentSuiteKey) {
      showAlert('Agent Suite key is missing.', 'error');
      return;
    }
    
    // Validate all required fields
    if (!data.firstName?.trim() || !data.lastName?.trim() || !data.email?.trim()) {
      showAlert('Please fill in all required fields.', 'error');
      return;
    }
    
    try {
      await inviteMutation.mutateAsync({
        agentSuiteKey: agentSuiteKey,
        firstname: data.firstName.trim(),
        lastname: data.lastName.trim(),
        email: data.email.trim(),
        role: mapUiRoleToApiRole(selectedRole),
      });
      showAlert(`Invitation sent successfully to ${data.email}`, 'success');
      // Clear form after successful submission
      reset();
      setSelectedRole('member');
      // Navigate back with preserved search params
      setTimeout(() => {
        const returnUrl = returnParams 
          ? `${ROUTES.DASHBOARD_SETTINGS_MEMBERS}?${returnParams}`
          : ROUTES.DASHBOARD_SETTINGS_MEMBERS;
        navigate(returnUrl);
      }, 1500);
    } catch (error: any) {
      const errorMessage = error?.response?.data?.message || 'Failed to send invitation. Please try again.';
      showAlert(errorMessage, 'error');
    }
  };

  const handleCancel = () => {
    navigate(-1);
  };

  // Get current user's role in the selected suite (for now using first suite, could be enhanced)
  const currentUserRole =
    userData?.userInfo?.tenant?.claimedAgentSuites?.[0]?.members?.find(
      (member) => member?.user?.userId === userData?.userInfo?.userId
    )?.memberRoles?.[0] || 'member';

  // Check if user can manage members (manager or lead roles)
  const canManageMembers =
    currentUserRole?.toLowerCase() === 'manager' ||
    currentUserRole?.toLowerCase() === 'lead';
  // Redirect if user doesn't have permission
  useEffect(() => {
    if (!isLoadingUser && userData && !canManageMembers) {
      // Show alert and redirect to members page
      navigate(ROUTES.DASHBOARD_SETTINGS_MEMBERS, {
        replace: true,
        state: {
          error:
            'You do not have permission to invite members. Only Managers and Leads can invite new members.',
        },
      });
    }
  }, [isLoadingUser, userData, canManageMembers, navigate]);
  // Show loading state while checking permissions
  if (isLoadingUser) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="text-center">
          <div className="mx-auto mb-4 w-8 h-8 rounded-full border-4 animate-spin border-primary/20 border-t-primary"></div>
          <p className="text-subText">Loading...</p>
        </div>
      </div>
    );
  }

  // Show unauthorized access message if user doesn't have permission
  if (!isLoadingUser && userData && !canManageMembers) {
    return (
      <div className="flex flex-col justify-center items-center h-64 text-center">
        <Shield className="mb-4 w-16 h-16 text-gray-400" />
        <h2 className="mb-2 text-xl font-semibold text-blackOne">
          Access Restricted
        </h2>
        <p className="mb-4 max-w-md text-subText">
          You do not have permission to invite members. Only Managers and Leads
          can invite new members.
        </p>
        <button
          onClick={() => navigate(ROUTES.DASHBOARD_SETTINGS_MEMBERS)}
          className="px-4 py-2 text-white rounded-md transition-colors bg-primary hover:bg-primary/90"
        >
          Back to Members
        </button>
      </div>
    );
  }

  return (
    <div className="overflow-x-hidden relative">
      {/* Alert Banner */}
      {alertMessage && (
        <AlertBanner
          message={alertMessage}
          type={alertType}
          onClose={() => setAlertMessage('')}
          width="lg"
        />
      )}

      <div className="flex flex-col gap-12 max-w-[483px]">
        {/* Header */}

        <div className="flex flex-col gap-4">
          <h1 className="text-lg font-bold text-subText">Members</h1>
          <div className="flex items-center space-x-1">
            <Link
              to="/dashboard/settings/members"
              className="text-sm font-medium underline text-[#FF6636] cursor-pointer"
            >
              Members
            </Link>
            <ChevronRight className="w-4 h-4 text-subText" strokeWidth={3} />
            <h3 className="text-sm font-semibold text-subText">
              Invite Members
            </h3>
          </div>
          {/* Description */}
          <p className="text-sm text-subText">
            Invite members to collaborate in your organization's Agent Suites.
            (Access limited to users with your email domain)
          </p>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          <Controller
            name="firstName"
            control={control}
            render={({ field }) => (
              <Input
                {...field}
                label="First name"
                placeholder="First name"
                error={errors.firstName?.message}
                fullWidth
              />
            )}
          />

          <Controller
            name="lastName"
            control={control}
            render={({ field }) => (
              <Input
                {...field}
                label="Last name"
                placeholder="Last name"
                error={errors.lastName?.message}
                fullWidth
              />
            )}
          />

          <Controller
            name="email"
            control={control}
            render={({ field }) => (
              <Input
                {...field}
                label="Email address"
                placeholder="Email address"
                error={errors.email?.message}
                fullWidth
              />
            )}
          />

          {/* Role Selection */}
          <Controller
            name="role"
            control={control}
            render={({ field }) => (
              <div className="space-y-4">
                <label className="block text-sm font-medium text-blackOne">
                  Assign Role
                </label>
                <div className="space-y-3">
                  <label className="flex items-start space-x-3 cursor-pointer">
                    <input
                      type="radio"
                      name="role"
                      value="manager"
                      checked={selectedRole === 'manager'}
                      onChange={(e) => {
                        const role = e.target.value as MemberRole;
                        setSelectedRole(role);
                        field.onChange(role);
                      }}
                      className="mt-1 h-4 w-4 text-primary focus:ring-primary border-[#D0D0D0] bg-white"
                    />
                    <div className="text-sm text-subText">
                      {ROLE_DESCRIPTIONS.manager}
                    </div>
                  </label>

                  <label className="flex items-start space-x-3 cursor-pointer">
                    <input
                      type="radio"
                      name="role"
                      value="lead"
                      checked={selectedRole === 'lead'}
                      onChange={(e) => {
                        const role = e.target.value as MemberRole;
                        setSelectedRole(role);
                        field.onChange(role);
                      }}
                      className="mt-1 h-4 w-4 text-primary focus:ring-primary border-[#D0D0D0] bg-white"
                    />
                    <div className="text-sm text-subText">
                      {ROLE_DESCRIPTIONS.lead}
                    </div>
                  </label>

                  <label className="flex items-start space-x-3 cursor-pointer">
                    <input
                      type="radio"
                      name="role"
                      value="member"
                      checked={selectedRole === 'member'}
                      onChange={(e) => {
                        const role = e.target.value as MemberRole;
                        setSelectedRole(role);
                        field.onChange(role);
                      }}
                      className="mt-1 h-4 w-4 text-primary focus:ring-primary border-[#D0D0D0] bg-white"
                    />
                    <div className="text-sm text-subText">
                      {ROLE_DESCRIPTIONS.member}
                    </div>
                  </label>
                </div>
                {errors.role && (
                  <p className="text-sm text-red-600">{errors.role.message}</p>
                )}
              </div>
            )}
          />

          {/* Actions */}
          <div className="flex justify-start items-center space-x-3">
            <button
              type="button"
              onClick={handleCancel}
              className="rounded-lg bg-[#F5F5F5] px-4 py-2.5 text-sm font-medium text-subText hover:bg-[#E5E5E5] transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="flex items-center gap-2 rounded-lg bg-primary px-4 py-2.5 text-sm font-medium text-white hover:bg-primary/90 transition-colors disabled:opacity-50"
              disabled={inviteMutation.isPending || !isValid}
            >
              {inviteMutation.isPending ? (
                <>
                  <div className="w-4 h-4 rounded-full border-2 animate-spin border-white/20 border-t-white"></div>
                  Inviting...
                </>
              ) : (
                'Invite'
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default InviteMembersPage;
