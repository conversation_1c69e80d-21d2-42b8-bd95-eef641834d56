import React, { useState } from 'react';
import { Search } from 'lucide-react';
import {
  Member,
  MemberRole,
  mapApiRoleToUiRole,
  mapUiRoleToApiRole,
} from '../../../types/members';
import {
  getRoleColor,
  getRoleDisplayName,
  formatLastLogin,
  generateMemberInitials,
} from '../../../utils/members';
import DataTable, { Column } from '../../../components/ui/tables/DataTable';
import ActionDropdown from '../../../components/common/ActionDropdown';
import UpdateRoleModal from './UpdateRole';
import RemoveMemberModal from './RemoveMember';
import { Input } from '@/components/ui';
import {
  useSuiteMembers,
  useUpdateSuiteMemberRoleMutation,
  useRemoveSuiteMemberMutation,
} from '@/hooks/useMembers';
import { useGetUserProfile } from '@/hooks/useUserProfile';
import { useDebounce } from '@/hooks/useDebounce';
import Pagination from '@/components/common/Pagination';

interface MembersTabProps {
  agentSuiteKey: string;
  searchQuery: string;
  onSearchChange: (query: string) => void;
  onShowAlert: (message: string, type: 'error' | 'success' | 'warning') => void;
}

export const MembersTab: React.FC<MembersTabProps> = ({
  agentSuiteKey,
  searchQuery,
  onSearchChange,
  onShowAlert,
}) => {
  const [page, setPage] = useState<number>(1);
  const [pageSize] = useState<number>(10);
  const debouncedSearchQuery = useDebounce(searchQuery, 500);

  const [isUpdateRoleModalOpen, setIsUpdateRoleModalOpen] = useState(false);
  const [isRemoveMemberModalOpen, setIsRemoveMemberModalOpen] = useState(false);
  const [selectedMember, setSelectedMember] = useState<{
    id: string;
    name: string;
    role: MemberRole;
  } | null>(null);

  const { data: userData } = useGetUserProfile();

  const { data: membersData, isLoading: isLoadingMembers } = useSuiteMembers(
    agentSuiteKey,
    debouncedSearchQuery,
    page,
    pageSize,
    !!agentSuiteKey
  );

  const members = membersData?.members || [];

  const updateRoleMutation = useUpdateSuiteMemberRoleMutation();
  const removeMemberMutation = useRemoveSuiteMemberMutation();

  const handleUpdateRole = async (memberId: string, newRole: MemberRole) => {
    try {
      await updateRoleMutation.mutateAsync({
        memberId,
        payload: {
          agentSuiteKey: agentSuiteKey,
          memberRole: mapUiRoleToApiRole(newRole),
        },
      });
      onShowAlert('Role updated successfully', 'success');
      setIsUpdateRoleModalOpen(false);
      setSelectedMember(null);
    } catch (error: any) {
      const errorMessage = error?.response?.data?.message || 'Failed to update role';
      onShowAlert(errorMessage, 'error');
      setIsUpdateRoleModalOpen(false);
      setSelectedMember(null);
    }
  };

  const handleRemoveMember = async (memberId: string) => {
    try {
      await removeMemberMutation.mutateAsync({
        memberId,
        agentSuiteKey: agentSuiteKey,
      });
      onShowAlert('Member removed successfully', 'success');
    } catch (error: any) {
      const errorMessage = error?.response?.data?.message || 'Failed to remove member';
      onShowAlert(errorMessage, 'error');
    } finally {
      setIsRemoveMemberModalOpen(false);
      setSelectedMember(null);
    }
  };

  const memberColumns: Column<Member>[] = [
    {
      key: 'user',
      label: 'Name',
      render: (_, member) => (
        <div className="flex items-center">
          <div className="flex justify-center items-center mr-3 w-10 h-10 text-sm font-medium text-white rounded-full bg-blackOne">
            {generateMemberInitials(
              `${member.user.firstName} ${member.user.lastName}`
            )}
          </div>
          <div>
            <div className="font-medium text-blackOne">{`${member.user.firstName} ${member.user.lastName}`}</div>
            <div className="text-sm text-subText">{member.user.email}</div>
          </div>
        </div>
      ),
    },
    {
      key: 'memberRoles',
      label: 'Role',
      render: (_, member) => {
        const role = mapApiRoleToUiRole(member.memberRoles[0]);
        return (
          <span
            className={`flex justify-center items-center px-4 w-28 text-xs font-medium rounded-lg h-[34px] ${getRoleColor(role)}`}
          >
            {getRoleDisplayName(role)}
          </span>
        );
      },
    },
    {
      key: 'claimedAgentSuite',
      label: 'Last Login',
      render: (_, member) => (
        <span className="text-sm text-subText">
          {formatLastLogin(member.user.lastActivity) || '--'}{' '}
        </span>
      ),
    },
    {
      key: 'id',
      label: '',
      render: (_, member) => {
        const actions = [
          {
            label: 'Change Role',
            icon: null,
            onClick: () => {
              setSelectedMember({
                id: member.id,
                name: `${member.user.firstName} ${member.user.lastName}`,
                role: member.memberRoles[0] as MemberRole,
              });
              setIsUpdateRoleModalOpen(true);
            },
            variant: 'default' as const,
          },
          {
            label: 'Remove Member',
            icon: null,
            onClick: () => {
              setSelectedMember({
                id: member.id,
                name: `${member.user.firstName} ${member.user.lastName}`,
                role: member.memberRoles[0] as MemberRole,
              });
              setIsRemoveMemberModalOpen(true);
            },
            variant: 'danger' as const,
          },
        ];

        const currentUserRole = userData?.userInfo?.tenant?.claimedAgentSuites?.find(
          (suite) => suite?.suite?.agentSuiteKey === agentSuiteKey
        )?.members?.find(
          (m) => m?.user?.userId === userData?.userInfo?.userId
        )?.memberRoles?.[0]?.toLowerCase() || 'member';

        const canManageMembers = currentUserRole === 'manager' || currentUserRole === 'lead';
        
        return canManageMembers ? <ActionDropdown actions={actions} /> : null;
      },
      className: 'text-right',
    },
  ];

  return (
    <>
      <div className="space-y-6">
        {/* Search */}
        <div className="flex gap-3 items-center">
          <Input
            type="text"
            placeholder="Search by name or email"
            className="py-2 pr-10 pl-4 w-[300px] h-10 text-sm rounded-[10px] border border-[#BAB9B9] placeholder:text-grayTen focus:outline-none focus:ring-0"
            endIcon={<Search className="mt-0.5 w-4 h-4" />}
            value={searchQuery}
            onChange={(e) => onSearchChange(e.target.value)}
          />
        </div>

        {/* Table */}
        <div className="flex overflow-x-auto flex-col gap-4">
          <DataTable
            data={members || []}
            columns={memberColumns}
            loading={isLoadingMembers}
            emptyMessage="No members found"
            rowColoring={true}
            rowColoringType="odd"
            getRowId={(member) => member.id}
          />
          <Pagination
            currentPage={page}
            totalPages={
              membersData
                ? Math.ceil(membersData.total / membersData.pageSize)
                : 0
            }
            onPageChange={(page) => setPage(page)}
          />
        </div>
      </div>

      {/* Modals */}
      <UpdateRoleModal
        isOpen={isUpdateRoleModalOpen}
        onClose={() => {
          setIsUpdateRoleModalOpen(false);
          setSelectedMember(null);
        }}
        onUpdateRole={(newRole: MemberRole) => {
          handleUpdateRole(selectedMember?.id || '', newRole);
        }}
        userDetails={selectedMember}
        loading={updateRoleMutation.isPending}
      />

      <RemoveMemberModal
        isOpen={isRemoveMemberModalOpen}
        onClose={() => {
          setIsRemoveMemberModalOpen(false);
          setSelectedMember(null);
        }}
        onRemoveMember={(memberId) => handleRemoveMember(memberId)}
        member={selectedMember}
        loading={removeMemberMutation.isPending}
      />
    </>
  );
};