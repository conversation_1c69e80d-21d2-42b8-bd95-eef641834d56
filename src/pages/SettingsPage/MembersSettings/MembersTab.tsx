import clsx from 'clsx';
import { Search } from 'lucide-react';
import React, { useState } from 'react';

import Pagination from '@/components/common/Pagination';
import { Input } from '@/components/ui';
import { useDebounce } from '@/hooks/useDebounce';
import {
  useRemoveSuiteMemberMutation,
  useSuiteMembers,
  useUpdateSuiteMemberRoleMutation,
} from '@/hooks/useMembers';
import { useGetUserProfile } from '@/hooks/useUserProfile';

import ActionDropdown from '../../../components/common/ActionDropdown';
import DataTable, { Column } from '../../../components/ui/tables/DataTable';
import {
  mapApiRoleToUiRole,
  mapUiRoleToApiRole,
  Member,
  MemberRole,
} from '../../../types/members';
import {
  formatLastLogin,
  generateMemberInitials,
  getRoleColor,
  getRoleDisplayName,
} from '../../../utils/members';
import RemoveMemberModal from './RemoveMember';
import UpdateRoleModal from './UpdateRole';

interface MembersTabProps {
  agentSuiteKey: string;
  searchQuery: string;
  onSearchChange: (query: string) => void;
  onShowAlert: (message: string, type: 'error' | 'success' | 'warning') => void;
}

export const MembersTab: React.FC<MembersTabProps> = ({
  agentSuiteKey,
  searchQuery,
  onSearchChange,
  onShowAlert,
}) => {
  const [page, setPage] = useState<number>(1);
  const [pageSize] = useState<number>(10);
  const debouncedSearchQuery = useDebounce(searchQuery, 500);

  const [isUpdateRoleModalOpen, setIsUpdateRoleModalOpen] = useState(false);
  const [isRemoveMemberModalOpen, setIsRemoveMemberModalOpen] = useState(false);
  const [selectedMember, setSelectedMember] = useState<{
    id: string;
    name: string;
    role: MemberRole;
  } | null>(null);

  const { data: userData } = useGetUserProfile();

  const { data: membersData, isLoading: isLoadingMembers } = useSuiteMembers(
    agentSuiteKey,
    debouncedSearchQuery,
    page,
    pageSize,
    !!agentSuiteKey
  );

  const members = membersData?.members || [];

  const updateRoleMutation = useUpdateSuiteMemberRoleMutation();
  const removeMemberMutation = useRemoveSuiteMemberMutation();

  const handleUpdateRole = async (memberId: string, newRole: MemberRole) => {
    try {
      await updateRoleMutation.mutateAsync({
        memberId,
        payload: {
          agentSuiteKey: agentSuiteKey,
          memberRole: mapUiRoleToApiRole(newRole),
        },
      });
      onShowAlert('Role updated successfully', 'success');
      setIsUpdateRoleModalOpen(false);
      setSelectedMember(null);
    } catch (error) {
      const err = error as { response?: { data?: { message?: string } } };
      const errorMessage =
        err?.response?.data?.message || 'Failed to update role';
      onShowAlert(errorMessage, 'error');
      setIsUpdateRoleModalOpen(false);
      setSelectedMember(null);
    }
  };

  const handleRemoveMember = async (memberId: string) => {
    try {
      await removeMemberMutation.mutateAsync({
        memberId,
        agentSuiteKey: agentSuiteKey,
      });
      onShowAlert('Member removed successfully', 'success');
    } catch (error) {
      const err = error as { response?: { data?: { message?: string } } };
      const errorMessage =
        err?.response?.data?.message || 'Failed to remove member';
      onShowAlert(errorMessage, 'error');
    } finally {
      setIsRemoveMemberModalOpen(false);
      setSelectedMember(null);
    }
  };

  const memberColumns: Column<Member>[] = [
    {
      key: 'user',
      label: 'Name',
      render: (_, member) => (
        <div className="flex items-center">
          <div className="mr-3 flex h-10 w-10 items-center justify-center rounded-full bg-blackOne text-sm font-medium text-white">
            {generateMemberInitials(
              `${member?.user?.firstName} ${member?.user?.lastName}`
            )}
          </div>
          <div>
            <div className="font-medium text-blackOne">{`${member.user.firstName} ${member.user.lastName}`}</div>
            <div className="text-sm text-subText">{member?.user?.email}</div>
          </div>
        </div>
      ),
    },
    {
      key: 'memberRoles',
      label: 'Role',
      render: (_, member) => {
        const role = mapApiRoleToUiRole(member.memberRoles[0]);
        return (
          <span
            className={clsx(
              'flex h-[34px] w-28 items-center justify-center rounded-lg px-4 text-xs font-medium',
              getRoleColor(role)
            )}
          >
            {getRoleDisplayName(role)}
          </span>
        );
      },
    },
    {
      key: 'claimedAgentSuite',
      label: 'Last Activity',
      render: (_, member) => (
        <span className="text-sm text-subText">
          {formatLastLogin(member?.user?.lastActivity) || '--'}{' '}
        </span>
      ),
    },
    {
      key: 'id',
      label: '',
      render: (_, member) => {
        const actions = [
          {
            label: 'Change Role',
            icon: null,
            onClick: () => {
              setSelectedMember({
                id: member.id,
                name: `${member?.user?.firstName} ${member?.user?.lastName}`,
                role: member.memberRoles[0] as MemberRole,
              });
              setIsUpdateRoleModalOpen(true);
            },
            variant: 'default' as const,
          },
          {
            label: 'Remove Member',
            icon: null,
            onClick: () => {
              setSelectedMember({
                id: member.id,
                name: `${member?.user?.firstName} ${member?.user?.lastName}`,
                role: member.memberRoles[0] as MemberRole,
              });
              setIsRemoveMemberModalOpen(true);
            },
            variant: 'danger' as const,
          },
        ];

        const currentUserRole =
          userData?.userInfo?.tenant?.claimedAgentSuites
            ?.find(suite => suite?.suite?.agentSuiteKey === agentSuiteKey)
            ?.members?.find(m => m?.user?.userId === userData?.userInfo?.userId)
            ?.memberRoles?.[0]?.toLowerCase() || 'member';

        const canManageMembers =
          currentUserRole === 'manager' || currentUserRole === 'lead';

        return canManageMembers ? <ActionDropdown actions={actions} /> : null;
      },
      className: 'text-right',
    },
  ];

  return (
    <>
      <div className="space-y-6">
        {/* Search */}
        <div className="flex items-center gap-3">
          <Input
            type="text"
            placeholder="Search by name or email"
            className="h-10 w-[300px] rounded-[10px] border border-[#BAB9B9] py-2 pl-4 pr-10 text-sm placeholder:text-grayTen focus:outline-none focus:ring-0"
            endIcon={<Search className="mt-0.5 h-4 w-4" />}
            value={searchQuery}
            onChange={e => onSearchChange(e.target.value)}
          />
        </div>

        {/* Table */}
        <div className="flex flex-col gap-4 overflow-x-auto">
          <DataTable
            data={members || []}
            columns={memberColumns}
            loading={isLoadingMembers}
            emptyMessage="No members found"
            rowColoring={true}
            rowColoringType="odd"
            getRowId={member => member.id}
          />
          {!isLoadingMembers && members && members?.length > 0 && (
            <Pagination
              currentPage={page}
              totalPages={
                membersData
                  ? Math.ceil(membersData.total / membersData.pageSize)
                  : 0
              }
              onPageChange={page => setPage(page)}
            />
          )}
        </div>
      </div>

      {/* Modals */}
      <UpdateRoleModal
        isOpen={isUpdateRoleModalOpen}
        onClose={() => {
          setIsUpdateRoleModalOpen(false);
          setSelectedMember(null);
        }}
        onUpdateRole={(newRole: MemberRole) => {
          handleUpdateRole(selectedMember?.id || '', newRole);
        }}
        userDetails={selectedMember}
        loading={updateRoleMutation.isPending}
      />

      <RemoveMemberModal
        isOpen={isRemoveMemberModalOpen}
        onClose={() => {
          setIsRemoveMemberModalOpen(false);
          setSelectedMember(null);
        }}
        onRemoveMember={memberId => handleRemoveMember(memberId)}
        member={selectedMember}
        loading={removeMemberMutation.isPending}
      />
    </>
  );
};
