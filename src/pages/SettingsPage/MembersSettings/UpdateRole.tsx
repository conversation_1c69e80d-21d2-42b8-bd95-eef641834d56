import React, { useState, useEffect } from 'react';
import { MemberRole, ROLE_DESCRIPTIONS } from '../../../types/members';
import AnimatedModal from '../../../components/common/AnimatedModal';
import { Loader2 } from 'lucide-react';

interface UpdateRoleModalProps {
  isOpen: boolean;
  onClose: () => void;
  onUpdateRole: (newRole: MemberRole) => void;
  userDetails: {
    id: string;
    name: string;
    role: MemberRole;
  } | null;
  loading?: boolean;
}

const UpdateRoleModal: React.FC<UpdateRoleModalProps> = ({
  isOpen,
  onClose,
  onUpdateRole,
  userDetails,
  loading = false,
}) => {
  const [selectedRole, setSelectedRole] = useState<MemberRole>('member');
  const [initialRole, setInitialRole] = useState<MemberRole | null>(null);

  useEffect(() => {
    if (userDetails && isOpen && !initialRole) {
      // Only set the role when modal first opens, not on subsequent data changes
      const normalizedRole = userDetails.role?.toLowerCase() as MemberRole;
      setSelectedRole(normalizedRole || 'member');
      setInitialRole(normalizedRole || 'member');
    }
  }, [userDetails, isOpen, initialRole]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!userDetails) return;

    // Compare with the initial role to prevent no-op updates
    if (selectedRole === initialRole) {
      onClose();
      return;
    }

    onUpdateRole(selectedRole);
  };

  const handleClose = () => {
    if (userDetails) {
      const normalizedRole = userDetails.role?.toLowerCase() as MemberRole;
      setSelectedRole(normalizedRole || 'member');
    }
    setInitialRole(null); // Reset initial role for next time modal opens
    onClose();
  };

  return (
    <AnimatedModal
      isOpen={isOpen && !!userDetails}
      onClose={handleClose}
      maxWidth="xl"
      showCloseButton={false}
    >
      <form
        onSubmit={handleSubmit}
        className="flex flex-col justify-center items-center p-6 space-y-6"
      >
        {/* Title */}
        <h3 className="text-lg font-semibold text-blackOne">
          Update {userDetails?.name}'s Role
        </h3>

        {/* Role Selection */}
        <div className="space-y-3">
          <label className="flex items-start space-x-3 cursor-pointer">
            <input
              type="radio"
              name="role"
              value="manager"
              checked={selectedRole === 'manager'}
              onChange={(e) => setSelectedRole(e.target.value as MemberRole)}
              className="mt-1 w-4 h-4 text-primary focus:ring-primary"
              disabled={loading}
            />

            <div className="text-sm text-subText">
              {ROLE_DESCRIPTIONS.manager}
            </div>
          </label>

          <label className="flex items-start space-x-3 cursor-pointer">
            <input
              type="radio"
              name="role"
              value="lead"
              checked={selectedRole === 'lead'}
              onChange={(e) => setSelectedRole(e.target.value as MemberRole)}
              className="mt-1 w-4 h-4 text-primary focus:ring-primary"
              disabled={loading}
            />

            <div className="text-sm text-subText">{ROLE_DESCRIPTIONS.lead}</div>
          </label>

          <label className="flex items-start space-x-3 cursor-pointer">
            <input
              type="radio"
              name="role"
              value="member"
              checked={selectedRole === 'member'}
              onChange={(e) => setSelectedRole(e.target.value as MemberRole)}
              className="mt-1 w-4 h-4 text-primary focus:ring-primary"
              disabled={loading}
            />

            <div className="text-sm text-subText">
              {ROLE_DESCRIPTIONS.member}
            </div>
          </label>
        </div>

        {/* Actions */}
        <div className="flex gap-3 justify-center">
          <button
            type="button"
            onClick={handleClose}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200"
            disabled={loading}
          >
            Cancel
          </button>
          <button
            type="submit"
            className="flex gap-2 items-center px-4 py-2 text-sm font-medium text-white rounded-lg bg-primary hover:bg-primary/90 disabled:opacity-50"
            disabled={
              loading || selectedRole === initialRole
            }
          >
            {loading ? (
              <>
                <Loader2 className="w-4 h-4 animate-spin" />
                Updating...
              </>
            ) : (
              'Update'
            )}
          </button>
        </div>
      </form>
    </AnimatedModal>
  );
};

export default UpdateRoleModal;
