import { Loader2 } from 'lucide-react';
import React, { useEffect, useState } from 'react';

import AnimatedModal from '../../../components/common/AnimatedModal';
import { MemberRole, ROLE_DESCRIPTIONS } from '../../../types/members';

interface UpdateRoleModalProps {
  isOpen: boolean;
  onClose: () => void;
  onUpdateRole: (newRole: MemberRole) => void;
  userDetails: {
    id: string;
    name: string;
    role: MemberRole;
  } | null;
  loading?: boolean;
}

const UpdateRoleModal: React.FC<UpdateRoleModalProps> = ({
  isOpen,
  onClose,
  onUpdateRole,
  userDetails,
  loading = false,
}) => {
  const [selectedRole, setSelectedRole] = useState<MemberRole>('member');
  const [initialRole, setInitialRole] = useState<MemberRole | null>(null);

  useEffect(() => {
    if (userDetails && isOpen && !initialRole) {
      // Only set the role when modal first opens, not on subsequent data changes
      const normalizedRole = userDetails.role?.toLowerCase() as MemberRole;
      setSelectedRole(normalizedRole || 'member');
      setInitialRole(normalizedRole || 'member');
    }
  }, [userDetails, isOpen, initialRole]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!userDetails) return;

    // Compare with the initial role to prevent no-op updates
    if (selectedRole === initialRole) {
      onClose();
      return;
    }

    onUpdateRole(selectedRole);
  };

  const handleClose = () => {
    if (userDetails) {
      const normalizedRole = userDetails.role?.toLowerCase() as MemberRole;
      setSelectedRole(normalizedRole || 'member');
    }
    setInitialRole(null); // Reset initial role for next time modal opens
    onClose();
  };

  return (
    <AnimatedModal
      isOpen={isOpen && !!userDetails}
      onClose={handleClose}
      maxWidth="xl"
      showCloseButton={false}
    >
      <form
        onSubmit={handleSubmit}
        className="flex flex-col items-center justify-center space-y-6 p-6"
      >
        {/* Title */}
        <h3 className="text-lg font-semibold text-blackOne">
          Update {userDetails?.name}'s Role
        </h3>

        {/* Role Selection */}
        <div className="space-y-3">
          <label className="flex cursor-pointer items-center space-x-3">
            <input
              type="radio"
              name="role"
              value="manager"
              checked={selectedRole === 'manager'}
              onChange={e => setSelectedRole(e.target.value as MemberRole)}
              className="mt-1 h-4 w-4 text-primary focus:ring-primary"
              disabled={loading}
            />

            <div className="text-sm text-subText">
              <span className="font-semibold">
                {ROLE_DESCRIPTIONS.manager.title}
              </span>
              {ROLE_DESCRIPTIONS.manager.description}
            </div>
          </label>

          <label className="flex cursor-pointer items-start space-x-3">
            <input
              type="radio"
              name="role"
              value="lead"
              checked={selectedRole === 'lead'}
              onChange={e => setSelectedRole(e.target.value as MemberRole)}
              className="mt-0.5 h-4 w-4 text-primary focus:ring-primary"
              disabled={loading}
            />

            <div className="text-sm text-subText">
              <span className="font-semibold">
                {ROLE_DESCRIPTIONS.lead.title}
              </span>
              {ROLE_DESCRIPTIONS.lead.description}
            </div>
          </label>

          <label className="flex cursor-pointer items-start space-x-3">
            <input
              type="radio"
              name="role"
              value="member"
              checked={selectedRole === 'member'}
              onChange={e => setSelectedRole(e.target.value as MemberRole)}
              className="mt-0.5 h-4 w-4 text-primary focus:ring-primary"
              disabled={loading}
            />

            <div className="text-sm text-subText">
              <span className="font-semibold">
                {ROLE_DESCRIPTIONS.member.title}
              </span>
              {ROLE_DESCRIPTIONS.member.description}
            </div>
          </label>
        </div>

        {/* Actions */}
        <div className="flex justify-center gap-3">
          <button
            type="button"
            onClick={handleClose}
            className="rounded-lg bg-gray-100 px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-200"
            disabled={loading}
          >
            Cancel
          </button>
          <button
            type="submit"
            className="flex items-center gap-2 rounded-lg bg-primary px-4 py-2 text-sm font-medium text-white hover:bg-primary/90 disabled:opacity-50"
            disabled={loading || selectedRole === initialRole}
          >
            {loading ? (
              <>
                <Loader2 className="h-4 w-4 animate-spin" />
                Updating...
              </>
            ) : (
              'Update'
            )}
          </button>
        </div>
      </form>
    </AnimatedModal>
  );
};

export default UpdateRoleModal;
