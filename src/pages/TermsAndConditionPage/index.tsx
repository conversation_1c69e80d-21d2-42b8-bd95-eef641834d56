import { motion } from 'framer-motion';
import React from 'react';

const termsAndConditions = [
  {
    id: 1,
    title: 'Acceptance of Terms',
    content: [
      'By accessing and using Agentous ("the Platform"), you agree to comply with and be bound by these Terms and Conditions.',
      'If you do not agree with any part of these Terms and Conditions, you must not use the Platform.',
    ],
  },
  {
    id: 2,
    title: 'User Registration',
    content: [
      'To access certain features of the Platform, you may be required to register for an account.',
      'You agree to provide accurate, current, and complete information during the registration process.',
    ],
  },
  {
    id: 3,
    title: 'Courses and Content',
    content: [
      'The Platform offers courses and educational content. The accuracy and quality of the content are not guaranteed.',
      'Users may not reproduce, distribute, or display any content without explicit permission from Agentous.',
    ],
  },
  {
    id: 4,
    title: 'User Conduct',
    content: [
      'Users must not engage in any conduct that may disrupt the proper functioning of the Platform or infringe on the rights of others.',
      'Users are responsible for maintaining the confidentiality of their account information and are liable for any activities that occur under their account.',
    ],
  },
  {
    id: 5,
    title: 'Payments and Fees',
    content: [
      'Some features of the Platform may require payment. Users agree to pay all fees and charges associated with their account.',
      'Payments are non-refundable unless otherwise specified.',
    ],
  },
  {
    id: 6,
    title: 'Intellectual Property',
    content: [
      'All intellectual property rights related to the Platform, including but not limited to copyrights, trademarks, and patents, are owned by Agentous.',
      "Users may not use, copy, reproduce, distribute, or create derivative works based on Agentous's intellectual property without explicit permission.",
    ],
  },
  {
    id: 7,
    title: 'Privacy Policy',
    content: [
      "Agentous's Privacy Policy governs the collection, use, and disclosure of personal information. By using the Platform, you agree to the terms outlined in the Privacy Policy.",
    ],
  },
  {
    id: 8,
    title: 'Termination of Account',
    content: [
      'Agentous reserves the right to terminate or suspend user accounts for any reason, including but not limited to a violation of these Terms and Conditions.',
    ],
  },
  {
    id: 9,
    title: 'Modifications to Terms',
    content: [
      'Agentous may modify these Terms and Conditions at any time. Users will be notified of changes, and continued use of the Platform constitutes acceptance of the modified terms.',
    ],
  },
  {
    id: 10,
    title: 'Governing Law',
    content: [
      'These Terms and Conditions are governed by and construed in accordance with the laws of [Your Jurisdiction].',
    ],
  },
];

const TermsAndConditionsPage: React.FC = () => {
  return (
    <div className="mx-auto max-w-4xl py-4">
      <h1 className="px-4 text-center text-2xl font-semibold text-gray-900 md:text-left">
        Terms and Conditions for Agentous
      </h1>

      <motion.section
        className="px-4 py-6"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 1 }}
      >
        <div className="space-y-8">
          {termsAndConditions.map(term => (
            <div key={term.id} className="space-y-4">
              {/* Main term number and title */}
              <h2 className="font-semibold text-gray-900">
                {term.id}. {term.title}
              </h2>

              {/* Sub-content */}
              <div className="ml-6 space-y-3">
                {term.content.map((paragraph, index) => (
                  <p key={index} className="leading-relaxed text-gray-700">
                    <span className="font-medium">
                      {term.id}.{index + 1}.
                    </span>{' '}
                    {paragraph}
                  </p>
                ))}
              </div>
            </div>
          ))}
        </div>
      </motion.section>
    </div>
  );
};

export default TermsAndConditionsPage;
