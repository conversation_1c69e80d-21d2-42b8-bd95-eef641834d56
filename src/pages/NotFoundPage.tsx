import React from 'react';
import { useNavigate } from 'react-router-dom';
import { ArrowLeft, Home } from 'lucide-react';

export const NotFoundPage: React.FC = () => {
  const navigate = useNavigate();

  const handleGoHome = () => {
    navigate('/dashboard');
  };

  const handleGoBack = () => {
    navigate(-1);
  };

  return (
    <div className="min-h-[calc(100vh-200px)] flex items-center justify-center bg-gray-50">
      <div className="w-full max-w-md text-center">
        {/* 404 Illustration */}
        <div className="mb-8">
          <div className="mb-4 text-5xl font-bold sm:text-8xl text-primary">404</div>
          <div className="mx-auto mb-6 w-24 h-1 bg-primary"></div>
        </div>

        {/* Content */}
        <div className="mb-8">
          <h1 className="mb-4 font-semibold sm:text-xl text-blackOne">
            Page Not Found
          </h1>
          <p className="mb-6 text-xs sm:text-sm text-subText">
            Sorry, the page you are looking for doesn't exist or has been moved.
            Please check the URL or navigate back to continue.
          </p>
        </div>

        {/* Action Buttons */}
        <div className="space-y-3">
          <button
            onClick={handleGoHome}
            className="flex justify-center items-center px-6 py-3 w-full text-white rounded-lg transition-colors bg-primary hover:bg-primary/90"
          >
            <Home className="mr-2 w-4 h-4" />
            Go to Dashboard
          </button>
          
          <button
            onClick={handleGoBack}
            className="flex justify-center items-center px-6 py-3 w-full text-gray-700 rounded-lg border border-gray-300 transition-colors hover:bg-gray-50"
          >
            <ArrowLeft className="mr-2 w-4 h-4" />
            Go Back
          </button>
        </div>

        {/* Additional Help */}
        <div className="mt-8 text-sm text-subText">
          <p>Need help? Contact support or try one of these popular pages:</p>
          <div className="flex justify-center mt-2 space-x-4">
            <button
              onClick={() => navigate('/dashboard/ai-agents')}
              className="text-primary hover:underline"
            >
              AI Agents
            </button>
            <button
              onClick={() => navigate('/dashboard/settings')}
              className="text-primary hover:underline"
            >
              Settings
            </button>
            <button
              onClick={() => navigate('/dashboard/knowledge-base')}
              className="text-primary hover:underline"
            >
              Knowledge Base
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default NotFoundPage;