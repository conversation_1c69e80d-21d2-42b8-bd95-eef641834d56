import { useKeycloak } from '@react-keycloak/web';
import { Eye, EyeOff } from 'lucide-react';
import { useMemo, useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';

import { eclipse, halfEclipse } from '../../assets/images';
import { ChatInput } from '../../components/chat/ChatInput';
import { RegisChatInterface } from '../../components/chat/RegisChatInterface';
import { ROUTES } from '../../constants/routes';
import { useStreamingRegisChat } from '../../hooks/useStreamingRegisChat';

const LoginFormSidebar = ({ state, sendMessage }: any) => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [isLoggingIn, setIsLoggingIn] = useState(false);
  const { keycloak } = useKeycloak();
  const navigate = useNavigate();
  const handleLogin = async () => {
    if (!email.trim() || !password.trim()) return;

    setIsLoggingIn(true);
    try {
      // Redirect to a redirect handler that will check tenant count and redirect accordingly
      const redirectUri = window.location.origin + '/pivotl/redirect-handler';

      await keycloak?.login({
        redirectUri,
      });
    } catch (error) {
      console.error('Error initiating Keycloak login:', error);
      // Fallback: redirect to main login page or show error
      window.location.href = '/login';
    } finally {
      setIsLoggingIn(false);
    }
  };

  const handleForgotPassword = () => {
    // sendMessage('I have forgotten my password');
    navigate(ROUTES.RESET_PASSWORD);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleLogin();
    }
  };

  return (
    <div
      className="relative flex flex-col justify-center overflow-hidden rounded bg-gradient-to-br from-orange-50/50 to-orange-100 p-6 font-inter"
      style={{ height: '100vh' }}
    >
      {/* Background Objects */}
      <div
        className="pointer-events-none absolute inset-0 z-0 -mt-48 bg-[right] bg-no-repeat"
        style={{
          backgroundImage: `url(${halfEclipse})`,
          backgroundSize: 'auto',
        }}
      />
      <div
        className="pointer-events-none absolute inset-0 z-0 bg-[right_bottom] bg-no-repeat"
        style={{
          backgroundImage: `url(${eclipse})`,
          backgroundSize: 'auto',
        }}
      />

      <div className="relative z-10 max-h-full overflow-y-auto rounded-2xl bg-white p-6 shadow-sm">
        <div className="space-y-4">
          {/* Email Input */}
          <div>
            <input
              type="email"
              value={email}
              onChange={e => setEmail(e.target.value)}
              onKeyDown={handleKeyDown}
              className="h-12 w-full rounded-md border border-gray-300 px-3 py-2 text-sm text-blackTwo transition-all focus:border-primary focus:outline-none focus:ring-2 focus:ring-primary/20"
              placeholder="Email address"
              disabled={state.isLoading || isLoggingIn}
            />
          </div>

          {/* Password Input */}
          <div>
            <div className="relative">
              <input
                type={showPassword ? 'text' : 'password'}
                value={password}
                onChange={e => setPassword(e.target.value)}
                onKeyDown={handleKeyDown}
                className="mt-4 h-12 w-full rounded-md border border-gray-300 px-3 py-2 pr-10 text-sm text-blackTwo transition-all focus:border-primary focus:outline-none focus:ring-2 focus:ring-primary/20"
                placeholder="Password"
                disabled={state.isLoading || isLoggingIn}
              />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 hover:text-primary"
                disabled={state.isLoading || isLoggingIn}
              >
                {showPassword ? (
                  <EyeOff className="mt-4 h-5 w-5" />
                ) : (
                  <Eye className="mt-4 h-5 w-5" />
                )}
              </button>
            </div>
          </div>

          {/* Forgot Password Link */}
          <button
            onClick={handleForgotPassword}
            className="text-sm text-primary transition-colors hover:text-primary/80"
            disabled={state.isLoading || isLoggingIn}
          >
            Forgot Password?
          </button>

          <div className="py-2">
            <hr className="border-primary" />
          </div>

          {/* Login Button */}
          <button
            onClick={handleLogin}
            disabled={
              !email.trim() ||
              !password.trim() ||
              state.isLoading ||
              isLoggingIn
            }
            className="w-full rounded-md border border-primary bg-lightOrangeTwo px-6 py-2 font-medium text-blue-midnight transition-colors hover:border-lightOrangeTwo hover:bg-orange-15 hover:text-white disabled:cursor-not-allowed disabled:border-none disabled:bg-gray-400 disabled:text-whiteOff disabled:opacity-50"
          >
            {isLoggingIn ? 'Logging in...' : 'Login'}
          </button>

          {/* Signup Link */}
          <div className="text-center">
            <p className="text-sm text-gray-600">
              Don't have an account?{' '}
              <Link
                to={ROUTES.SIGNUP}
                className="font-medium text-primary transition-colors hover:text-primary/80"
              >
                Sign up here
              </Link>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export const PivotlLoginPage = () => {
  const { state, sendMessage, isStreaming, streamingMessage } =
    useStreamingRegisChat('login');

  // Memoize the chat input component to prevent recreation on every render
  const ChatInputComponent = useMemo(() => {
    return () => (
      <ChatInput
        onSendMessage={sendMessage}
        placeholder="Ask me anything about login or password recovery..."
        disabled={state.isLoading}
      />
    );
  }, [sendMessage, state.isLoading]);

  return (
    <div className="mx-auto max-w-screen-3xl font-inter">
      <div className="grid h-screen grid-cols-1 lg:grid-cols-3">
        {/* LHS - Chat Interface */}
        <div className="relative lg:col-span-2 lg:px-24">
          <div className="h-[calc(100vh-100px)] bg-white">
            <RegisChatInterface
              state={state}
              ChatInputComponent={ChatInputComponent}
              isStreaming={isStreaming}
              streamingMessage={streamingMessage}
            />
          </div>
        </div>

        {/* RHS - Login Form Sidebar */}
        <div className="lg:col-span-1">
          <LoginFormSidebar state={state} sendMessage={sendMessage} />
        </div>
      </div>
    </div>
  );
};

export default PivotlLoginPage;
