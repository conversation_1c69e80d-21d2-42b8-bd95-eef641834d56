import React from 'react';
import { useLocation } from 'react-router-dom';

import SectionTitle from '@/components/common/SectionTitle';
import { ROUTES } from '@/constants/routes';
import { useTenant } from '@/context/TenantContext';
import { useDailyInsightsList } from '@/hooks/useDashboard';
import { useAnalyticsParams } from '@/utils/urlParams';

import DailyInsightCommentary from './DailyInsightCommentary';

const InsightsPage: React.FC = () => {
  const { tenantId } = useTenant();
  const location = useLocation();
  const { filters } = useAnalyticsParams();

  // Get filters from URL params
  const searchParams = new URLSearchParams(location.search);
  const fromDate = searchParams.get('from') || undefined;
  const toDate = searchParams.get('to') || undefined;

  // Build filter for insights - include agent filter by default
  const insightFilter = {
    tenantId: tenantId || '',
    agentName: filters.agent || undefined,
    from: fromDate,
    to: toDate,
    page: 0,
    pageSize: 10,
  };

  // Fetch insights using the hook
  const {
    data: insightsResponse,
    isLoading,
    isError,
  } = useDailyInsightsList(insightFilter, !!tenantId);

  const insights = insightsResponse?.insights || [];

  // Build browse all route with current filters
  const browseAllRoute = `${ROUTES.DASHBOARD_ANALYTICS_INSIGHTS_ALL}${location.search}`;

  return (
    <div className="px-6">
      {/* Daily Insight Commentary */}
      <div className="overflow-hidden rounded-xl bg-white p-4">
        <SectionTitle
          title="Daily Insight Commentary"
          className="mb-4"
          browseAllRoute={browseAllRoute}
        />
        <DailyInsightCommentary
          insights={insights}
          isLoading={isLoading}
          isError={isError}
        />
      </div>
    </div>
  );
};

export default InsightsPage;
