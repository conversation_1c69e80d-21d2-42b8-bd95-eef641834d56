import React from 'react';
import SectionTitle from '@/components/common/SectionTitle';
import { ROUTES } from '@/constants/routes';
import DailyInsightCommentary from './DailyInsightCommentary';
import { useTenant } from '@/context/TenantContext';
import { useLocation } from 'react-router-dom';
import { useAnalyticsParams } from '@/utils/urlParams';
import { useDailyInsightsList } from '@/hooks/useDashboard';

const InsightsPage: React.FC = () => {
  const { tenantId } = useTenant();
  const location = useLocation();
  const { filters } = useAnalyticsParams();

  // Get filters from URL params
  const searchParams = new URLSearchParams(location.search);
  const fromDate = searchParams.get('from') || undefined;
  const toDate = searchParams.get('to') || undefined;

  // Build filter for insights - include agent filter by default
  const insightFilter = {
    tenantId: tenantId || '',
    agentName: filters.agent || undefined,
    from: fromDate,
    to: toDate,
    page: 0,
    pageSize: 10,
  };

  // Fetch insights using the hook
  const {
    data: insightsResponse,
    isLoading,
    isError,
  } = useDailyInsightsList(insightFilter, !!tenantId);

  const insights = insightsResponse?.insights || [];

  // Build browse all route with current filters
  const browseAllRoute = `${ROUTES.DASHBOARD_ANALYTICS_INSIGHTS_ALL}${location.search}`;

  return (
    <div className="px-6">
      {/* Daily Insight Commentary */}
      <div className="overflow-hidden p-4 bg-white rounded-xl">
        <SectionTitle
          title="Daily Insight Commentary"
          className="mb-4"
          browseAllRoute={browseAllRoute}
        />
        <DailyInsightCommentary
          insights={insights}
          isLoading={isLoading}
          isError={isError}
        />
      </div>
    </div>
  );
};

export default InsightsPage;