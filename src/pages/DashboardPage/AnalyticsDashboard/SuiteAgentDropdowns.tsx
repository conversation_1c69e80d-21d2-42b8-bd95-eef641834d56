import React, { useState, useEffect, useRef, useMemo } from 'react';
import { useOnClickOutside } from '@/hooks/useOnClickOutside';
import { useAnalyticsParams } from '@/utils/urlParams';
import { useGetUserProfile } from '@/hooks/useUserProfile';
import { UserBasicInfoPayload } from '@/types/user';
import { marketplaceAgents } from '@/data/constants';
import { DropdownOption } from '../../../components/ui/AgentsDropdown';
import AgentsDropdown from '../../../components/ui/AgentsDropdown';
import { useTenant } from '@/context/TenantContext';

interface SuiteAgentDropdownProps {
  className?: string;
}

const SuiteAgentDropdown: React.FC<SuiteAgentDropdownProps> = ({
  className,
}) => {
  const { data: userData } = useGetUserProfile<UserBasicInfoPayload>();
  const { filters, updateFilters } = useAnalyticsParams();
  const { setActiveAgent } = useTenant();
  const [isSuiteDropdownOpen, setIsSuiteDropdownOpen] =
    useState<boolean>(false);
  const [isAgentDropdownOpen, setIsAgentDropdownOpen] =
    useState<boolean>(false);

  const suiteDropdownRef = useRef<HTMLDivElement>(null);
  const agentDropdownRef = useRef<HTMLDivElement>(null);
  useOnClickOutside(suiteDropdownRef, () => setIsSuiteDropdownOpen(false));
  useOnClickOutside(agentDropdownRef, () => setIsAgentDropdownOpen(false));

  const suiteOptions = useMemo<DropdownOption[]>(() => {
    const claimedSuites = userData?.userInfo?.tenant?.claimedAgentSuites || [];
    return claimedSuites.map((suite) => ({
      id: suite.suite.agentSuiteKey,
      name: suite.suite.agentSuiteName,
      icon: suite.suite.avatar,
    }));
  }, [userData]);

  const currentSuite = useMemo(
    () => suiteOptions.find((s) => s.id === filters.suite) || suiteOptions[0],
    [filters.suite, suiteOptions]
  );

  const agentOptions = useMemo<DropdownOption[]>(() => {
    if (!filters.suite || !userData) return [];

    const tenantAgents = userData.tenantAgents || [];
    const suiteAgents = tenantAgents.filter(
      (agent) => agent.agentSuiteKey === filters.suite
    );

    return suiteAgents.map((agent) => ({
      id: agent.agentKey,
      name: agent.agentName,
      icon: agent.avatar,
    }));
  }, [filters.suite, userData]);

  const currentAgent = useMemo(
    () => agentOptions.find((a) => a.id === filters.agent) || agentOptions[0],
    [filters.agent, agentOptions]
  );

  const handleSuiteChange = (suite: DropdownOption) => {
    updateFilters({ suite: suite.id, agent: undefined });
    setIsSuiteDropdownOpen(false);
    setActiveAgent(suite.id);
  };

  const handleAgentChange = (agent: DropdownOption) => {
    updateFilters({ agent: agent.id });
    setIsAgentDropdownOpen(false);
    setActiveAgent(agent.id);
  };

  useEffect(() => {
    if (suiteOptions.length > 0 && !filters.suite) {
      updateFilters({ suite: suiteOptions[0].id });
    }
    if (agentOptions.length > 0 && !filters.agent) {
      updateFilters({ agent: agentOptions[0].id });
    }
  }, [suiteOptions, agentOptions, filters, updateFilters]);

  const handleAgentError = (
    event: React.SyntheticEvent<HTMLImageElement, Event>
  ) => {
    (event.target as HTMLImageElement).src =
      marketplaceAgents.find(
        (mockAgent) =>
          mockAgent.name.toLowerCase() === currentAgent?.name.toLowerCase()
      )?.image || '';
  };

  const getAgentOptionError =
    (option: DropdownOption) =>
    (event: React.SyntheticEvent<HTMLImageElement, Event>) => {
      (event.target as HTMLImageElement).src =
        marketplaceAgents.find(
          (mockAgent) =>
            mockAgent.name.toLowerCase() === option.name.toLowerCase()
        )?.image || '';
    };

  return (
    <div className={`flex gap-8 items-center ${className}`}>
      <div className="relative" ref={suiteDropdownRef}>
        <AgentsDropdown
          isOpen={isSuiteDropdownOpen}
          onToggle={() => setIsSuiteDropdownOpen(!isSuiteDropdownOpen)}
          currentItem={currentSuite}
          options={suiteOptions}
          onItemSelect={handleSuiteChange}
          placeholder="Suite"
          noOptionsMessage="No other suites available"
        />
      </div>

      <div className="relative" ref={agentDropdownRef}>
        <AgentsDropdown
          isOpen={isAgentDropdownOpen}
          onToggle={() => setIsAgentDropdownOpen(!isAgentDropdownOpen)}
          currentItem={currentAgent}
          options={agentOptions}
          onItemSelect={handleAgentChange}
          placeholder="Agent"
          noOptionsMessage="No other agents available"
          onImageError={handleAgentError}
          getOptionImageError={getAgentOptionError}
        />
      </div>
    </div>
  );
};

export default SuiteAgentDropdown;
