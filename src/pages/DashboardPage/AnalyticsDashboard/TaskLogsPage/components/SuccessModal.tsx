import React from 'react';

interface SuccessModalProps {
  isOpen: boolean;
  title?: string;
  message?: string;
}

const SuccessModal: React.FC<SuccessModalProps> = ({
  isOpen,
  title = "Success!",
  message = "Task log has been deleted successfully",
}) => {
  if (!isOpen) return null;

  return (
    <div className="flex fixed inset-0 z-50 justify-center items-center p-4 backdrop-blur-sm bg-black/30 animate-fade-in">
      <div className="p-12 mx-4 w-full max-w-md text-center bg-white rounded-2xl shadow-2xl animate-scale-in">
        <div className="mb-8">
          <div className="flex justify-center items-center mx-auto mb-6 w-24 h-24 bg-green-50 rounded-full animate-pulse">
            <svg
              className="w-12 h-12 text-green-500"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M5 13l4 4L19 7"
              />
            </svg>
          </div>
          <h3 className="mb-4 text-3xl font-bold text-gray-900">
            {title}
          </h3>
          <p className="text-lg text-gray-600">
            {message}
          </p>
          <p className="mt-3 text-sm text-gray-500">
            Redirecting you back to the list...
          </p>
        </div>
      </div>
    </div>
  );
};

export default SuccessModal;