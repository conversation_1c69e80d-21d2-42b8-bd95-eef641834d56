import { Chevron<PERSON>eft, Edit, Loader2, Trash2, X } from 'lucide-react';
import React from 'react';

interface TaskLogHeaderProps {
  taskTitle: string;
  isEditing: boolean;
  saving: boolean;
  hasChanges?: boolean;
  onBack: () => void;
  onEdit: () => void;
  onSave: () => void;
  onCancel: () => void;
  onDelete?: () => void;
}

const TaskLogHeader: React.FC<TaskLogHeaderProps> = ({
  taskTitle,
  isEditing,
  saving,
  hasChanges = false,
  onBack,
  onEdit,
  onSave,
  onCancel,
  onDelete,
}) => {
  return (
    <div className="border-b border-[#FFECE3] py-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <button
            onClick={onBack}
            className="flex items-center font-medium text-blackOne transition-colors hover:text-gray-900"
          >
            <ChevronLeft className="mr-1 h-5 w-5" />
            {taskTitle}
          </button>
        </div>

        <div className="hidden items-center gap-3">
          {onDelete && !isEditing && (
            <button
              className="flex h-[36px] w-[52px] items-center justify-center rounded-full border border-[#BAB9B9] p-2 text-grayTen transition-colors hover:bg-gray-100 hover:text-gray-900"
              onClick={onDelete}
            >
              <Trash2 className="h-5 w-5" />
            </button>
          )}

          {isEditing ? (
            <button
              onClick={onSave}
              disabled={saving}
              className="flex h-[36px] items-center justify-center gap-2 rounded-full border border-primary bg-transparent px-6 transition-colors disabled:cursor-not-allowed disabled:opacity-50"
            >
              {saving && <Loader2 className="h-4 w-4 animate-spin" />}
              {saving ? 'Saving...' : 'Save Changes'}
            </button>
          ) : (
            <button
              onClick={onEdit}
              className="flex h-[36px] w-[52px] items-center justify-center rounded-full border border-[#BAB9B9] p-2 text-grayTen transition-colors hover:bg-gray-100 hover:text-gray-900"
            >
              <Edit className="h-4 w-4" />
            </button>
          )}

          {isEditing && (
            <button
              onClick={onCancel}
              className="flex h-[36px] w-[52px] items-center justify-center rounded-full border border-[#BAB9B9] p-2 text-grayTen transition-colors hover:bg-gray-100 hover:text-gray-900"
            >
              <X className="h-4 w-4" />
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default TaskLogHeader;
