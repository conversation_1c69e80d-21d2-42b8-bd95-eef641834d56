import React from 'react';
import { ChevronLeft, Edit, Trash2, Save, X, Loader2 } from 'lucide-react';

interface TaskLogHeaderProps {
  taskTitle: string;
  isEditing: boolean;
  saving: boolean;
  hasChanges?: boolean;
  onBack: () => void;
  onEdit: () => void;
  onSave: () => void;
  onCancel: () => void;
  onDelete?: () => void;
}

const TaskLogHeader: React.FC<TaskLogHeaderProps> = ({
  taskTitle,
  isEditing,
  saving,
  hasChanges = false,
  onBack,
  onEdit,
  onSave,
  onCancel,
  onDelete,
}) => {
  return (
    <div className="py-4 border-b border-[#FFECE3]">
      <div className="flex justify-between items-center">
        <div className="flex gap-4 items-center">
          <button
            onClick={onBack}
            className="flex items-center font-medium transition-colors text-blackOne hover:text-gray-900"
          >
            <ChevronLeft className="mr-1 w-5 h-5" />
            {taskTitle}
          </button>
        </div>

        <div className="hidden gap-3 items-center">
          {onDelete && !isEditing && (
            <button
              className="p-2 w-[52px] h-[36px] flex items-center justify-center border border-[#BAB9B9] rounded-full transition-colors text-grayTen hover:text-gray-900 hover:bg-gray-100"
              onClick={onDelete}
            >
              <Trash2 className="w-5 h-5" />
            </button>
          )}

          {isEditing ? (
            <button
              onClick={onSave}
              disabled={saving}
              className="flex gap-2 justify-center items-center px-6 h-[36px] bg-transparent rounded-full border transition-colors border-primary disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {saving && <Loader2 className="w-4 h-4 animate-spin" />}
              {saving ? 'Saving...' : 'Save Changes'}
            </button>
          ) : (
            <button
              onClick={onEdit}
              className="p-2 w-[52px] h-[36px] text-grayTen flex items-center justify-center rounded-full border transition-colors border-[#BAB9B9] hover:text-gray-900 hover:bg-gray-100"
            >
              <Edit className="w-4 h-4" />
            </button>
          )}

          {isEditing && (
            <button
              onClick={onCancel}
              className="p-2 w-[52px] h-[36px] text-grayTen flex items-center justify-center rounded-full transition-colors border border-[#BAB9B9] hover:text-gray-900 hover:bg-gray-100"
            >
              <X className="w-4 h-4" />
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default TaskLogHeader;