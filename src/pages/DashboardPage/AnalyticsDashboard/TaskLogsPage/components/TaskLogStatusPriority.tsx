import React from 'react';
import {
  TaskLogDetailsFormData,
  TaskLogStatusDisplay,
  TaskLogPriorityDisplay,
} from '@/types/taskLog';
import { Icons } from '@/assets/icons/DashboardIcons';
import clsx from 'clsx';
import CustomDropdown, { DropdownOption } from './CustomDropdown';

interface TaskLogStatusPriorityProps {
  taskLog: TaskLogDetailsFormData;
  editForm: Partial<TaskLogDetailsFormData>;
  isEditing: boolean;
  onSelectChange: (field: keyof TaskLogDetailsFormData, value: string) => void;
}

const TaskLogStatusPriority: React.FC<TaskLogStatusPriorityProps> = ({
  taskLog,
  editForm,
  isEditing,
  onSelectChange,
}) => {
  const statusOptionStrings: TaskLogStatusDisplay[] = [
    'Not Started',
    'In Progress',
    'Completed',
  ];
  const priorityOptionStrings: TaskLogPriorityDisplay[] = [
    'Low',
    'Medium',
    'High',
  ];

  const getPriorityColor = (priority: string) => {
    const colorMap = {
      Low: 'bg-[#23BD33] text-white',
      Medium: 'bg-[#FBA320] text-white',
      High: 'bg-[#CE1111] text-white',
    };
    return (
      colorMap[priority as keyof typeof colorMap] || 'bg-gray-500 text-white'
    );
  };

  const getStatusColor = (status: string) => {
    const colorMap = {
      Completed: 'bg-blackOne text-white',
      'In Progress': 'bg-purpleOne text-white',
      'Not Started': 'bg-grayTen text-white',
    };
    return (
      colorMap[status as keyof typeof colorMap] || 'bg-gray-500 text-white'
    );
  };

  const getPriorityIcon = (priority: string) => {
    //use switch case to return the correct icon
    switch (priority) {
      case 'Low':
        return <Icons.PriorityLow className="w-4 h-3.5" />;
      case 'Medium':
        return <Icons.PriorityMedium className="w-4 h-3.5" />;
      case 'High':
        return <Icons.PriorityHigh className="w-4 h-3.5" />;
      default:
        return null;
    }
  };

  const statusOptions: DropdownOption[] = statusOptionStrings.map((s) => ({
    value: s,
    label: s,
  }));

  const priorityOptions: DropdownOption[] = priorityOptionStrings.map((p) => ({
    value: p,
    label: p,
    icon: getPriorityIcon(p),
  }));

  return (
    <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
      <div className="p-4 rounded-none border border-[#DFEAF2]">
        <h3 className="mb-3 text-sm font-medium text-grayTen">Status</h3>
        {isEditing ? (
          <CustomDropdown
            options={statusOptions}
            value={editForm.status || taskLog.status}
            onChange={(value) => onSelectChange('status', value)}
            buttonClassName={clsx(
              'w-fit min-w-[136px] h-[44px]',
              getStatusColor(editForm.status || taskLog.status)
            )}
            renderButtonContent={(selectedOption) => (
              <span className="flex-grow text-left">
                {selectedOption?.label}
              </span>
            )}
            dropdownClassName="w-[150px]!"
          />
        ) : (
          <span
            className={clsx(
              'inline-flex items-center gap-2 px-6 h-[44px] rounded-full text-sm font-medium',
              getStatusColor(taskLog.status)
            )}
          >
            {taskLog.status}
          </span>
        )}
      </div>

      <div className="p-4 rounded-none border border-[#DFEAF2]">
        <h3 className="mb-3 text-sm font-medium text-grayTen">Priority</h3>
        {isEditing ? (
          <CustomDropdown
            options={priorityOptions}
            value={editForm.priority || taskLog.priority}
            onChange={(value) => onSelectChange('priority', value)}
            buttonClassName={clsx(
              'w-fit min-w-[136px] h-[44px]',
              getPriorityColor(editForm.priority || taskLog.priority)
            )}
            renderButtonContent={(selectedOption) => (
              <span className="flex gap-2 items-center">
                {selectedOption?.icon}
                {selectedOption?.label}
              </span>
            )}
            dropdownClassName="w-[150px]!"
          />
        ) : (
          <span
            className={clsx(
              'inline-flex items-center gap-2 px-6 h-[44px] rounded-full text-sm font-medium',
              getPriorityColor(taskLog.priority)
            )}
          >
            {getPriorityIcon(taskLog.priority)}
            {taskLog.priority}
          </span>
        )}
      </div>
    </div>
  );
};

export default TaskLogStatusPriority;