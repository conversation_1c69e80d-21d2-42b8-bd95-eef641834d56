import React from 'react';
import { AlertTriangle } from 'lucide-react';

// Loading Skeleton Component
export const LogDetailsSkeleton: React.FC = () => {
  return (
    <div className="flex overflow-y-auto flex-col flex-1 gap-8 px-8 pb-20">
      {/* Header Skeleton */}
      <div className="py-4 w-full border-b border-gray-200">
        <div className="flex justify-between items-center w-full">
          <div className="flex gap-4 items-center">
            <div className="w-24 h-6 bg-gray-200 rounded animate-pulse" />
          </div>
          <div className="flex gap-3 items-center">
            <div className="w-20 h-10 bg-gray-200 rounded-full animate-pulse" />
            <div className="w-20 h-10 bg-gray-200 rounded-full animate-pulse" />
          </div>
        </div>
      </div>

      {/* Main Content Skeleton */}
      <div className="flex-1 w-full">
        <div className="w-full max-w-full">
          {/* Top Section Skeleton */}
          <div className="grid grid-cols-3 gap-6 mb-8 w-full">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="p-4 bg-gray-200 rounded-lg animate-pulse">
                <div className="mb-2 w-20 h-4 bg-gray-200 rounded" />
                <div className="w-16 h-6 bg-gray-300 rounded" />
              </div>
            ))}
          </div>

          {/* Task Title Skeleton */}
          <div className="mb-8 w-full">
            <div className="mb-3 w-20 h-4 bg-gray-200 rounded animate-pulse" />
            <div className="w-full h-8 bg-[] rounded-lg animate-pulse" />
          </div>

          {/* Description Skeleton */}
          <div className="mb-8 w-full">
            <div className="mb-3 w-20 h-4 bg-gray-200 rounded animate-pulse" />
            <div className="p-4 bg-[] rounded-lg animate-pulse">
              <div className="space-y-3">
                <div className="w-full h-4 bg-gray-300 rounded" />
                <div className="w-full h-4 bg-gray-300 rounded" />
                <div className="w-3/4 h-4 bg-gray-300 rounded" />
                <div className="w-full h-4 bg-gray-300 rounded" />
                <div className="w-2/3 h-4 bg-gray-300 rounded" />
              </div>
            </div>
          </div>

          {/* Status and Priority Skeleton */}
          <div className="grid grid-cols-2 gap-6 mb-8 w-full">
            {[...Array(2)].map((_, i) => (
              <div key={i}>
                <div className="mb-3 w-16 h-4 bg-gray-200 rounded animate-pulse" />
                <div className="w-24 h-10 bg-gray-200 rounded-lg animate-pulse" />
              </div>
            ))}
          </div>

          {/* Additional Fields Skeleton */}
          <div className="space-y-6 w-full">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="p-4 bg-gray-200 rounded-lg animate-pulse">
                <div className="w-32 h-6 bg-gray-300 rounded" />
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

// Error State Component
export const LogDetailsError: React.FC<{
  message: string;
  onRetry?: () => void;
  onBack?: () => void;
}> = ({ message, onRetry, onBack }) => {
  return (
    <div className="flex justify-center items-center min-h-screen">
      <div className="p-6 mx-auto w-full text-center">
        <div className="flex justify-center items-center mx-auto mb-6 w-16 h-16 bg-red-100 rounded-full">
          <AlertTriangle className="w-8 h-8 text-red-600" />
        </div>

        <h1 className="mb-4 text-2xl font-semibold text-gray-900">
          Unable to Load Task Log
        </h1>

        <p className="mb-8 text-gray-600">{message}</p>

        <div className="flex gap-3 justify-center">
          {onBack && (
            <button
              onClick={onBack}
              className="px-6 py-2 text-gray-700 rounded-lg border border-gray-300 transition-colors hover:bg-gray-50"
            >
              Go Back
            </button>
          )}

          {onRetry && (
            <button
              onClick={onRetry}
              className="px-6 py-2 text-white bg-orange-500 rounded-lg transition-colors hover:bg-orange-600"
            >
              Try Again
            </button>
          )}
        </div>
      </div>
    </div>
  );
};