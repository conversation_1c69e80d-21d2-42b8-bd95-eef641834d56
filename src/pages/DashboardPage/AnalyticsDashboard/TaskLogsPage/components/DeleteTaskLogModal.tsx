import React from 'react';

interface DeleteTaskLogModalProps {
  isOpen: boolean;
  isDeleting: boolean;
  onConfirm: () => void;
  onCancel: () => void;
}

const DeleteTaskLogModal: React.FC<DeleteTaskLogModalProps> = ({
  isOpen,
  isDeleting,
  onConfirm,
  onCancel,
}) => {
  if (!isOpen) return null;

  return (
    <div className="flex fixed inset-0 z-50 justify-center items-center p-4 backdrop-blur-sm bg-black/30 animate-fade-in">
      <div className="p-8 mx-4 w-full max-w-md bg-white rounded-2xl shadow-2xl animate-scale-in">
        <div className="mb-6 text-center">
          <div className="flex justify-center items-center mx-auto mb-4 w-20 h-20 bg-red-50 rounded-full">
            <svg
              className="w-10 h-10 text-red-500"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
              />
            </svg>
          </div>
          <h3 className="mb-3 text-xl font-bold text-gray-900">
            Delete Task Log
          </h3>
          <p className="text-base leading-relaxed text-gray-600">
            Are you sure you want to delete this task log?
          </p>
          <p className="mt-2 text-sm font-medium text-red-600">
            This action cannot be undone.
          </p>
        </div>

        <div className="grid grid-cols-2 gap-4 w-full">
          <button
            onClick={onCancel}
            className="px-6 py-3 text-base font-medium text-gray-700 bg-gray-100 rounded-xl border-2 border-gray-200 transition-all duration-200 hover:bg-gray-200 hover:border-gray-300 focus:outline-none focus:ring-4 focus:ring-gray-200 sm:order-1"
            disabled={isDeleting}
          >
            Cancel
          </button>
          <button
            onClick={onConfirm}
            className="px-6 py-3 text-base font-medium text-white bg-red-600 rounded-xl border-2 border-red-600 transition-all duration-200 hover:bg-red-700 hover:border-red-700 focus:outline-none focus:ring-4 focus:ring-red-200 disabled:opacity-50 disabled:cursor-not-allowed sm:order-2"
            disabled={isDeleting}
          >
            {isDeleting ? (
              <span className="flex items-center">
                <svg
                  className="mr-3 -ml-1 w-4 h-4 text-white animate-spin"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                >
                  <circle
                    className="opacity-25"
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    strokeWidth="4"
                  ></circle>
                  <path
                    className="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  ></path>
                </svg>
                Deleting...
              </span>
            ) : (
              'Delete Task Log'
            )}
          </button>
        </div>
      </div>
    </div>
  );
};

export default DeleteTaskLogModal;