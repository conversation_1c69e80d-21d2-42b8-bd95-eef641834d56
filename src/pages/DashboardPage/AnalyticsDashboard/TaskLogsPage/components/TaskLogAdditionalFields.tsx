import React from 'react';
import { TaskLogDetailsFormData } from '@/types/taskLog';
import clsx from 'clsx';
import { Icons } from '@/assets/icons/DashboardIcons';

interface TaskLogAdditionalFieldsProps {
  taskLog: TaskLogDetailsFormData;
  editForm: Partial<TaskLogDetailsFormData>;
  isEditing: boolean;
  onInputChange: (field: keyof TaskLogDetailsFormData, value: string) => void;
}

const TaskLogAdditionalFields: React.FC<TaskLogAdditionalFieldsProps> = ({
  taskLog,
  editForm,
  isEditing,
  onInputChange,
}) => {
  const fieldConfig = [
    {
      key: 'assignedBy' as keyof TaskLogDetailsFormData,
      label: 'Assigned By',
      icon: Icons.AssignedByIcon,
      value: taskLog.assignedBy,
      editValue: editForm.assignedBy,
      placeholder: 'Enter assignee name',
    },
    {
      key: 'type' as keyof TaskLogDetailsFormData,
      label: 'Type',
      icon: Icons.TypeIcon,
      value: taskLog.type,
      editValue: editForm.type,
      placeholder: 'Enter task type',
    },
    {
      key: 'escalationPolicy' as keyof TaskLogDetailsFormData,
      label: 'Escalation Policy',
      icon: Icons.EscalationPolicyIcon,
      value: taskLog.escalationPolicy,
      editValue: editForm.escalationPolicy,
      placeholder: 'Enter escalation policy',
    },
  ];

  return (
    <div className="space-y-4">
      {fieldConfig.map(
        ({ key, label, icon: Icon, value, editValue, placeholder }) => (
          <div
            key={key}
            className={clsx(
              'rounded p-4 border',
              isEditing
                ? 'bg-[#FFFAF7] border-primary'
                : 'bg-[#FFFAF7] border-0'
            )}
          >
            <div className="flex gap-3 items-center">
              <Icon className="w-5 h-5 text-primary" />
              <span className="text-sm font-medium text-[#4F4F4F]">
                {label}:
              </span>
              {isEditing ? (
                <input
                  type="text"
                  value={editValue || ''}
                  onChange={(e) => onInputChange(key, e.target.value)}
                  className="flex-1 py-0 text-gray-900 bg-transparent border-0 outline-none"
                  placeholder={placeholder}
                />
              ) : (
                <span className="text-gray-900">{value}</span>
              )}
            </div>
          </div>
        )
      )}
    </div>
  );
};

export default TaskLogAdditionalFields;
