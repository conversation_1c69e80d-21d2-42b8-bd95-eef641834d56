import React, { useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import DataTable, { Column } from '@/components/ui/tables/DataTable';
import { useAnalyticsParams } from '@/utils/urlParams';
import { ROUTES } from '@/constants/routes';
import { useTaskLogsList } from '@/hooks/useTaskLog';
import { useTenant } from '@/context/TenantContext';
import {
  TaskLog,
  mapStatusToDisplay,
  mapPriorityToDisplay,
  TaskLogStatus,
  TaskLogPriority,
} from '@/types/taskLog';
import Pagination from '@/components/common/Pagination';
import { useDebounce } from '@/hooks/useDebounce';
import { TablePageContainer } from '@/components/layout/DashboardWithChatLayout';

interface TaskLogTableRow {
  id: string;
  dateCreated: string;
  time: string;
  taskTitle: string;
  assignedBy: string;
  type: string;
  priority: 'Low' | 'Medium' | 'High' | 'Critical' | 'Urgent';
  status: 'Completed' | 'In Progress' | 'Not Started' | 'Cancelled';
  escalationPolicy: string;
}

const TaskLogsPage: React.FC = () => {
  const { filters } = useAnalyticsParams();
  const navigate = useNavigate();
  const location = useLocation();
  const { tenantId } = useTenant();

  // Get search term from URL params
  const searchParams = new URLSearchParams(location.search);
  const searchTerm = searchParams.get('search') || '';

  // Debounce search term
  const debouncedSearchTerm = useDebounce(searchTerm, 700);

  // Pagination state
  const [currentPage, setCurrentPage] = useState<number>(1);
  const pageSize = 10;

  // Build filter using URL params and tenant - get filters from URL params
  const urlParams = new URLSearchParams(location.search);
  const statusFilter = (urlParams.get('status') as TaskLogStatus) || undefined;
  const priorityFilter =
    (urlParams.get('priority') as TaskLogPriority) || undefined;
  const fromDate = urlParams.get('from') || undefined;
  const toDate = urlParams.get('to') || undefined;

  const taskLogFilter = {
    search: debouncedSearchTerm,
    assignedTo: filters.agent || 'ALL', // Use 'ALL' instead of empty string
    tenantId: tenantId || '',
    status: statusFilter,
    priority: priorityFilter,
    from: fromDate,
    to: toDate,
    page: currentPage - 1, // Convert to 0-based indexing
    pageSize: pageSize,
  };

  // Use the hook to fetch task logs with filter - only when we have both required fields
  const { data: taskLogsResponse, isLoading } = useTaskLogsList(
    taskLogFilter,
    !!tenantId && !!taskLogFilter.assignedTo && taskLogFilter.assignedTo !== ''
  );

  const taskLogs = Array.isArray(taskLogsResponse)
    ? taskLogsResponse
    : taskLogsResponse?.tasks || [];
  const totalCount = taskLogsResponse?.total || taskLogs.length;
  const totalPages = Math.ceil(totalCount / pageSize);

  // Convert TaskLog data to table format
  const convertToTableData = (taskLogs: TaskLog[]): TaskLogTableRow[] => {
    return taskLogs.map((log) => ({
      id: log.id,
      dateCreated: new Date(log.createdAt).toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
        year: 'numeric',
      }),
      time: new Date(log.createdAt).toLocaleTimeString([], {
        hour: '2-digit',
        minute: '2-digit',
      }),
      taskTitle: log.taskTitle,
      assignedBy: log.assignedTo || 'Unassigned',
      type: log.taskType || 'General',
      priority: mapPriorityToDisplay(log.priority),
      status: mapStatusToDisplay(log.status),
      escalationPolicy: log.escalationPolicy || 'Default',
    }));
  };

  const tableData = convertToTableData(taskLogs);

  const columns: Column<TaskLogTableRow>[] = [
    {
      key: 'dateCreated',
      label: 'Date Created',
      sortable: true,
    },
    {
      key: 'time',
      label: 'Time (EST)',
      sortable: true,
    },
    {
      key: 'taskTitle',
      label: 'Task Title',
      sortable: true,
      render: (value) => (
        <div className="max-w-xs truncate" title={String(value)}>
          {String(value)}
        </div>
      ),
    },
    {
      key: 'assignedBy',
      label: 'Assigned By',
      sortable: true,
    },
    {
      key: 'type',
      label: 'Type',
      sortable: true,
    },
    {
      key: 'priority',
      label: 'Priority',
      sortable: true,
      render: (value) => {
        const priority = value as TaskLogTableRow['priority'];
        const colorMap = {
          Low: 'bg-green-500 text-white',
          Medium: 'bg-[#FBA320] text-white',
          High: 'bg-red-500 text-white',
          Critical: 'bg-red-600 text-white',
          Urgent: 'bg-purple-600 text-white',
        };
        return (
          <span className={`px-3 py-1.5 text-xs font-medium`}>{priority}</span>
        );
      },
    },
    {
      key: 'status',
      label: 'Status',
      sortable: true,
      render: (value) => {
        const status = value as TaskLogTableRow['status'];
        const colorMap = {
          Completed: 'bg-[#059669] text-white',
          'In Progress': 'bg-[#2563eb] text-white',
          'Not Started': 'bg-[#4F4F4F] text-white',
          Cancelled: 'bg-[#4b5563] text-white',
        };
        return (
          <span className={`px-3 py-1.5 text-xs font-medium `}>{status}</span>
        );
      },
    },
    {
      key: 'escalationPolicy',
      label: 'Escalation Policy',
      render: (value) => (
        <div className="max-w-xs truncate" title={String(value)}>
          {String(value)}
        </div>
      ),
    },
  ];

  const handleRowClick = (row: TaskLogTableRow) => {
    // Navigate to task log details page
    navigate(ROUTES.DASHBOARD_ANALYTICS_TASK_LOG_DETAILS(row.id));
  };

  return (
    <TablePageContainer>
      <DataTable
        data={tableData}
        columns={columns}
        onRowClick={handleRowClick}
        loading={isLoading}
        emptyMessage={
          searchTerm
            ? 'No task logs found matching your search'
            : 'No task logs found'
        }
        rowColoring={true}
        rowColoringType="odd"
        showCheckAll={false}
      />

      {/* Pagination */}
      {totalPages > 0 && !isLoading && (
        <div className="mt-4">
          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            onPageChange={setCurrentPage}
          />
        </div>
      )}
    </TablePageContainer>
  );
};

export default TaskLogsPage;