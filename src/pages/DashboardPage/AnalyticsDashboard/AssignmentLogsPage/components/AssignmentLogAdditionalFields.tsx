import clsx from 'clsx';
import React from 'react';

import { Icons } from '@/assets/icons/DashboardIcons';
import { AssignmentLogDetailsFormData } from '@/types/assignmentLog';

interface AssignmentLogAdditionalFieldsProps {
  log: AssignmentLogDetailsFormData;
  editForm: Partial<AssignmentLogDetailsFormData>;
  isEditing: boolean;
  onInputChange: (
    field: keyof AssignmentLogDetailsFormData,
    value: string
  ) => void;
}

const AssignmentLogAdditionalFields: React.FC<
  AssignmentLogAdditionalFieldsProps
> = ({ log, editForm, isEditing, onInputChange }) => {
  const fieldConfig = [
    {
      key: 'assignedTo' as keyof AssignmentLogDetailsFormData,
      label: 'Assigned To',
      icon: Icons.AssignedByIcon,
      value: log.createdBy,
      editValue: editForm.createdBy,
      placeholder: 'Enter assignee name',
    },
    {
      key: 'type' as keyof AssignmentLogDetailsFormData,
      label: 'Type',
      icon: Icons.TypeIcon,
      value: log.type,
      editValue: editForm.type,
      placeholder: 'Enter task type',
    },
    {
      key: 'escalationPolicy' as keyof AssignmentLogDetailsFormData,
      label: 'Escalation Policy',
      icon: Icons.EscalationPolicyIcon,
      value: log.escalationPolicy,
      editValue: editForm.escalationPolicy,
      placeholder: 'Enter escalation policy',
    },
  ];

  return (
    <div className="space-y-4">
      {fieldConfig.map(
        ({ key, label, icon: Icon, value, editValue, placeholder }) => (
          <div
            key={key}
            className={clsx(
              'rounded border p-4',
              isEditing
                ? 'border-primary bg-[#FFFAF7]'
                : 'border-0 bg-[#FFFAF7]'
            )}
          >
            <div className="flex items-center gap-3">
              <Icon className="h-5 w-5 text-primary" />
              <span className="text-sm font-medium text-[#0F0006]">
                {label}:
              </span>
              {isEditing ? (
                <input
                  type="text"
                  value={editValue || ''}
                  onChange={e => onInputChange(key, e.target.value)}
                  className="flex-1 border-0 bg-transparent py-0 text-gray-900 outline-none"
                  placeholder={placeholder}
                />
              ) : (
                <span className="capitalize text-subText">{value}</span>
              )}
            </div>
          </div>
        )
      )}
    </div>
  );
};

export default AssignmentLogAdditionalFields;
