import React from 'react';
import clsx from 'clsx';
import { AssignmentLogDetailsFormData } from '@/types/assignmentLog';

interface LogBasicInfoProps {
  log: AssignmentLogDetailsFormData;
  editForm: Partial<AssignmentLogDetailsFormData>;
  isEditing: boolean;
  onInputChange: (field: keyof AssignmentLogDetailsFormData, value: string) => void;
}

const AssignmentLogBasicInfo: React.FC<LogBasicInfoProps> = ({
  log,
  editForm,
  isEditing,
  onInputChange,
}) => {
  return (
    <div className="space-y-8">
      {/* Top Section - Time, Date Created, Due Date */}
      <div className="grid grid-cols-3 gap-6 mb-8">
        <div className="p-4 bg-[#FFFAF7] rounded-lg border border-[#DFEAF2]">
          <h3 className="mb-2 text-sm font-medium sm:text-base text-grayTen">
            Time (EST)
          </h3>
          <p className="text-base text-subText">{log.time}</p>
        </div>

        <div className="p-4 bg-[#FFFAF7] rounded-lg border border-[#DFEAF2]">
          <h3 className="mb-2 text-sm font-medium sm:text-base text-grayTen">
            Date Created
          </h3>
          <p className="text-base text-subText">{log.dateCreated}</p>
        </div>

        <div
          className={clsx(
            'rounded-lg p-4',
            'bg-[#FFFAF7] border border-[#DFEAF2]',
            isEditing && 'bg-[#FFFAF7] border-primary'
          )}
        >
          <h3 className="mb-2 text-sm font-medium sm:text-base text-grayTen">
            Due Date
          </h3>
          {isEditing ? (
            <input
              type="date"
              value={editForm.dueDate || ''}
              onChange={(e) => onInputChange('dueDate', e.target.value)}
              className="p-3 w-full text-base bg-white rounded-none border outline-none border-primary focus:border-primary focus:outline-none text-subText"
              placeholder="Enter due date"
            />
          ) : (
            <p className="text-base text-subText">{log.dueDate}</p>
          )}
        </div>
      </div>

      {/* Task Title */}
      <div className="p-4 rounded-lg border border-[#DFEAF2]">
        <h3 className="mb-3 text-sm font-medium text-grayTen">Task Title</h3>
        {isEditing ? (
          <input
            type="text"
            value={editForm.taskTitle || ''}
            onChange={(e) => onInputChange('taskTitle', e.target.value)}
            className="p-3 w-full text-base bg-white rounded-none border outline-none border-primary focus:border-primary focus:outline-none text-subText"
            placeholder="Enter task title"
          />
        ) : (
          <p className="text-base text-subText">{log.taskTitle}</p>
        )}
      </div>

      {/* Description */}
      <div className="p-4 rounded-lg border border-[#DFEAF2]">
        <h3 className="mb-3 text-sm font-medium text-grayTen">Description</h3>
        {isEditing ? (
          <textarea
            value={editForm.description || ''}
            onChange={(e) => onInputChange('description', e.target.value)}
            rows={8}
            className="p-3 w-full bg-white rounded-none border outline-none resize-none focus:border-primary focus:outline-none focus:ring-0 border-primary text-subText"
            placeholder="Enter task description"
          />
        ) : (
          <p className="leading-relaxed text-subText">{log.description}</p>
        )}
      </div>
    </div>
  );
};

export default AssignmentLogBasicInfo;