import React, { useState } from 'react';
import { AgentCard } from '../AiAgentsPage';
import { ROUTES } from '../../constants/routes';
import clsx from 'clsx';
import { dashboardIcons } from '../../assets/images';
import { useGetAIAgentsData } from '../../hooks/useAIAgents';
import { Link, useNavigate } from 'react-router-dom';
import AgentSuiteSkeletonLoader from '../../components/ui/AgentSuiteSkeleton';
import { agentSuites as mockAgentsSuites } from '../../data/constants';
import { useGetUserProfile } from '../../hooks/useUserProfile';
import { UserBasicInfoPayload } from '../../types/user';
import { getUrlWithParams } from '../../utils/urlParams';
import { useTenant } from '@/context/TenantContext';

type DashboardTab = { label: string; value: string };

interface DashboardWelcomeProps {
  onServiceSelect?: () => void;
}

const DashboardWelcome: React.FC<DashboardWelcomeProps> = () => {
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState<DashboardTab>({
    label: 'ai-agents-suite-dashboard',
    value: 'AI Agents Suite dashboard',
  });
  const { agents, agentSuites, isLoadingAgents, isLoadingSuites, error } =
    useGetAIAgentsData();
  const { setActiveAgent } = useTenant();
  const { data: userData, isLoading: isLoadingUser } =
    useGetUserProfile<UserBasicInfoPayload>();

  // Determine if user has claimed tenants
  const hasClaimedTenants =
    userData?.userInfo?.tenant?.claimedAgentSuites?.length &&
    userData.userInfo.tenant.claimedAgentSuites.length > 0;

  // Get user's tenant agents from the /me endpoint
  const userTenantAgents = userData?.tenantAgents || [];

  // Get claimed suites - suites that the user has claimed
  const claimedSuites = userData?.userInfo?.tenant?.claimedAgentSuites || [];

  // Determine which suites to show based on tenant status
  const suitesToShow = hasClaimedTenants
    ? claimedSuites.map((cs) => cs.suite)
    : agentSuites;

  // Determine which agents to show based on tenant status
  const agentsToShow = hasClaimedTenants ? userTenantAgents : agents;

  // Handle agents suite card click
  const redirectToSuite = (suite: any) => {
    if (hasClaimedTenants) {
      // If user has claimed tenants, redirect to new analytics insights dashboard
      return ROUTES.DASHBOARD_ANALYTICS_INSIGHTS;
    } else {
      // If not claimed, go to activation page
      return ROUTES.DASHBOARD_ANALYTICS_ACTIVATE_SUITE(suite.agentSuiteKey);
    }
  };

  const tabs: DashboardTab[] =
    hasClaimedTenants && !isLoadingUser
      ? [
          {
            label: 'ai-agents-suite-dashboard',
            value: 'AI Agents Suite Dashboard',
          },
          { label: 'agent-task-logs', value: 'Agent Task Logs' },
          { label: 'agent-assignment-logs', value: 'Agent Assignment Logs' },
        ]
      : [
          {
            label: 'ai-agents-suite-dashboard',
            value: 'AI Agents Suite Dashboard',
          },
          { label: 'agentic-ai-dashboard', value: 'Agentic AI Dashboard' },
        ];

  // Handle agent card click navigation
  const handleAgentCardClick = (agent: any) => {
    setActiveAgent(agent.agentKey);
    const queryParams = { suite: 'set-iq', agent: agent.agentKey };

    // Navigate based on current active tab
    switch (activeTab.label) {
      case 'agent-task-logs':
        navigate(
          getUrlWithParams(ROUTES.DASHBOARD_ANALYTICS_TASK_LOGS, queryParams)
        );
        break;
      case 'agent-assignment-logs':
        navigate(
          getUrlWithParams(
            ROUTES.DASHBOARD_ANALYTICS_ASSIGNMENT_LOGS,
            queryParams
          )
        );
        break;
      default:
        // Default behavior for other tabs - go to insights
        navigate(
          getUrlWithParams(ROUTES.DASHBOARD_ANALYTICS_INSIGHTS, queryParams)
        );
        break;
    }
  };

  return (
    <div className="flex flex-col gap-8 p-8 h-full">
      {/* Header */}
      <div className="text-start">
        <h1 className="mb-2 text-2xl font-semibold text-blackOne">Dashboard</h1>

        {/* Hero Section */}
        <div className="flex h-[140px] max-w-4xl items-center overflow-hidden rounded-2xl bg-[#040721] text-white">
          <div className="flex justify-between items-center w-full">
            <div className="p-6 text-left">
              <h2 className="mb-2 text-lg font-bold">
                Actionable Intelligence across all Agentic AI agents.
              </h2>
              <p className="text-gray-300">
                Compare performance, monitor activity, and act on daily
                insights.
              </p>
            </div>
            <div className="relative mr-8 h-full w-[158px]">
              <img src={dashboardIcons} alt="bg" className="w-full h-full" />
            </div>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="flex space-x-1 w-full max-w-4xl border-b border-gray-200">
        {tabs.map((tab) => (
          <button
            key={tab.label}
            onClick={() => setActiveTab(tab)}
            className={clsx(
              'px-4 py-2 text-sm font-medium transition-colors',
              activeTab.label === tab.label
                ? 'border-b-2 border-primary text-primary'
                : 'text-gray-600 hover:text-blackOne'
            )}
          >
            {tab.value}
          </button>
        ))}
      </div>

      {/* Error State */}
      {error && (
        <div className="p-4 mb-4 text-red-700 bg-red-50 rounded-lg border border-red-200">
          <p>Error loading agent suites: {error.message}</p>
        </div>
      )}

      {/* Content */}
      <div className="w-full max-w-4xl">
        {activeTab.label === 'ai-agents-suite-dashboard' && (
          <>
            {isLoadingSuites || isLoadingUser ? (
              <AgentSuiteSkeletonLoader count={4} />
            ) : (
              <>
                {suitesToShow.length === 0 ? (
                  <div className="flex flex-col justify-center items-center py-12">
                    <p className="mb-4 text-lg text-gray-600">
                      {hasClaimedTenants
                        ? "You haven't claimed any agent suites yet."
                        : 'No agent suites available.'}
                    </p>
                    {!hasClaimedTenants && (
                      <p className="text-sm text-gray-500">
                        Activate a suite to get started with your agents.
                      </p>
                    )}
                  </div>
                ) : (
                  <div className="grid grid-cols-1 gap-8 lg:grid-cols-2">
                    {suitesToShow.map((suite) => (
                      <Link
                        to={redirectToSuite(suite)}
                        key={suite.agentSuiteKey}
                        className={clsx(
                          'flex overflow-hidden items-center w-full bg-white rounded-lg border border-transparent transition-shadow h-[195px] max-w-[334px] hover:border-primary',
                          'cursor-pointer'
                        )}
                      >
                        {/* Agent Image */}
                        <div className="h-full w-[111px] flex-shrink-0">
                          <img
                            src={suite.avatar}
                            alt={suite.agentSuiteName}
                            className="object-cover w-full h-full"
                            onError={(e) => {
                              // Fallback to mock logo if agent avatar fails to load
                              (e.target as HTMLImageElement).src =
                                mockAgentsSuites.filter(
                                  (agent) =>
                                    agent.id.toLowerCase() ===
                                    suite.agentSuiteKey.toLowerCase()
                                )[0]?.image || '';
                            }}
                          />
                        </div>

                        {/* Agent Content */}
                        <div className="p-4 h-full bg-white">
                          <div className="flex justify-between items-center mb-2">
                            <h3 className="text-base font-bold font-spartan text-blackOne">
                              {suite.agentSuiteName}
                            </h3>
                          </div>
                          <h4 className="mb-2 text-sm font-semibold font-spartan text-subText">
                            {suite.description}
                          </h4>
                          <p className="text-sm text-subText">
                            {suite.roleDescription}
                          </p>
                        </div>
                      </Link>
                    ))}
                  </div>
                )}
              </>
            )}
          </>
        )}

        {activeTab.label === 'agentic-ai-dashboard' && (
          <>
            {isLoadingAgents || isLoadingUser ? (
              <AgentSuiteSkeletonLoader count={4} />
            ) : (
              <>
                {agentsToShow.length === 0 ? (
                  <div className="flex flex-col justify-center items-center py-12">
                    <p className="mb-4 text-lg text-gray-600">
                      {hasClaimedTenants
                        ? 'No agents available in your claimed suites.'
                        : 'No agents available.'}
                    </p>
                    {!hasClaimedTenants && (
                      <p className="text-sm text-gray-500">
                        Activate a suite to access agents.
                      </p>
                    )}
                  </div>
                ) : (
                  <div className="grid grid-cols-1 gap-8 md:grid-cols-2">
                    {agentsToShow.map((agent) => (
                      <AgentCard
                        key={agent.agentKey}
                        className="w-full max-w-[334px]"
                        agent={agent}
                        link="#" // We'll handle navigation with onAgentSelect
                        onAgentSelect={() => handleAgentCardClick(agent)}
                      />
                    ))}
                  </div>
                )}
              </>
            )}
          </>
        )}

        {activeTab.label === 'agent-insights' && (
          <div className="flex flex-col justify-center items-center py-12">
            <p className="mb-4 text-lg text-gray-600">
              Agent Insights available in the dedicated analytics section
            </p>
            <p className="text-sm text-gray-500">
              Click on an agent card below to view detailed insights for that
              agent
            </p>
          </div>
        )}

        {activeTab.label === 'agent-task-logs' && (
          <>
            {isLoadingAgents || isLoadingUser ? (
              <AgentSuiteSkeletonLoader count={4} />
            ) : (
              <>
                {userTenantAgents.length === 0 ? (
                  <div className="flex flex-col justify-center items-center py-12">
                    <p className="mb-4 text-lg text-gray-600">
                      {hasClaimedTenants
                        ? 'No agents available in your claimed suites.'
                        : 'No agents available.'}
                    </p>
                    {!hasClaimedTenants && (
                      <p className="text-sm text-gray-500">
                        Activate a suite to access agents.
                      </p>
                    )}
                  </div>
                ) : (
                  <div className="grid grid-cols-1 gap-8 md:grid-cols-2">
                    {userTenantAgents.map((agent) => (
                      <AgentCard
                        key={agent.agentKey}
                        className="w-full max-w-[334px]"
                        agent={agent}
                        link="#" // We'll handle navigation with onAgentSelect
                        onAgentSelect={() => handleAgentCardClick(agent)}
                      />
                    ))}
                  </div>
                )}
              </>
            )}
          </>
        )}

        {activeTab.label === 'agent-assignment-logs' && (
          <>
            {isLoadingAgents || isLoadingUser ? (
              <AgentSuiteSkeletonLoader count={4} />
            ) : (
              <>
                {userTenantAgents.length === 0 ? (
                  <div className="flex flex-col justify-center items-center py-12">
                    <p className="mb-4 text-lg text-gray-600">
                      {hasClaimedTenants
                        ? 'No agents available in your claimed suites.'
                        : 'No agents available.'}
                    </p>
                    {!hasClaimedTenants && (
                      <p className="text-sm text-gray-500">
                        Activate a suite to access agents.
                      </p>
                    )}
                  </div>
                ) : (
                  <div className="grid grid-cols-1 gap-8 md:grid-cols-2">
                    {userTenantAgents.map((agent) => (
                      <AgentCard
                        key={agent.agentKey}
                        className="w-full max-w-[334px]"
                        agent={agent}
                        link="#" // We'll handle navigation with onAgentSelect
                        onAgentSelect={() => handleAgentCardClick(agent)}
                      />
                    ))}
                  </div>
                )}
              </>
            )}
          </>
        )}
      </div>
    </div>
  );
};

export default DashboardWelcome;
