import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';

import AgentSelectionLayout from '@/components/layout/AgentSelectionLayout';
import { useTenant } from '@/context/TenantContext';

import { dashboardIcons } from '../../assets/images';
import { ROUTES } from '../../constants/routes';
import { useGetAIAgentsData } from '../../hooks/useAgents';
import { useGetUserProfile } from '../../hooks/useUserProfile';
import { UserBasicInfoPayload } from '../../types/user';
import { getUrlWithParams } from '../../utils/urlParams';

type DashboardTab = { label: string; value: string };

const DashboardWelcome: React.FC = () => {
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState<DashboardTab>({
    label: 'ai-agents-suite-dashboard',
    value: 'AI Agents Suite dashboard',
  });
  const { agents, agentSuites, isLoadingAgents, isLoadingSuites, error } =
    useGetAIAgentsData();
  const { setActiveAgent } = useTenant();
  const { data: userData, isLoading: isLoadingUser } =
    useGetUserProfile<UserBasicInfoPayload>();

  // Determine if user has claimed tenants
  const hasClaimedTenants =
    userData?.userInfo?.tenant?.claimedAgentSuites?.length &&
    userData.userInfo.tenant.claimedAgentSuites.length > 0;

  // Get user's tenant agents from the /me endpoint
  const userTenantAgents = userData?.tenantAgents || [];

  // Get claimed suites - suites that the user has claimed
  const claimedSuites = userData?.userInfo?.tenant?.claimedAgentSuites || [];

  // Determine which suites to show based on tenant status
  const suitesToShow = hasClaimedTenants
    ? claimedSuites.map(cs => cs.suite)
    : agentSuites;

  // Determine which agents to show based on tenant status
  const agentsToShow = hasClaimedTenants ? userTenantAgents : agents;

  // Handle agents suite card click
  const redirectToSuite = (suite: any) => {
    if (hasClaimedTenants) {
      // If user has claimed tenants, redirect to new analytics insights dashboard
      return ROUTES.DASHBOARD_ANALYTICS_INSIGHTS;
    } else {
      // If not claimed, go to activation page
      return ROUTES.DASHBOARD_ANALYTICS_ACTIVATE_SUITE(suite.agentSuiteKey);
    }
  };

  const tabs: DashboardTab[] =
    hasClaimedTenants && !isLoadingUser
      ? [
          {
            label: 'ai-agents-suite-dashboard',
            value: 'AI Agents Suites',
          },
          { label: 'agent-task-logs', value: 'Task Logs' },
          { label: 'agent-assignment-logs', value: 'Assignment Logs' },
        ]
      : [
          {
            label: 'ai-agents-suite-dashboard',
            value: 'AI Agents Suite Dashboard',
          },
          { label: 'agentic-ai-dashboard', value: 'Agentic AI Dashboard' },
        ];

  // Handle agent card click navigation
  const handleAgentCardClick = (agent: any) => {
    setActiveAgent(agent.agentKey);
    const queryParams = { suite: 'set-iq', agent: agent.agentKey };

    // Navigate based on current active tab
    switch (activeTab.label) {
      case 'agent-task-logs':
        navigate(
          getUrlWithParams(ROUTES.DASHBOARD_ANALYTICS_TASK_LOGS, queryParams)
        );
        break;
      case 'agent-assignment-logs':
        navigate(
          getUrlWithParams(
            ROUTES.DASHBOARD_ANALYTICS_ASSIGNMENT_LOGS,
            queryParams
          )
        );
        break;
      default:
        // Default behavior for other tabs - go to insights
        navigate(
          getUrlWithParams(ROUTES.DASHBOARD_ANALYTICS_INSIGHTS, queryParams)
        );
        break;
    }
  };

  return (
    <>
      <div className="hidden h-full flex-col gap-8 p-8">
        {/* Header */}
        <div className="text-start">
          <h1 className="mb-2 text-2xl font-semibold text-blackOne">
            Dashboard
          </h1>

          {/* Hero Section */}
          <div className="flex h-[140px] max-w-4xl items-center overflow-hidden rounded-2xl bg-[#040721] text-white">
            <div className="flex w-full items-center justify-between">
              <div className="p-6 text-left">
                <h2 className="mb-2 text-lg font-bold">
                  Actionable Intelligence across all Agentic AI agents.
                </h2>
                <p className="text-gray-300">
                  Compare performance, monitor activity, and act on daily
                  insights.
                </p>
              </div>
              <div className="relative mr-8 h-full w-[158px]">
                <img src={dashboardIcons} alt="bg" className="h-full w-full" />
              </div>
            </div>
          </div>
        </div>
      </div>
      <AgentSelectionLayout
        title="Actionable Intelligence across all Agentic AI agents."
        description="Compare performance, monitor activity, and act on daily insights."
        className=" w-full bg-[#040721] text-white"
        bgImage={dashboardIcons}
        pageType="dashboard"
        onAgentSuiteClick={suite => {
          if (!hasClaimedTenants) {
            navigate(
              ROUTES.DASHBOARD_ANALYTICS_ACTIVATE_SUITE(suite.agentSuiteKey)
            );
          } else {
            navigate(ROUTES.DASHBOARD_ANALYTICS_INSIGHTS, {
              state: { suite },
            });
          }
        }}
      />
    </>
  );
};

export default DashboardWelcome;
