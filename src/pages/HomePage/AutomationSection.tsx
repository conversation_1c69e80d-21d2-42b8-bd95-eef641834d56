export const AutomationSection = () => {
  return (
    <section className="relative h-[400px] overflow-hidden font-inter text-white">
      <div className="container relative z-10 mx-auto h-full max-w-screen-3xl bg-[#000307]">
        {/* Content Container */}
        <div className="relative z-10 mx-auto h-full max-w-screen-2xl px-4">
          <div className="flex h-full flex-col md:flex-row">
            <div className="flex h-full w-full items-center justify-center py-8 text-center md:py-12">
              <div className="rounded-xl bg-[linear-gradient(180deg,#FFFFFF0D,#FFFFFF00)] py-8 md:max-w-[900px]">
                <p className="mb-5 text-lg font-medium text-primary">
                  Beyond Automation
                </p>
                <h2 className="mb-5 text-[32px] font-bold leading-tight">
                  Empowering Agentic AI Transformation
                </h2>
                <p className="text-lg">
                  PivoTL agents don't just complete tasks—they collaborate,
                  score, escalate, and execute workflows across your most
                  critical business functions.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};
