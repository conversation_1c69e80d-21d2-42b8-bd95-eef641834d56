import { ChevronLeft, ChevronRight } from 'lucide-react';
import { useEffect, useRef, useState } from 'react';

import AgentSkeleton from '@/components/ui/AgentSkeleton';

import {
  agentCategories,
  marketplaceAgents as mockAgents,
} from '../../data/constants';
import { useGetAIAgents } from '../../hooks/useAgents';

export const MarketplaceSection = () => {
  const [showLeftArrow, setShowLeftArrow] = useState(false);
  const [showRightArrow, setShowRightArrow] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const scrollRef = useRef<HTMLDivElement>(null);

  // Use React Query hook for data fetching
  const { data: agents = [], isLoading } = useGetAIAgents();
  const [selectedCategory, setSelectedCategory] = useState('all');

  // Compute filtered agents based on selected category
  const agentsList =
    selectedCategory === 'all'
      ? agents
      : agents.filter(agent =>
          agent.categories.some(c => c === selectedCategory)
        );

  // Check if device is mobile
  useEffect(() => {
    const checkIsMobile = () => {
      setIsMobile(window.innerWidth < 1024); // lg breakpoint
    };

    checkIsMobile();
    window.addEventListener('resize', checkIsMobile);

    return () => window.removeEventListener('resize', checkIsMobile);
  }, []);

  // Initialize arrow visibility based on content and screen size
  useEffect(() => {
    if (scrollRef.current && agentsList.length > 0) {
      const { scrollLeft, scrollWidth, clientWidth } = scrollRef.current;

      // Determine if right arrow should be shown based on array length and screen size
      const shouldShowRightArrow = isMobile
        ? agentsList.length > 1
        : agentsList.length > 4;

      setShowLeftArrow(scrollLeft > 0);
      setShowRightArrow(
        shouldShowRightArrow && scrollLeft < scrollWidth - clientWidth - 1
      );
    }
  }, [agentsList.length, isMobile]);

  const handleScroll = () => {
    if (scrollRef.current) {
      const { scrollLeft, scrollWidth, clientWidth } = scrollRef.current;

      // Determine if right arrow should be shown based on array length and screen size
      const shouldShowRightArrow = isMobile
        ? agentsList.length > 1
        : agentsList.length > 4;

      setShowLeftArrow(scrollLeft > 0);
      setShowRightArrow(
        shouldShowRightArrow && scrollLeft < scrollWidth - clientWidth - 1
      );
    }
  };

  const scroll = (direction: 'left' | 'right') => {
    if (scrollRef.current) {
      const scrollAmount = direction === 'left' ? -300 : 300;
      scrollRef.current.scrollBy({
        left: scrollAmount,
        behavior: 'smooth',
      });
    }
  };

  const handleAgentsFilter = (categoryId: string) => {
    if (categoryId === selectedCategory) {
      setSelectedCategory('all');
    } else {
      setSelectedCategory(categoryId);
    }
  };

  return (
    <section className="bg-white pb-8 pt-12">
      <div className="container relative z-10 mx-auto max-w-screen-2xl px-4">
        <div className="text-center">
          <h2 className="mb-4 text-center text-3xl font-bold capitalize">
            Agentic AI marketplace for Enterprise automation
          </h2>
          <p className="mx-auto mb-6 text-center font-inter text-lg text-gray-600">
            Deploy AI agents that work around the clock — scoring leads,
            resolving tasks, and scaling operations with precision.
          </p>
        </div>

        <div className="mb-6 flex flex-wrap justify-center gap-4 bg-darkGray p-4">
          {agentCategories.map((category, index) => (
            <button
              key={index}
              className={`rounded-md ${
                selectedCategory === category.id
                  ? 'bg-primary text-white hover:bg-darkOrangeTwo'
                  : 'bg-grayFifteen text-blackOne hover:bg-blue-50'
              } px-4 py-2.5 font-inter font-medium transition
              ${index === 0 ? 'ml-0' : 'ml-2'}`}
              onClick={() => handleAgentsFilter(category.id)}
            >
              {category.alias}
            </button>
          ))}
        </div>

        <div className="relative w-full">
          {/* Left Arrow */}
          {!isLoading && showLeftArrow && (
            <button
              onClick={() => scroll('left')}
              className="absolute left-2 top-1/2 z-10 -translate-y-1/2 rounded-full bg-white p-2 shadow-md hover:bg-gray-100"
              aria-label="Scroll left"
            >
              <ChevronLeft className="h-8 w-8" />
            </button>
          )}

          {/* AI Agents */}
          <div className="flex justify-center">
            <div
              ref={scrollRef}
              onScroll={handleScroll}
              className={`no-scrollbar flex snap-x snap-mandatory gap-6 overflow-x-auto scroll-smooth px-4 pb-6 pt-2 ${
                !isLoading &&
                agents.length < 5 &&
                'justify-start lg:justify-center'
              }`}
            >
              {isLoading ? (
                Array.from({ length: 4 }).map((_, index) => (
                  <AgentSkeleton key={`skeleton-${index}`} />
                ))
              ) : agentsList.length === 0 ? (
                <div className="flex flex-col items-center justify-center">
                  {Array.from({ length: 1 }).map((_, index) => (
                    <AgentSkeleton key={`skeleton-${index}`} />
                  ))}
                  <p className="py-6 text-center font-medium text-gray-500">
                    No agents available yet for this category
                  </p>
                </div>
              ) : (
                agentsList.map((agent, index) => (
                  <div
                    key={index}
                    className="flex w-[290px] min-w-[290px] cursor-pointer flex-col overflow-hidden rounded border bg-white shadow-sm transition-all hover:shadow-md"
                  >
                    <div className="bg-peachTwo">
                      <img
                        src={agent.avatar || '/placeholder-agent.png'}
                        className="h-56 w-full object-contain"
                        alt={agent.agentName}
                        onError={e => {
                          // Fallback to mock logo if agent avatar fails to load
                          const mockAgent = mockAgents.find(
                            mockAgent =>
                              mockAgent.name.toLowerCase() ===
                              agent.agentName.toLowerCase()
                          );
                          (e.target as HTMLImageElement).src =
                            mockAgent?.image || '/placeholder-agent.png';
                        }}
                      />
                    </div>
                    <div className="flex flex-1 flex-col gap-2 p-4">
                      <div className="w-fit rounded border border-grayNine bg-grayNineTeen px-2 py-[2px] font-bold">
                        {agent.agentName}
                      </div>
                      <p className="mt-1 text-lg font-semibold">
                        {agent.description}
                      </p>
                      <p className="font-inter text-darkGray">
                        {agent.roleDescription}
                      </p>
                    </div>
                  </div>
                ))
              )}
            </div>
          </div>

          {/* Right Arrow */}
          {!isLoading && showRightArrow && (
            <button
              onClick={() => scroll('right')}
              className="absolute right-2 top-1/2 z-10 -translate-y-1/2 rounded-full bg-white p-2 shadow-md hover:bg-gray-100"
              aria-label="Scroll right"
            >
              <ChevronRight className="h-8 w-8" />
            </button>
          )}
        </div>
      </div>
    </section>
  );
};
