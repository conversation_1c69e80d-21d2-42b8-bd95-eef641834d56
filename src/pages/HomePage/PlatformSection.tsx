import AutoPlayVideo from '@/components/ui/AutoPlayVideo';
import { earthGlobal } from '../../assets/videos';

export const PlatformSection = () => {
  return (
    <section className="mt-10 font-inter text-white">
      <div className="container relative z-10 mx-auto max-w-screen-3xl bg-[#000307] pt-10">
        <div className="text-center md:text-left">
          <div className="flex justify-center text-center">
            <div className="mb-10 flex flex-col items-center justify-center px-6 md:mb-0 md:w-[60%] md:max-w-[860px]">
              <h2 className="mb-4 text-3xl font-bold leading-normal md:mb-2.5 md:text-[40px] md:leading-tight">
                The Platform for Intelligent Value-Optimizing Transformation
                Layers
              </h2>
              <p className="mb-8 text-lg leading-[150%] md:max-w-[720px]">
                PivoTL is a composable stack of autonomous AI layers—each
                designed to transform a specific business function, decision, or
                workflow with goal-aligned intelligence.
              </p>
            </div>
          </div>

          <hr className="border-darkGray" />

          <div className="flex w-full flex-col items-center md:flex-row">
            <div className="flex w-full justify-center border-r border-darkGray md:w-1/2">
              <AutoPlayVideo videoSrc={earthGlobal} />
            </div>
            <div className="flex w-full items-center justify-center px-6 pb-10 text-lg leading-9 md:mt-8 md:w-1/2">
              <p className="md:max-w-[580px]">
                Just as the brain organizes perception, emotion, and action
                across interdependent layers of neurons, PiVoTL orchestrates
                transformation through coordinated layers of agentic
                intelligence. Each layer is built to sense, reason, and act with
                purpose— unlocking adaptive, outcome-optimized change across
                every level of your organization.
              </p>
            </div>
          </div>

          <hr className="border-darkGray" />
        </div>
      </div>
    </section>
  );
};
