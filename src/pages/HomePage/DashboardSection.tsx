import { clouds, dashboardImage, vectorGrid } from '../../assets/images';
import { EvolutionSection } from './EvolutionSection';

export const DashboardSection = () => {
  return (
    <section className="relative -mt-12 overflow-hidden pb-10 pt-20 font-inter">
      <div className="container relative z-10 mx-auto max-w-screen-3xl md:py-16">
        {/* Background Cloud */}
        <div
          className="absolute inset-0 z-0 mt-16 h-[100%] md:-mt-8"
          style={{
            backgroundImage: `url(${clouds})`,
            backgroundSize: 'cover',
            backgroundPosition: 'center',
            backgroundRepeat: 'no-repeat',
          }}
        />
        {/* Background Grid */}
        <div
          className="absolute inset-0 z-0 -mt-[800px] scale-150 opacity-30"
          style={{
            backgroundImage: `url(${vectorGrid})`,
            backgroundSize: 'auto',
            backgroundPosition: 'center',
            backgroundRepeat: 'repeat-x',
            width: `${3 * 400}px`, // Width=400px, repeats 3x
            left: '50%',
            transform: 'translateX(-50%) scale(1.5)',
          }}
        />

        <div className="mx-auto flex max-w-screen-2xl flex-col md:flex-row">
          {/* Left Column */}
          <div className="relative py-6 md:w-1/2">
            <div className="">
              <div className="mb-8 px-4 text-center md:w-4/5 md:px-0 md:text-left">
                <h2 className="text-3xl font-semibold leading-snug text-blue-midnight md:text-[40px]">
                  Purpose-Built Dashboards For Every AI Agents Suite
                </h2>
                <p className="mt-4 text-lg text-blackOne">
                  Sales Operations AI Agents Accelerate pipelines with smart
                  outreach, dynamic scoring, and seamless follow-up.
                </p>
              </div>

              <div className="text-blackOne">
                <div className="flex flex-col items-center md:flex-row">
                  <div className="flex h-16 w-72 items-center justify-center border border-blueOne bg-[#FFFFFF99] px-4 text-center text-sm md:w-1/2 md:bg-transparent">
                    Real-time status of deployed agents
                  </div>
                  <div className="flex h-16 w-72 items-center justify-center border border-blueOne bg-[#FFFFFF99] px-4 text-center text-sm md:w-1/2 md:bg-transparent">
                    KPIs tracked per suite (e.g., resolution rate, lead
                    conversion, risk flags)
                  </div>
                </div>

                <div className="flex flex-col items-center justify-center md:flex-row">
                  <div className="flex h-16 w-72 items-center justify-center border-x border-blueOne bg-[#FFFFFF99] px-4 text-center text-sm md:w-1/2 md:bg-transparent">
                    Custom alerts, summaries, and exportable reports
                  </div>
                </div>

                <div className="flex flex-col items-center md:flex-row">
                  <div className="flex h-16 w-72 items-center justify-center border border-blueOne bg-[#FFFFFF99] px-4 text-center text-sm md:w-1/2 md:bg-transparent">
                    Agent usage breakdown with billing insights
                  </div>
                  <div className="flex h-16 w-72 items-center justify-center border border-blueOne bg-[#FFFFFF99] px-4 text-center text-sm md:w-1/2 md:bg-transparent">
                    Manager- and exec-level views by role or permission
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Right Column - Image */}
          <div className="z-10 px-2 md:w-1/2 md:px-0">
            <img
              src={dashboardImage}
              alt="Dashboard Image"
              className="h-full w-full rounded-sm border-2 border-grayTen object-contain md:border-4"
            />
          </div>
        </div>

        {/* Evolution Section */}
        <EvolutionSection />
      </div>
    </section>
  );
};
