import React, { useCallback, useEffect, useRef, useState } from "react";
import {
  eclipse,
  halfEclipse,
  pricingHeader,
  regis,
} from "../../assets/images";
import { ChatMessage, ChatState } from "@/types/agents";
import { ChatInput } from "@/components/chat/ChatInput";
import { useScyraChatApi } from "@/services/scyraChatService";
import { useTenant } from "@/context/TenantContext";
import {
  capitalizeAgentName,
  generateSecureSessionId,
} from "@/utils/agentUtils";
import { TypingIndicator } from "@/components/chat/TypingIndicator";
import MessageComponent from "@/components/chat/MessageComponent";
import { ArrowDown } from "lucide-react";
import Select, { components } from "react-select";
import countries from "world-countries";
import { submitDemoRequest } from "@/services/upivotalAgenticService";

interface FormData {
  firstName: string;
  lastName: string;
  email: string;
  countryCode: string;
  phoneNumber: string;
  neededService: string;
  enquiryDetails: string;
}

interface FormErrors {
  firstName?: string;
  lastName?: string;
  email?: string;
  neededService?: string;
}

interface CountryOption {
  value: string;
  label: string;
  flag: string;
  code: string;
}

interface ServiceOption {
  value: string;
  label: string;
}

// Create country options from world-countries data
const createCountryOptions = (): CountryOption[] => {
  return countries
    .map((country) => ({
      value: country.idd.root + (country.idd.suffixes?.[0] || ""),
      label: `${country.flag} ${country.name.common} ${country.idd.root}${country.idd.suffixes?.[0] || ""}`,
      flag: country.flag,
      code: country.idd.root + (country.idd.suffixes?.[0] || ""),
    }))
    .filter((country) => country.value && country.value !== "+")
    .sort((a, b) => a.label.localeCompare(b.label));
};

const COUNTRY_OPTIONS = createCountryOptions();

const SERVICE_OPTIONS: ServiceOption[] = [
  {
    value: "SetIQ - Collection Services AI Agents Suite",
    label: "SetIQ - Collection Services AI Agents Suite",
  },
  {
    value: "UnderwriteIQ - Strategy & Innovation AI Agents Suite",
    label: "UnderwriteIQ - Strategy & Innovation AI Agents Suite",
  },
  {
    value: "SalesWing - Sales Operations AI Agents Suite",
    label: "SalesWing - Sales Operations AI Agents Suite",
  },
  {
    value: "AdminWing - Administrative Services AI Agents Suite",
    label: "AdminWing - Administrative Services AI Agents Suite",
  },
  {
    value: "DataWing - Data Science and Quant AI Agents Suite",
    label: "DataWing - Data Science and Quant AI Agents Suite",
  },
  {
    value: "OperatorIQ - Project & Program Oversight AI Agents Suite",
    label: "OperatorIQ - Project & Program Oversight AI Agents Suite",
  },
  {
    value: "Other - Not yet listed",
    label: "Other - Not yet listed",
  },
];

const PricingPage: React.FC = () => {
  const { activeAgent, setActiveAgent } = useTenant();
  const chatWithAgent = useScyraChatApi();

  // Ensure the overall page is scrolled to top on initial navigation to this page.
  useEffect(() => {
    // Use a setTimeout to allow the route transition to complete before forcing scroll.
    const id = window.setTimeout(() => {
      try {
        window.scrollTo({ top: 0, left: 0, behavior: "auto" });
      } catch (e) {
        window.scrollTo(0, 0);
      }
    }, 0);

    return () => window.clearTimeout(id);
  }, []);

  useEffect(() => {
    setActiveAgent("regis");
  }, [setActiveAgent]);

  const [formData, setFormData] = useState<FormData>({
    firstName: "",
    lastName: "",
    email: "",
    countryCode: "",
    phoneNumber: "",
    neededService: "",
    enquiryDetails: "",
  });

  const [errors, setErrors] = useState<FormErrors>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [externalMessage, setExternalMessage] = useState<string>("");

  // Add local message
  const localMessage: ChatMessage = {
    id: `${activeAgent || "regis"}-${Date.now()}`,
    sender: activeAgent || "regis",
    content: externalMessage,
    timestamp: new Date(),
    senderName: capitalizeAgentName(activeAgent || "Regis"),
  };

  // refs for the select components
  const countrySelectRef = useRef<any>(null);
  const serviceSelectRef = useRef<any>(null);

  // Custom styles for react-select
  const selectStyles = {
    control: (provided: any, state: any) => ({
      ...provided,
      height: "40px",
      border: state.isFocused ? "2px solid #FF5C02" : "1px solid #d1d5db",
      borderRadius: "6px",
      boxShadow: "none",
      "&:hover": {
        border: state.isFocused ? "2px solid #FF5C02" : "1px solid #d1d5db",
      },
    }),
    option: (provided: any, state: any) => ({
      ...provided,
      backgroundColor: state.isSelected
        ? "#FF5C02"
        : state.isFocused
          ? "#FFF5F0"
          : "white",
      color: state.isSelected ? "white" : "#374151",
      "&:hover": {
        backgroundColor: state.isSelected ? "#FF5C02" : "#FFF5F0",
      },
    }),
    menu: (provided: any) => ({
      ...provided,
      border: "1px solid #d1d5db",
      borderRadius: "6px",
      boxShadow: "0 4px 6px -1px rgba(0, 0, 0, 0.1)",
      maxHeight: "200px",
      zIndex: 9999,
    }),
    menuList: (provided: any) => ({
      ...provided,
      maxHeight: "200px",
      overflowY: "auto" as const,
    }),
  };

  const validateEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    if (!formData.firstName.trim()) {
      newErrors.firstName = "First name is required";
    }

    if (!formData.lastName.trim()) {
      newErrors.lastName = "Last name is required";
    }

    if (!formData.email.trim()) {
      newErrors.email = "Work email is required";
    } else if (!validateEmail(formData.email)) {
      newErrors.email = "Please enter a valid email address";
    }

    if (!formData.neededService) {
      newErrors.neededService = "Please select a service";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement
    >
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));

    // Clear error when user starts typing
    if (errors[name as keyof FormErrors]) {
      setErrors((prev) => ({
        ...prev,
        [name]: undefined,
      }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      const payload = {
        firstName: formData.firstName,
        lastName: formData.lastName,
        email: formData.email,
        phoneNumber: `${formData.countryCode}${formData.phoneNumber}`,
        neededService: formData.neededService,
        enquiryDetails: formData.enquiryDetails,
      };

      const response = await submitDemoRequest(payload);

      if (response.status === true) {
        // Set success message for chat interface
        setExternalMessage(
          "Thank you for your demo request! Our team will get back to you promptly."
        );

        setChatState((prev) => ({
          ...prev,
          messages: [...prev.messages, localMessage],
          isLoading: false,
        }));

        // Reset form
        setFormData({
          firstName: "",
          lastName: "",
          email: "",
          countryCode: "",
          phoneNumber: "",
          neededService: "",
          enquiryDetails: "",
        });
        if (countrySelectRef.current) {
          countrySelectRef.current.clearValue();
        }
        if (serviceSelectRef.current) {
          serviceSelectRef.current.clearValue();
        }
      } else {
        throw new Error(response.message || "Submission failed");
      }
    } catch (error) {
      console.error("Form submission error:", error);
      setExternalMessage(
        "Sorry, there was an error submitting your request. Please try again or contact our support team."
      );
      setChatState((prev) => ({
        ...prev,
        messages: [...prev.messages, localMessage],
        isLoading: true,
      }));
    } finally {
      setIsSubmitting(false);
    }
  };

  // Chat state management
  const [chatState, setChatState] = useState<ChatState>({
    messages: [],
    isLoading: false,
    sessionId: generateSecureSessionId(),
  });

  // Chat scroll functionality
  const messagesContainerRef = useRef<HTMLDivElement>(null);
  const [showScrollToBottom, setShowScrollToBottom] = useState(false);
  const [isUserAtBottom, setIsUserAtBottom] = useState(true);

  const sendMessage = useCallback(
    async (messageContent: string) => {
      if (!messageContent.trim() || chatState.isLoading) return;

      // Add user message
      const userMessage: ChatMessage = {
        id: `user-${Date.now()}`,
        sender: "user",
        content: messageContent.trim(),
        timestamp: new Date(),
        senderName: "You",
      };

      setChatState((prev) => ({
        ...prev,
        messages: [...prev.messages, userMessage],
        isLoading: true,
      }));

      try {
        // Call agent API
        const response = await chatWithAgent({
          userMessage: messageContent.trim(),
          sessionId: chatState.sessionId,
        });

        // Add agent response
        const agentMessage: ChatMessage = {
          id: `${activeAgent || "regis"}-${Date.now()}`,
          sender: activeAgent || "regis",
          content: response,
          timestamp: new Date(),
          senderName: capitalizeAgentName(activeAgent || "Regis"),
        };

        setChatState((prev) => ({
          ...prev,
          messages: [...prev.messages, agentMessage],
          isLoading: false,
        }));
      } catch (error) {
        console.error(
          `Error sending message to ${activeAgent || "regis"}:`,
          error
        );

        // Add error message
        const errorMessage: ChatMessage = {
          id: `error-${Date.now()}`,
          sender: activeAgent || "regis",
          content: "Sorry, I encountered an error. Please try again.",
          timestamp: new Date(),
          senderName: capitalizeAgentName(activeAgent || "Regis"),
        };

        setChatState((prev) => ({
          ...prev,
          messages: [...prev.messages, errorMessage],
          isLoading: false,
        }));
      }
    },
    [chatWithAgent, chatState.sessionId, chatState.isLoading, activeAgent]
  );

  // Scroll functionality
  const scrollToBottom = useCallback(() => {
    if (messagesContainerRef.current) {
      messagesContainerRef.current.scrollTo({
        top: messagesContainerRef.current.scrollHeight,
        behavior: "smooth",
      });
    }
  }, []);

  // Show/hide down-arrow when user scrolls up and track user position
  useEffect(() => {
    const container = messagesContainerRef.current;
    if (!container) return;

    const handleScroll = () => {
      const atBottom =
        container.scrollHeight - container.scrollTop - container.clientHeight <
        40;
      setShowScrollToBottom(!atBottom);
      setIsUserAtBottom(atBottom);
    };

    container.addEventListener("scroll", handleScroll);
    handleScroll();

    return () => container.removeEventListener("scroll", handleScroll);
  }, [chatState.messages.length]);

  // Auto-scroll to bottom only when user is already at bottom and new messages arrive
  useEffect(() => {
    if (isUserAtBottom || chatState.messages.length === 0) {
      scrollToBottom();
    }
  }, [chatState.messages, isUserAtBottom, scrollToBottom]);

  // Handle loading state changes - only scroll if user is at bottom
  useEffect(() => {
    if (!chatState.isLoading && isUserAtBottom) {
      setTimeout(() => {
        scrollToBottom();
      }, 100);
    }
  }, [chatState.isLoading, isUserAtBottom, scrollToBottom]);

  return (
    <div className="min-h-full font-inter">
      <div className="mx-auto max-w-screen-2xl p-4 lg:py-0 lg:px-8">
        <div className="grid grid-cols-1 gap-8 lg:grid-cols-3">
          {/* --- Main Content --- */}
          <div className="lg:col-span-2">
            {/* LHS - Header */}
            <div className="relative h-[198px] overflow-hidden rounded-lg border bg-gray-200 bg-cover bg-center">
              {/* Background Image */}
              <div
                className="absolute inset-0 bg-cover bg-center"
                style={{
                  backgroundImage: `url(${pricingHeader})`,
                }}
              />
              {/* Dark Overlay */}
              <div className="absolute inset-0 bg-black/20" />
              {/* Content */}
              <div className="relative z-10 flex h-full flex-col justify-center py-6 px-8 md:px-12">
                <div className="w-fit flex items-center justify-center rounded bg-white px-4 py-2.5 text-xl md:text-[32px] font-bold backdrop-blur-sm">
                  Pricing
                </div>

                <div className="font-inter text-sm md:text-lg text-white mt-6">
                  One-time setup, monthly retainer, performance-based
                  scalability included.
                  <br />
                  Chat with Regis to learn more.
                </div>
              </div>
            </div>

            {/* Enhanced Chat Interface */}
            <div className="bg-white font-inter w-full md:w-[90%]">
              {chatState.messages.length === 0 ? (
                // Initial state - only show input field
                <div className="mt-6 md:mt-20">
                  <div className="mb-6 flex gap-3">
                    {/* Selected agent's image */}
                    <div className="h-12 w-12 flex-shrink-0">
                      <div className="rounded-full bg-grayTwentySix">
                        <img
                          src={regis}
                          alt="Regis"
                          className="h-full w-full rounded-full object-cover"
                        />
                      </div>
                    </div>

                    <div className="flex-1">
                      <div className="mb-1 flex items-center gap-2">
                        {/* Selected agent's name */}
                        <span className="font-semibold text-darkGray">
                          {"Regis"}
                        </span>
                      </div>
                      <div className="rounded-lg bg-gray-5 p-3 font-medium text-grayTwentyFour">
                        {/* Selected agent's description */}
                        <div className="w-fit">
                          <span>
                            Hi, I’m Regis. Ask me anything about pricing.
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className="flex h-20 items-center mt-6">
                    <ChatInput
                      onSendMessage={sendMessage}
                      placeholder="I'm here — whenever you're ready."
                      disabled={chatState.isLoading}
                    />
                  </div>
                </div>
              ) : (
                // Full chat interface with messages
                <div className="relative flex h-[calc(100vh-400px)] md:h-[calc(100vh-320px)] flex-col mt-4">
                  {/* Messages Container */}
                  <div
                    ref={messagesContainerRef}
                    className="flex-1 overflow-y-auto py-4"
                    style={{ minHeight: 0 }}
                  >
                    {chatState.messages.map((message) => (
                      <MessageComponent
                        key={message.id}
                        message={message}
                        agentAvatar={regis}
                      />
                    ))}

                    {/* Typing Indicator */}
                    {chatState.isLoading && (
                      <TypingIndicator
                        agentImageSrc={regis}
                        agentName={capitalizeAgentName(activeAgent || "Regis")}
                        message={`${capitalizeAgentName(
                          activeAgent || "Regis"
                        )} is connecting`}
                      />
                    )}
                  </div>

                  {/* Floating Down Arrow */}
                  {showScrollToBottom && (
                    <button
                      className="absolute bottom-24 right-6 z-20 flex h-10 w-10 items-center justify-center rounded-full border border-gray-200 bg-white shadow-lg transition hover:bg-gray-50"
                      onClick={scrollToBottom}
                      aria-label="Scroll to latest message"
                    >
                      <ArrowDown className="h-6 w-6 text-primary" />
                    </button>
                  )}

                  {/* Chat Input */}
                  <div className="flex-shrink-0 px-4 py-4">
                    <ChatInput
                      onSendMessage={sendMessage}
                      placeholder="I'm here — whenever you're ready."
                      disabled={chatState.isLoading}
                    />
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* --- Sidebar --- */}
          <div className="lg:col-span-1">
            {/* Background Objects */}
            <div
              className="pointer-events-none hidden md:block absolute inset-0 z-0 -mt-48 bg-[right] bg-no-repeat"
              style={{
                backgroundImage: `url(${halfEclipse})`,
                backgroundSize: "auto",
              }}
            />
            <div
              className="pointer-events-none hidden md:block absolute inset-0 z-0 bg-[right_bottom] bg-no-repeat"
              style={{
                backgroundImage: `url(${eclipse})`,
                backgroundSize: "auto",
              }}
            />
            <div className="relative flex h-full flex-col justify-center overflow-hidden rounded bg-gradient-to-br from-orange-50/50 to-orange-100 p-2 md:p-6 font-inter">
              <div className="bg-white rounded-2xl px-4 py-6 md:p-6">
                <h2 className="text-2xl font-semibold text-gray-900 mb-2">
                  Request Demo and Pricing
                </h2>
                <p className="text-gray-600 mb-6 text-xs">
                  Fill out the form below, and our team will get back to you
                  promptly. Let's connect and create solutions together.
                </p>

                <form onSubmit={handleSubmit} className="space-y-4">
                  {/* First Name */}
                  <div>
                    <label
                      htmlFor="firstName"
                      className="block text-xs font-medium text-[#19213D] mb-1"
                    >
                      First Name <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="text"
                      id="firstName"
                      name="firstName"
                      value={formData.firstName}
                      onChange={handleInputChange}
                      className={`w-full px-3 py-2 text-sm border rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary ${
                        errors.firstName ? "border-red-500" : "border-gray-300"
                      }`}
                      placeholder="Enter your first name"
                    />
                    {errors.firstName && (
                      <p className="mt-1 text-sm text-red-600">
                        {errors.firstName}
                      </p>
                    )}
                  </div>

                  {/* Last Name */}
                  <div>
                    <label
                      htmlFor="lastName"
                      className="block text-xs font-medium text-[#19213D] mb-1"
                    >
                      Last Name <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="text"
                      id="lastName"
                      name="lastName"
                      value={formData.lastName}
                      onChange={handleInputChange}
                      className={`w-full px-3 py-2 text-sm border rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary ${
                        errors.lastName ? "border-red-500" : "border-gray-300"
                      }`}
                      placeholder="Enter your last name"
                    />
                    {errors.lastName && (
                      <p className="mt-1 text-sm text-red-600">
                        {errors.lastName}
                      </p>
                    )}
                  </div>

                  {/* Work Email */}
                  <div>
                    <label
                      htmlFor="email"
                      className="block text-xs font-medium text-[#19213D] mb-1"
                    >
                      Work Email <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="email"
                      id="email"
                      name="email"
                      value={formData.email}
                      onChange={handleInputChange}
                      className={`w-full px-3 py-2 text-sm border rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary ${
                        errors.email ? "border-red-500" : "border-gray-300"
                      }`}
                      placeholder="Enter your work email"
                    />
                    {errors.email && (
                      <p className="mt-1 text-sm text-red-600">
                        {errors.email}
                      </p>
                    )}
                  </div>

                  {/* Phone Number */}
                  <div>
                    <label className="block text-xs font-medium text-[#19213D] mb-1">
                      Phone Number
                    </label>
                    <div className="flex flex-col md:flex-row gap-2">
                      <div className="md:w-48">
                        <Select
                          ref={countrySelectRef}
                          className="text-sm"
                          options={COUNTRY_OPTIONS}
                          value={COUNTRY_OPTIONS.find(
                            (option) => option.value === formData.countryCode
                          )}
                          onChange={(selectedOption) => {
                            setFormData((prev) => ({
                              ...prev,
                              countryCode: selectedOption?.value || "",
                            }));
                          }}
                          styles={selectStyles}
                          placeholder="Select code"
                          isSearchable={true}
                          components={{
                            IndicatorSeparator: () => null,
                          }}
                        />
                      </div>
                      <input
                        type="tel"
                        name="phoneNumber"
                        value={formData.phoneNumber}
                        onChange={handleInputChange}
                        className="flex-1 px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary"
                        placeholder="Enter phone number"
                      />
                    </div>
                  </div>

                  {/* Services Needed */}
                  <div>
                    <label
                      htmlFor="neededService"
                      className="block text-xs font-medium text-[#19213D] mb-1"
                    >
                      What service do you need?{" "}
                      <span className="text-red-500">*</span>
                    </label>
                    <Select
                      ref={serviceSelectRef}
                      className="text-sm"
                      options={SERVICE_OPTIONS}
                      value={SERVICE_OPTIONS.find(
                        (option) => option.value === formData.neededService
                      )}
                      onChange={(selectedOption) => {
                        setFormData((prev) => ({
                          ...prev,
                          neededService: selectedOption?.value || "",
                        }));
                        // Clear error when user selects a service
                        if (errors.neededService) {
                          setErrors((prev) => ({
                            ...prev,
                            neededService: undefined,
                          }));
                        }
                      }}
                      styles={{
                        ...selectStyles,
                        control: (provided: any, state: any) => ({
                          ...provided,
                          height: "40px",
                          border: errors.neededService
                            ? "1px solid #ef4444"
                            : state.isFocused
                              ? "2px solid #FF5C02"
                              : "1px solid #d1d5db",
                          borderRadius: "6px",
                          boxShadow: "none",
                          "&:hover": {
                            border: errors.neededService
                              ? "1px solid #ef4444"
                              : state.isFocused
                                ? "2px solid #FF5C02"
                                : "1px solid #d1d5db",
                          },
                        }),
                      }}
                      placeholder="Select a service"
                      isSearchable={false}
                      components={{
                        IndicatorSeparator: () => null,
                      }}
                    />
                    {errors.neededService && (
                      <p className="mt-1 text-sm text-red-600">
                        {errors.neededService}
                      </p>
                    )}
                  </div>

                  {/* How can we help you */}
                  <div>
                    <label
                      htmlFor="enquiryDetails"
                      className="block text-xs font-medium text-[#19213D] mb-1"
                    >
                      How can we help you?
                    </label>
                    <textarea
                      id="enquiryDetails"
                      name="enquiryDetails"
                      value={formData.enquiryDetails}
                      onChange={handleInputChange}
                      rows={4}
                      className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary"
                      placeholder="Tell us about your needs..."
                    />
                  </div>

                  {/* Submit Button */}
                  <button
                    type="submit"
                    disabled={isSubmitting}
                    className={`w-full py-2 px-4 rounded-lg font-medium transition-colors ${
                      isSubmitting
                        ? "bg-gray-400 text-gray-600 cursor-not-allowed"
                        : "border border-primary bg-peach-5 text-blackOne hover:bg-primary/90 hover:text-white"
                    }`}
                  >
                    {isSubmitting ? (
                      <div className="flex items-center justify-center">
                        <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                        Submitting...
                      </div>
                    ) : (
                      "Submit"
                    )}
                  </button>
                </form>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PricingPage;
