import React from 'react';
import { AgentSuite, IndividualAgent } from '../../../../data/constants';
import { ActivationLevel } from '../index';
import clsx from 'clsx';
import { ArrowLeftIcon, ArrowRight } from 'lucide-react';
import { agentSelector } from '@/assets/images';

interface AgentLevelSelectorProps {
  selectedLevel: ActivationLevel | null;
  onLevelSelect: (level: ActivationLevel) => void;
  onNext: () => void;
  currentSuite?: AgentSuite | null;
  currentAgent?: IndividualAgent | null;
}

const AgentLevelSelector: React.FC<AgentLevelSelectorProps> = ({
  selectedLevel,
  onLevelSelect,
  onNext,
  currentSuite,
}) => {
  const canProceed = selectedLevel !== null;

  const levelOptions = [
    {
      id: 'suite' as const,
      title: 'Suite Level',
      description: 'Shared by all agents in this suite',
      image: currentSuite?.image,
      showImage: true,
    },
    {
      id: 'agent' as const,
      title: 'Agent Level',
      description: 'Only used by the selected agent',
      image: agentSelector,
      showImage: true,
    },
  ];

  return (
    <div className="mx-auto flex w-full max-w-[640px] flex-col gap-6 bg-[#363D8808] p-6">
      {/* Header */}
      <div className="p-2 bg-white">
        <h2 className="text-lg font-semibold text-center capitalize text-blackOne">
          Knowledge base document upload selector
        </h2>
      </div>

      <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
        {levelOptions.map(level => (
          <button
            key={level.id}
            onClick={() => onLevelSelect(level.id)}
            className={clsx(
              'flex h-[124px] w-full max-w-[285px] overflow-hidden rounded-xl border-2 text-left shadow-[0px_1px_10px_0px_#98A2B30F] transition-all',
              selectedLevel === level.id
                ? 'border-primary bg-orange-50'
                : 'border-[#121212] bg-white',
            )}
          >
            {level.showImage ? (
              <div className="flex w-[111px] items-center justify-center bg-[#F9FAFB]">
                {level.image && (
                  <img
                    src={level.image}
                    alt={level.title}
                    className="object-cover w-full h-full"
                  />
                )}
              </div>
            ) : (
              <div className="w-[111px] overflow-hidden rounded-l-lg bg-[#F9FAFB]"></div>
            )}
            <div className="flex flex-col gap-4 p-4">
              <h3 className="text-base font-semibold text-blackOne">
                {level.title}
              </h3>
              <p className="text-sm text-blackOne">{level.description}</p>
            </div>
          </button>
        ))}
      </div>

      <div className="h-[1px] w-full bg-primary" />

      {/* Navigation */}
      <div className="flex justify-between">
        <button
          onClick={() => window.history.back()}
          className="flex gap-2 items-center text-sm text-blackOne"
        >
          <ArrowLeftIcon className="w-4 h-4" />
          Back
        </button>

        <button
          onClick={onNext}
          disabled={!canProceed}
          className={clsx(
            'flex gap-2 items-center px-6 py-2 font-medium rounded-lg border transition-colors bg-light-orangeTwo border-primary text-blackOne disabled:opacity-50',
          )}
        >
          Proceed
          <ArrowRight className="w-4 h-4" />
        </button>
      </div>
    </div>
  );
};

export default AgentLevelSelector;
