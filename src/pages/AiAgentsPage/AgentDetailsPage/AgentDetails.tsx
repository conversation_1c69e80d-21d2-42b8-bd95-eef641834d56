import { ArrowDown, ChevronRight } from 'lucide-react';
import { useCallback, useEffect, useRef, useState } from 'react';
import { Link, useLocation, useParams } from 'react-router-dom';

import { setIq, vesa } from '@/assets/images';
import { ChatInput } from '@/components/chat/ChatInput';
import MessageComponent from '@/components/chat/MessageComponent';
import { TypingIndicator } from '@/components/chat/TypingIndicator';
import { MainLoaderSkeleton } from '@/components/hocs/suspense/withSuspense';
import AgentSuiteSkeletonLoader from '@/components/ui/AgentSuiteSkeleton';
import { ROUTES } from '@/constants/routes';
import { useTenant } from '@/context/TenantContext';
import { agentSuites as mockAgents, featureIcons } from '@/data/constants';
import { useGetAIAgentsData } from '@/hooks/useAgents';
// import { useOnClickOutside } from '@/hooks/useOnClickOutside';
import { useScyraChatApi } from '@/services/scyraChatService';
import { generateSecureSessionId } from '@/services/upivotalAgenticService';
import { AIAgent, ChatMessage, ChatState } from '@/types/agents';
import { capitalizeAgentName } from '@/utils/agentUtils';

import { AgentCard } from '..';

interface PageAgentProps {
  isSuite: boolean;
  id: string;
  name: string;
  description: string;
  roleDescription: string;
  roles: string[];
  avatar: string;
}

const AgentDetails = () => {
  const location = useLocation();
  const chatWithAgent = useScyraChatApi();

  // Chat state management
  const [chatState, setChatState] = useState<ChatState>({
    messages: [],
    isLoading: false,
    sessionId: generateSecureSessionId(),
  });

  // Chat scroll functionality
  const messagesContainerRef = useRef<HTMLDivElement>(null);
  const [showScrollToBottom, setShowScrollToBottom] = useState(false);
  const [isUserAtBottom, setIsUserAtBottom] = useState(true);

  const { agentSuites, isLoadingAgents, isLoadingSuites } =
    useGetAIAgentsData();
  const { activeAgent: tenantActiveAgent, setActiveAgent } = useTenant();

  const { agentId: agentSuiteId } = useParams<{ agentId: string }>();
  const agentSuite = agentSuiteId
    ? agentSuites.find(agentSuite => agentSuite.agentSuiteKey === agentSuiteId)
    : null;
  const agents = agentSuite?.availableAgents || [];

  const [pageAgent, setPageAgent] = useState<PageAgentProps>({
    isSuite: true,
    id: agentSuiteId || '',
    name: agentSuite?.agentSuiteName || '',
    description: agentSuite?.description || '',
    roleDescription: agentSuite?.roleDescription || '',
    roles: agentSuite?.roles || [],
    avatar: agentSuite?.avatar || '',
  });

  useEffect(() => {
    if (!tenantActiveAgent || tenantActiveAgent === 'regis') {
      setPageAgent({
        isSuite: true,
        id: agentSuiteId || '',
        name: agentSuite?.agentSuiteName || '',
        description: agentSuite?.description || '',
        roleDescription: agentSuite?.roleDescription || '',
        roles: agentSuite?.roles || [],
        avatar: agentSuite?.avatar || '',
      });
    } else {
      setPageAgent({
        isSuite: false,
        id: tenantActiveAgent || '',
        name: activeAgent?.agentName || '',
        description: activeAgent?.description || '',
        roleDescription: activeAgent?.roleDescription || '',
        roles: activeAgent?.roles || [],
        avatar: activeAgent?.avatar || '',
      });
    }
  }, [tenantActiveAgent, agentSuite]);

  // Handle location state from HeroSection navigation
  const locationState = location.state as {
    selectedAgent?: AIAgent;
    userMessage?: string;
  } | null;

  // Track if we came from HeroSection with location state
  const [isFromHeroSection, setIsFromHeroSection] = useState(
    !!locationState?.selectedAgent
  );

  const activeAgent =
    (isFromHeroSection && locationState?.selectedAgent) ||
    agents.filter(agent => agent.agentKey === tenantActiveAgent)[0];

  // Set active agent in context when coming from HeroSection
  useEffect(() => {
    if (locationState?.selectedAgent && isFromHeroSection) {
      setActiveAgent(locationState.selectedAgent.agentKey);
    }
  }, [locationState?.selectedAgent, setActiveAgent, isFromHeroSection]);

  // Track auto-send to prevent duplicates
  const hasAutoSentMessage = useRef(false);

  // Track current agent to detect switches
  const currentAgentRef = useRef(activeAgent?.agentKey || null);
  const [isAgentSwitching, setIsAgentSwitching] = useState(false);

  const [showAgentFeatures, setShowAgentFeatures] = useState(true);
  const agentFeaturesRef = useRef<HTMLDivElement>(null);
  const toggleAgentFeatures = () => setShowAgentFeatures(previous => !previous);

  // useOnClickOutside(agentFeaturesRef, () => setShowAgentFeatures(false));

  // Scroll functionality
  const scrollToBottom = useCallback(() => {
    if (messagesContainerRef.current) {
      messagesContainerRef.current.scrollTo({
        top: messagesContainerRef.current.scrollHeight,
        behavior: 'smooth',
      });
    }
  }, []);

  // Show/hide down-arrow when user scrolls up and track user position
  useEffect(() => {
    const container = messagesContainerRef.current;
    if (!container) return;

    const handleScroll = () => {
      const atBottom =
        container.scrollHeight - container.scrollTop - container.clientHeight <
        40;
      setShowScrollToBottom(!atBottom);
      setIsUserAtBottom(atBottom);
    };

    container.addEventListener('scroll', handleScroll);
    handleScroll();

    return () => container.removeEventListener('scroll', handleScroll);
  }, [chatState.messages.length]);

  // Auto-scroll to bottom only when user is already at bottom and new messages arrive
  useEffect(() => {
    if (isUserAtBottom || chatState.messages.length === 0) {
      scrollToBottom();
    }
  }, [chatState.messages, isUserAtBottom, scrollToBottom]);

  // Handle loading state changes - only scroll if user is at bottom
  useEffect(() => {
    if (!chatState.isLoading && isUserAtBottom) {
      setTimeout(() => {
        scrollToBottom();
      }, 100);
    }
  }, [chatState.isLoading, isUserAtBottom, scrollToBottom]);

  const sendMessage = useCallback(
    async (messageContent: string) => {
      if (!messageContent.trim() || chatState.isLoading || !activeAgent) return;

      // Add user message
      const userMessage: ChatMessage = {
        id: `user-${Date.now()}`,
        sender: 'user',
        content: messageContent.trim(),
        timestamp: new Date(),
        senderName: 'You',
      };

      setChatState(prev => ({
        ...prev,
        messages: [...prev.messages, userMessage],
        isLoading: true,
      }));

      try {
        // Call agent API
        const response = await chatWithAgent({
          userMessage: messageContent.trim(),
          sessionId: chatState.sessionId,
        });

        // Add agent response
        const agentMessage: ChatMessage = {
          id: `${activeAgent.agentKey}-${Date.now()}`,
          sender: activeAgent.agentKey,
          content: response,
          timestamp: new Date(),
          senderName: capitalizeAgentName(activeAgent.agentName),
        };

        setChatState(prev => ({
          ...prev,
          messages: [...prev.messages, agentMessage],
          isLoading: false,
        }));

        window.scrollTo({ top: 10, behavior: 'smooth' });
      } catch (error) {
        console.error(
          `Error sending message to ${activeAgent.agentKey}:`,
          error
        );

        // Add error message
        const errorMessage: ChatMessage = {
          id: `error-${Date.now()}`,
          sender: activeAgent.agentKey,
          content: 'Sorry, I encountered an error. Please try again.',
          timestamp: new Date(),
          senderName: capitalizeAgentName(activeAgent.agentName),
        };

        setChatState(prev => ({
          ...prev,
          messages: [...prev.messages, errorMessage],
          isLoading: false,
        }));
      }
    },
    [chatWithAgent, chatState.sessionId, chatState.isLoading, activeAgent]
  );

  // Handle agent switching - clear chat state when agent changes
  useEffect(() => {
    const currentAgentKey = activeAgent?.agentKey || null;
    if (currentAgentRef.current !== currentAgentKey) {
      // Agent has switched, clear chat state and show switching indicator
      setIsAgentSwitching(true);
      setChatState({
        messages: [],
        isLoading: false,
        sessionId: generateSecureSessionId(),
      });

      // Reset auto-send flag for new agent
      hasAutoSentMessage.current = false;

      // Update current agent ref
      currentAgentRef.current = currentAgentKey;

      // Clear switching indicator after a brief moment
      setTimeout(() => {
        setIsAgentSwitching(false);
      }, 500);
    }
  }, [activeAgent?.agentKey]);

  // Auto-send message from HeroSection - use useCallback to prevent re-runs
  const autoSendMessage = useCallback(() => {
    if (
      locationState?.userMessage &&
      chatState.messages.length === 0 &&
      !hasAutoSentMessage.current &&
      !chatState.isLoading &&
      !isAgentSwitching &&
      isFromHeroSection
    ) {
      hasAutoSentMessage.current = true;
      sendMessage(locationState.userMessage);
    }
  }, [
    locationState?.userMessage,
    chatState.messages.length,
    chatState.isLoading,
    isAgentSwitching,
    isFromHeroSection,
    sendMessage,
  ]);

  // Auto-send message from HeroSection
  useEffect(() => {
    autoSendMessage();
  }, [autoSendMessage]);

  if (!agentSuite) {
    if (isLoadingSuites) {
      return <MainLoaderSkeleton />;
    } else {
      return (
        <div className="-mt-20 flex min-h-screen items-center justify-center bg-gray-50">
          <div className="text-center">
            <h1 className="mb-4 text-2xl font-bold text-blackOne">
              Agent Not Found
            </h1>
            <p className="mb-6 text-gray-600">
              The agent you're looking for doesn't exist.
            </p>
            <Link
              to={ROUTES.HOME}
              className="rounded-lg bg-primary px-6 py-3 font-medium text-white transition-colors hover:bg-orange-15"
            >
              Back to Home
            </Link>
          </div>
        </div>
      );
    }
  }

  return (
    <div className="mx-auto max-w-7xl px-4 lg:px-8">
      <div className="grid grid-cols-1 gap-8 lg:grid-cols-3">
        {/* --- Main Content --- */}
        <div className="lg:col-span-2">
          {/* LHS - Header */}
          <div className="relative h-[198px] overflow-hidden rounded-lg border bg-gray-200 bg-cover bg-center">
            {/* Background Image */}
            <div
              className="absolute inset-0 bg-cover bg-center"
              style={{
                backgroundImage: agentSuite.avatar
                  ? `url(${agentSuite.avatar})`
                  : `url(${setIq})`,
              }}
            />
            {/* Dark Overlay */}
            <div className="absolute inset-0 bg-black/20" />
            {/* Content */}
            <div className="relative z-10 flex h-full flex-col justify-center p-6">
              <div className="flex w-fit items-center justify-center rounded bg-white px-4 py-2 text-xl font-bold backdrop-blur-sm md:text-[32px]">
                <span className="mt-1.5">{agentSuite.agentSuiteName}</span>
              </div>

              <h2 className="mt-8 w-fit rounded text-base font-semibold text-white md:text-[20px]">
                {agentSuite.description}
              </h2>
              <div className="font-inter text-sm text-white md:text-lg">
                {agentSuite.roleDescription}
              </div>
            </div>
          </div>

          {/* Active agent */}
          {chatState.messages && chatState.messages.length === 0 && (
            <div className="relative mt-4 w-full rounded-[14px] border border-peachTwo font-inter">
              <div
                ref={agentFeaturesRef}
                className="flex cursor-pointer items-center gap-4 rounded-lg p-3 md:p-6"
                onClick={toggleAgentFeatures}
              >
                <div className="h-12 min-w-12 rounded-full bg-peachTwo">
                  <img
                    key={pageAgent.id}
                    src={pageAgent.avatar}
                    className="h-12 w-12 rounded-full object-cover"
                    alt={pageAgent.name}
                    onError={e => {
                      const fallbackAgent = mockAgents.find(
                        agent =>
                          agent.id.toLowerCase() === pageAgent.id.toLowerCase()
                      );
                      if (fallbackAgent) {
                        (e.target as HTMLImageElement).src =
                          fallbackAgent.image;
                      }
                    }}
                  />
                </div>
                <div className="flex w-full items-center justify-between text-sm md:text-base">
                  {pageAgent.isSuite ? (
                    <div>
                      <p className="text-lg font-bold text-darkGray">
                        {pageAgent.name} Features
                      </p>
                      <p className="text-lg text-blackOne">
                        Suite — Team Capabilities
                      </p>
                    </div>
                  ) : (
                    <div>
                      <p className="text-darkGray">
                        Hi, I'm{' '}
                        <span className="font-semibold">
                          {`${pageAgent.name} — ${pageAgent.description}`}
                        </span>{' '}
                      </p>
                      <p>{pageAgent.roleDescription}</p>
                    </div>
                  )}
                  <ChevronRight
                    className={`${
                      showAgentFeatures ? '-rotate-90' : 'rotate-0'
                    } text-primary`}
                  />
                </div>
              </div>

              {/* Marketplace agent features */}
              {showAgentFeatures && (
                <div className="mb-2 w-full border-t border-peachTwo p-3 md:p-6">
                  <div className="flex h-[20vh] flex-col gap-2.5 overflow-auto md:h-full md:gap-4">
                    {pageAgent.roles.map((feature, index) => (
                      <div key={index} className="flex items-center gap-4">
                        <img
                          src={featureIcons[index % 3]}
                          alt=""
                          className="w-4 md:w-5"
                        />
                        <p className="text-sm text-blackTwo">{feature}</p>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Enhanced Chat Interface */}
          <div className="bg-white font-inter">
            {chatState.messages.length === 0 ? (
              // Initial state - only show input field
              <div className="flex h-20 items-center gap-2">
                <div className="h-12 min-w-12 rounded-full bg-peachTwo">
                  <img
                    key={pageAgent.id}
                    src={
                      pageAgent.isSuite && agentSuite.availableAgents?.[0]
                        ? agentSuite.availableAgents[0].avatar
                        : pageAgent.avatar
                    }
                    className="h-12 w-12 rounded-full object-cover"
                    alt={
                      pageAgent.isSuite && agentSuite.availableAgents?.[0]
                        ? agentSuite.availableAgents[0].agentName
                        : pageAgent.name
                    }
                    onError={e => {
                      const fallbackAgent = mockAgents.find(
                        agent =>
                          agent.id.toLowerCase() ===
                          (pageAgent.isSuite && agentSuite.availableAgents?.[0]
                            ? agentSuite.availableAgents[0].agentKey.toLowerCase()
                            : pageAgent.id.toLowerCase())
                      );
                      if (fallbackAgent) {
                        (e.target as HTMLImageElement).src =
                          fallbackAgent.image;
                      }
                    }}
                  />
                </div>
                <ChatInput
                  onSendMessage={sendMessage}
                  agent={
                    activeAgent?.agentKey || agentSuite.availableAgents?.[0]
                      ? agentSuite.availableAgents[0].agentKey
                      : 'colton'
                  }
                  placeholder={`${
                    pageAgent.isSuite && agentSuite.availableAgents?.[0]
                      ? agentSuite.availableAgents[0].agentName
                      : pageAgent.name
                  } here — whenever you're ready.`}
                  disabled={chatState.isLoading || isAgentSwitching}
                />
              </div>
            ) : (
              // Full chat interface with messages
              <div className="relative flex h-[calc(100vh-400px)] flex-col md:h-[calc(100vh-320px)]">
                {/* Messages Container */}
                <div
                  ref={messagesContainerRef}
                  className="flex-1 overflow-y-auto py-4"
                  style={{ minHeight: 0 }}
                >
                  {chatState.messages.map(message => (
                    <MessageComponent
                      key={message.id}
                      message={message}
                      agentAvatar={pageAgent.avatar}
                    />
                  ))}

                  {/* Typing Indicator */}
                  {(chatState.isLoading || isAgentSwitching) && (
                    <TypingIndicator
                      agentImageSrc={pageAgent.avatar || vesa}
                      agentName={capitalizeAgentName(pageAgent.name)}
                      message={
                        isAgentSwitching
                          ? `${capitalizeAgentName(pageAgent.name)} is connecting`
                          : undefined
                      }
                    />
                  )}
                </div>

                {/* Floating Down Arrow */}
                {showScrollToBottom && (
                  <button
                    className="absolute bottom-24 right-6 z-20 flex h-10 w-10 items-center justify-center rounded-full border border-gray-200 bg-white shadow-lg transition hover:bg-gray-50"
                    onClick={scrollToBottom}
                    aria-label="Scroll to latest message"
                  >
                    <ArrowDown className="h-6 w-6 text-primary" />
                  </button>
                )}

                {/* Chat Input */}
                <div className="flex-shrink-0 px-4 py-4">
                  <ChatInput
                    onSendMessage={sendMessage}
                    placeholder="I'm here — whenever you're ready."
                    disabled={chatState.isLoading || isAgentSwitching}
                  />
                </div>
              </div>
            )}
          </div>
        </div>

        {/* --- Sidebar --- */}
        <div className="lg:col-span-1">
          {isLoadingAgents ? (
            <AgentSuiteSkeletonLoader count={4} />
          ) : (
            <div className="flex flex-col gap-4">
              {agents.map(agent => (
                <AgentCard
                  key={agent.agentKey}
                  className="w-full max-w-[334px]"
                  agent={agent}
                  showChatButton
                  isActiveAgent={tenantActiveAgent === agent.agentKey}
                  link="#"
                  onAgentSelect={() => {
                    // Clear HeroSection influence when manually selecting agent
                    setIsFromHeroSection(false);
                    // Clear location state by replacing history
                    window.history.replaceState(
                      null,
                      '',
                      window.location.pathname
                    );

                    setActiveAgent(agent.agentKey);
                  }}
                />
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default AgentDetails;
