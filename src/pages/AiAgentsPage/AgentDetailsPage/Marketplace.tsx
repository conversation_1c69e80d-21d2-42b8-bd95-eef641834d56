import { useEffect, useState } from "react";
import { motion } from "framer-motion";
import AppContainer from "@/components/common/AppContainer";
import EnhancedChatSidebar from "@/components/common/EnhancedChatSidebar";
import { useTenant } from "@/context/TenantContext";
import { agentSuites as mockAgents, agentCategories } from "@/data/constants";
import { useGetAIAgentsData } from "@/hooks/useAIAgents";
import { ROUTES } from "@/constants/routes";
import AgentSkeleton from "@/components/ui/AgentSkeleton";
import { useNavigate } from "react-router-dom";
import { regis } from "@/assets/images";
import { ArrowLeft } from "lucide-react";

const AgentDetailsPage = () => {
  const { activeAgent, setActiveAgent } = useTenant();
  const navigate = useNavigate();
  // Ensure the overall page is scrolled to top on initial navigation to this page.
  useEffect(() => {
    // Use a setTimeout to allow the route transition to complete before forcing scroll.
    const id = window.setTimeout(() => {
      try {
        window.scrollTo({ top: 0, left: 0, behavior: "auto" });
      } catch (e) {
        window.scrollTo(0, 0);
      }
    }, 0);

    return () => window.clearTimeout(id);
  }, []);

  useEffect(() => {
    setActiveAgent("regis");
  }, [setActiveAgent, activeAgent]);

  const [chatMessage, setChatMessage] = useState<string>("");
  const [selectedCategory, setSelectedCategory] = useState("all");

  const handleAgentsFilter = (categoryId: string) => {
    if (categoryId === selectedCategory) {
      setSelectedCategory("all");
    } else {
      setSelectedCategory(categoryId);
    }
  };

  const { agents, agentSuites, isLoadingAgents, isLoadingSuites, error } =
    useGetAIAgentsData();

  // Compute filtered agents based on selected category
  const agentsList =
    selectedCategory === "all"
      ? agents
      : agents.filter((agent) =>
          agent.categories.some((c) => c === selectedCategory)
        );

  const handleAgentSelect = (agentKey: string) => {
    setActiveAgent(agentKey);
  };

  return (
    <div className="min-h-full">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -20 }}
        transition={{ duration: 0.3 }}
        className="h-full max-w-screen-2xl mx-auto"
      >
        <div className="max-h-screen font-inter">
          <div className="h-[calc(100vh-100px)] flex overflow-hidden flex-1 gap-8">
            {/* Chat Sidebar */}
            <EnhancedChatSidebar externalMessage={chatMessage} />

            <div className="overflow-y-auto flex-1">
              <AppContainer
                className="space-y-6 lg:space-y-8"
                isPadding={false}
              >
                <div className="mb-6 flex flex-wrap justify-center gap-2 bg-blue-midnight p-4 w-fit">
                  {agentCategories.map((category, index) => (
                    <button
                      key={index}
                      className={`rounded-md ${
                        selectedCategory === category.id
                          ? "bg-primary text-white hover:bg-darkOrangeTwo"
                          : "bg-grayFifteen text-blackOne hover:bg-blue-50"
                      } px-4 py-2.5 font-inter text-sm font-medium transition
                          ${index === 0 ? "ml-0" : "ml-2"}`}
                      onClick={() => handleAgentsFilter(category.id)}
                    >
                      {category.alias}
                    </button>
                  ))}
                </div>

                <div className="flex gap-6">
                  {isLoadingSuites ? (
                    Array.from({ length: 2 }).map((_, index) => (
                      <AgentSkeleton key={`skeleton-${index}`} />
                    ))
                  ) : (
                    <div className="grid grid-cols-1 gap-6">
                      {agentSuites.map((suite) => (
                        <div
                          key={suite.agentSuiteKey}
                          className="flex w-[240px] min-w-[240px] h-[338px] cursor-pointer flex-col items-center overflow-hidden rounded border transition-all hover:shadow-md"
                          onClick={() => {
                            setActiveAgent("");
                            navigate(
                              ROUTES.AGENTS_DETAILS(suite.agentSuiteKey)
                            );
                          }}
                        >
                          <div>
                            <img
                              src={suite.avatar}
                              className="h-[160px] w-full bg-peachTwo object-cover"
                              alt={suite.agentSuiteName}
                              onError={(e) => {
                                const agentKey =
                                  suite.agentSuiteKey.toLowerCase();

                                // Fallback to mock logo if agent avatar fails to load
                                (e.target as HTMLImageElement).src =
                                  mockAgents.filter(
                                    (agent) =>
                                      agent.id.toLowerCase() === agentKey
                                  )[0].image;
                              }}
                            />
                            <div className="flex flex-col gap-2.5 py-3 px-2.5 text-blackOne">
                              <div className="w-fit rounded border border-grayNine bg-grayNineTeen px-2 py-[2px] font-bold">
                                {suite.agentSuiteName}
                              </div>
                              <p className="font-medium leading-5">
                                {suite.description}
                              </p>
                              <p className="mb-3 text-sm text-darkGray">
                                {suite.roleDescription}
                              </p>
                            </div>
                          </div>
                        </div>
                      ))}

                      <div
                        className="flex w-[240px] min-w-[240px] h-[338px] cursor-pointer flex-col items-center overflow-hidden rounded border transition-all hover:shadow-md"
                        onClick={() => {
                          setActiveAgent("regis");
                        }}
                      >
                        <img
                          src={regis}
                          className="h-[160px] w-full bg-[#587C72] object-contain"
                          alt="Regis"
                        />
                        <div className="flex flex-col py-4 px-8 text-blackOne items-center">
                          <div className="w-fit rounded-lg border border-blackOne px-1.5 py-1 font-bold">
                            <ArrowLeft />
                          </div>
                          <p className="text-lg mt-3">Chat with Regis</p>
                          <p className="text-2xl font-semibold text-darkGray text-center">
                            See what's next?
                          </p>
                        </div>
                      </div>
                    </div>
                  )}

                  {isLoadingAgents ? (
                    Array.from({ length: 4 }).map((_, index) => (
                      <AgentSkeleton key={`skeleton-${index}`} />
                    ))
                  ) : (
                    <div className="flex justify-center w-fit">
                      <div className="grid grid-cols-1 gap-6 md:grid-cols-2 justify-items-center">
                        {agentsList.map((agent) => (
                          <div
                            key={agent.agentKey}
                            className="flex w-[240px] h-[338px] cursor-pointer flex-col items-center overflow-hidden rounded border transition-all hover:shadow-md"
                          >
                            <div
                              onClick={() => {
                                agent.agentKey
                                  ? setActiveAgent(agent.agentKey)
                                  : setActiveAgent("");

                                agent.agentSuiteKey &&
                                  navigate(
                                    ROUTES.AGENTS_DETAILS(agent.agentSuiteKey)
                                  );
                              }}
                            >
                              <img
                                src={agent.avatar}
                                className="h-[160px] w-full bg-peachTwo object-contain"
                                alt={agent.agentName}
                                onError={(e) => {
                                  const agentKey = agent.agentKey.toLowerCase();

                                  // Fallback to mock logo if agent avatar fails to load
                                  (e.target as HTMLImageElement).src =
                                    mockAgents.filter(
                                      (agent) =>
                                        agent.id.toLowerCase() === agentKey
                                    )[0].image;
                                }}
                              />
                              <div className="flex flex-col gap-2.5 p-3 text-blackOne">
                                <div className="w-fit rounded border border-grayNine bg-grayNineTeen px-2 py-[2px] font-bold">
                                  {agent.agentName}
                                </div>
                                <p className="font-medium leading-5">
                                  {agent.description}
                                </p>
                                <p className="mb-3 text-sm text-darkGray">
                                  {agent.roleDescription}
                                </p>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </AppContainer>
            </div>
          </div>
        </div>
      </motion.div>
    </div>
  );
};

export default AgentDetailsPage;
