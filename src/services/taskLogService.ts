import { axiosInstance } from '@/helpers/axiosConfig';
import { agenticService } from '@/utils/apiServiceControllersRoute';
import {
  CreateTaskLogRequest,
  UpdateTaskLogRequest,
  TaskLogResponse,
  TaskLogListResponse,
  DeleteTaskLogResponse,
} from '@/types/taskLog';
import type { TaskLogFilter } from '@/types/taskLog';

class TaskLogService {
  private static instance: TaskLogService;
  private readonly BASE_URL: string;

  private constructor() {
    this.BASE_URL = `${agenticService}/task-logs`;
  }

  public static getInstance(): TaskLogService {
    if (!TaskLogService.instance) {
      TaskLogService.instance = new TaskLogService();
    }
    return TaskLogService.instance;
  }

  async createTaskLog(
    kcToken: string | undefined,
    payload: CreateTaskLogRequest
  ): Promise<TaskLogResponse> {
    const response = await axiosInstance.post(this.BASE_URL, payload, {
      headers: { Authorization: `Bearer ${kcToken}` },
    });
    return response.data;
  }

  async getTaskLog(
    kcToken: string | undefined,
    id: string
  ): Promise<TaskLogResponse> {
    const response = await axiosInstance.get(`${this.BASE_URL}/${id}`, {
      headers: { Authorization: `Bearer ${kcToken}` },
    });
    return response.data;
  }

  // New filtered list endpoint using query param 'filter' as JSON string
  async getTaskLogs(
    kcToken: string | undefined,
    filter: TaskLogFilter
  ): Promise<TaskLogListResponse> {
    const { search, ...rest } = filter;
    const response = await axiosInstance.get(this.BASE_URL, {
      headers: { Authorization: `Bearer ${kcToken}` },
      params: {
        search,
        ...rest,
      },
    });
    return response.data;
  }

  async getTaskLogsByTenant(
    kcToken: string | undefined,
    tenantId: string
  ): Promise<TaskLogListResponse> {
    const response = await axiosInstance.get(`${this.BASE_URL}/tenant/${tenantId}`, {
      headers: { Authorization: `Bearer ${kcToken}` },
    });
    return response.data;
  }

  async updateTaskLog(
    kcToken: string | undefined,
    id: string,
    payload: UpdateTaskLogRequest
  ): Promise<TaskLogResponse> {
    const response = await axiosInstance.put(`${this.BASE_URL}/${id}`, payload, {
      headers: { Authorization: `Bearer ${kcToken}` },
    });
    return response.data;
  }

  async deleteTaskLog(
    kcToken: string | undefined,
    id: string
  ): Promise<DeleteTaskLogResponse> {
    const response = await axiosInstance.delete(`${this.BASE_URL}/${id}`, {
      headers: { Authorization: `Bearer ${kcToken}` },
    });
    return response.data;
  }
}

export default TaskLogService.getInstance();
