import { useTenant } from '@/context/TenantContext';
import { usePivotlPrivateRequest } from '@/lib/axios/usePrivateRequest';
import { agenticService } from '@/utils/apiServiceControllersRoute';
import { BASE_URL } from '@/utils/apiUrls';

// TypeScript interfaces for Knowledge Base API
export interface KnowledgeBaseUploadRequest {
  filePart: File;
  fileTag: string;
}

export interface KnowledgeBaseReplaceRequest {
  filePart: File;
  fileRef: string;
}

export interface KnowledgeBaseDeleteRequest {
  fileTag: string;
}

export interface KnowledgeBaseResponse {
  status: boolean;
  message: string;
  data?: any;
}

export interface KnowledgeBaseDocument {
  iconUrl: string;
  name: string;
  key: string;
  description: string;
  hasFile?: boolean;
  fileName?: string;
  uploadedAt?: string;
}

// Suite Level Knowledge Base API Hooks
export const useSuiteKnowledgeBaseApi = () => {
  const { tenantId } = useTenant();

  // For suite-level calls we must NOT send X-Active-Agent header.
  // Pass only tenantId (or undefined) so the hook will not add an empty X-Active-Agent header.
  const axiosInstanceRef = usePivotlPrivateRequest(
    BASE_URL,
    tenantId || undefined
  );

  const checkSuiteKnowledgeBase = async (
    agentSuiteKey: string
  ): Promise<KnowledgeBaseResponse> => {
    try {
      // Ensure tenantId is present
      if (!tenantId) {
        throw new Error(
          'Tenant ID is required for suite-level knowledge base operations'
        );
      }

      const axiosInstance = axiosInstanceRef;

      if (!axiosInstance?.current) {
        throw new Error('Axios instance not initialized');
      }

      const response = await axiosInstance.current.get(
        `${agenticService}/ai-agent-suites/knowledge-base`,
        {
          headers: {
            // Ensure suite header is provided per-request
            'X-Active-Agent-Suite': agentSuiteKey,
          },
        }
      );

      return response?.data || { status: false, message: 'No data' };
    } catch (error) {
      console.error('Suite Knowledge Base Check API Error:', error);
      throw new Error(
        'Failed to check suite knowledge base. Please try again.'
      );
    }
  };

  const uploadSuiteKnowledgeBase = async (
    agentSuiteKey: string,
    payload: KnowledgeBaseUploadRequest
  ): Promise<KnowledgeBaseResponse> => {
    try {
      if (!tenantId) {
        throw new Error(
          'Tenant ID is required for suite-level knowledge base operations'
        );
      }

      const axiosInstance = axiosInstanceRef;

      if (!axiosInstance?.current) {
        throw new Error('Axios instance not initialized');
      }

      const formData = new FormData();
      formData.append('filePart', payload.filePart);
      formData.append('fileTag', payload.fileTag);

      const response = await axiosInstance.current.post(
        `${agenticService}/ai-agent-suites/knowledge-base-upload`,
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data',
            // Provide suite header per-request rather than relying on axios default
            'X-Active-Agent-Suite': agentSuiteKey,
          },
        }
      );

      return response?.data || { status: false, message: 'Upload failed' };
    } catch (error) {
      console.error('Suite Knowledge Base Upload API Error:', error);
      throw new Error(
        'Failed to upload suite knowledge base document. Please try again.'
      );
    }
  };

  const replaceSuiteKnowledgeBase = async (
    agentSuiteKey: string,
    payload: KnowledgeBaseReplaceRequest
  ): Promise<KnowledgeBaseResponse> => {
    try {
      if (!tenantId) {
        throw new Error(
          'Tenant ID is required for suite-level knowledge base operations'
        );
      }

      const axiosInstance = axiosInstanceRef;

      if (!axiosInstance?.current) {
        throw new Error('Axios instance not initialized');
      }

      const formData = new FormData();
      formData.append('filePart', payload.filePart);
      formData.append('fileRef', payload.fileRef);

      const response = await axiosInstance.current.put(
        `${agenticService}/ai-agent-suites/knowledge-base-upload`,
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data',
            'X-Active-Agent-Suite': agentSuiteKey,
          },
        }
      );

      return response?.data || { status: false, message: 'Replace failed' };
    } catch (error) {
      console.error('Suite Knowledge Base Replace API Error:', error);
      throw new Error(
        'Failed to replace suite knowledge base document. Please try again.'
      );
    }
  };

  const deleteSuiteKnowledgeBase = async (
    agentSuiteKey: string,
    payload: KnowledgeBaseDeleteRequest
  ): Promise<KnowledgeBaseResponse> => {
    try {
      if (!tenantId) {
        throw new Error(
          'Tenant ID is required for suite-level knowledge base operations'
        );
      }

      const axiosInstance = axiosInstanceRef;

      if (!axiosInstance?.current) {
        throw new Error('Axios instance not initialized');
      }

      const response = await axiosInstance.current.delete(
        `${agenticService}/ai-agent-suites/knowledge-base-upload`,
        {
          data: payload,
          headers: {
            'X-Active-Agent-Suite': agentSuiteKey,
          },
        }
      );

      return response?.data || { status: false, message: 'Delete failed' };
    } catch (error) {
      console.error('Suite Knowledge Base Delete API Error:', error);
      throw new Error(
        'Failed to delete suite knowledge base document. Please try again.'
      );
    }
  };

  return {
    checkSuiteKnowledgeBase,
    uploadSuiteKnowledgeBase,
    replaceSuiteKnowledgeBase,
    deleteSuiteKnowledgeBase,
  };
};

// Agent Level Knowledge Base API Hooks
export const useAgentKnowledgeBaseApi = () => {
  const { tenantId, activeAgent } = useTenant();

  // Initialize axios instance ref via hook at top-level with tenantId and activeAgent if available.
  // This ensures default headers include tenant and agent when available and avoids empty header values.
  const axiosInstanceRef = usePivotlPrivateRequest(
    BASE_URL,
    tenantId || undefined,
    activeAgent || undefined
  );

  const checkAgentKnowledgeBase = async (
    agentKey?: string
  ): Promise<KnowledgeBaseResponse> => {
    try {
      if (!tenantId) {
        throw new Error(
          'Tenant ID is required for agent-level knowledge base operations'
        );
      }

      if (!agentKey && !activeAgent) {
        throw new Error(
          'Agent key is required for agent-level knowledge base operations'
        );
      }

      const axiosInstance = axiosInstanceRef;

      if (!axiosInstance?.current) {
        throw new Error('Axios instance not initialized');
      }

      const response = await axiosInstance.current.get(
        `${agenticService}/ai-agents/knowledge-base`,
        {
          headers: {
            // Ensure active agent header is present per-request
            'X-Active-Agent': agentKey || activeAgent,
          },
        }
      );

      return response?.data || { status: false, message: 'No data' };
    } catch (error) {
      console.error('Agent Knowledge Base Check API Error:', error);
      throw new Error(
        'Failed to check agent knowledge base. Please try again.'
      );
    }
  };

  const uploadAgentKnowledgeBase = async (
    payload: KnowledgeBaseUploadRequest,
    agentKey?: string
  ): Promise<KnowledgeBaseResponse> => {
    try {
      if (!tenantId) {
        throw new Error(
          'Tenant ID is required for agent-level knowledge base operations'
        );
      }

      if (!agentKey && !activeAgent) {
        throw new Error(
          'Agent key is required for agent-level knowledge base operations'
        );
      }

      const axiosInstance = axiosInstanceRef;

      if (!axiosInstance?.current) {
        throw new Error('Axios instance not initialized');
      }

      const formData = new FormData();
      formData.append('filePart', payload.filePart);
      formData.append('fileTag', payload.fileTag);

      const response = await axiosInstance.current.post(
        `${agenticService}/ai-agents/knowledge-base-upload`,
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data',
            'X-Active-Agent': agentKey || activeAgent,
          },
        }
      );

      return response?.data || { status: false, message: 'Upload failed' };
    } catch (error) {
      console.error('Agent Knowledge Base Upload API Error:', error);
      throw new Error(
        'Failed to upload agent knowledge base document. Please try again.'
      );
    }
  };

  const replaceAgentKnowledgeBase = async (
    payload: KnowledgeBaseReplaceRequest,
    agentKey?: string
  ): Promise<KnowledgeBaseResponse> => {
    try {
      if (!tenantId) {
        throw new Error(
          'Tenant ID is required for agent-level knowledge base operations'
        );
      }

      if (!agentKey && !activeAgent) {
        throw new Error(
          'Agent key is required for agent-level knowledge base operations'
        );
      }

      const axiosInstance = axiosInstanceRef;

      if (!axiosInstance?.current) {
        throw new Error('Axios instance not initialized');
      }

      const formData = new FormData();
      formData.append('filePart', payload.filePart);
      formData.append('fileRef', payload.fileRef);

      const response = await axiosInstance.current.put(
        `${agenticService}/ai-agents/knowledge-base-upload`,
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data',
            'X-Active-Agent': agentKey || activeAgent,
          },
        }
      );

      return response?.data || { status: false, message: 'Replace failed' };
    } catch (error) {
      console.error('Agent Knowledge Base Replace API Error:', error);
      throw new Error(
        'Failed to replace agent knowledge base document. Please try again.'
      );
    }
  };

  const deleteAgentKnowledgeBase = async (
    payload: KnowledgeBaseDeleteRequest,
    agentKey?: string
  ): Promise<KnowledgeBaseResponse> => {
    try {
      if (!tenantId) {
        throw new Error(
          'Tenant ID is required for agent-level knowledge base operations'
        );
      }

      if (!agentKey && !activeAgent) {
        throw new Error(
          'Agent key is required for agent-level knowledge base operations'
        );
      }

      const axiosInstance = axiosInstanceRef;

      if (!axiosInstance?.current) {
        throw new Error('Axios instance not initialized');
      }

      // console.log("Making Agent Knowledge Base Delete API call:", {
      //   endpoint: `${agenticService}/ai-agents/knowledge-base-upload`,
      //   agentKey: agentKey || activeAgent,
      //   tenantId,
      //   fileTag: payload.fileTag,
      // });

      const response = await axiosInstance.current.delete(
        `${agenticService}/ai-agents/knowledge-base-upload`,
        {
          data: payload,
          headers: {
            'X-Active-Agent': agentKey || activeAgent,
          },
        }
      );

      // console.log("Agent Knowledge Base Delete API response:", response);
      return response?.data || { status: false, message: 'Delete failed' };
    } catch (error) {
      console.error('Agent Knowledge Base Delete API Error:', error);
      throw new Error(
        'Failed to delete agent knowledge base document. Please try again.'
      );
    }
  };

  return {
    checkAgentKnowledgeBase,
    uploadAgentKnowledgeBase,
    replaceAgentKnowledgeBase,
    deleteAgentKnowledgeBase,
  };
};
