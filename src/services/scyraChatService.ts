import { useCallback } from "react";
import {
  usePivotlPrivateRequest,
  useUnauthenticatedAgentRequest,
} from "@/lib/axios/usePrivateRequest";
import { agenticService } from "@/utils/apiServiceControllersRoute";
import { BASE_URL } from "@/utils/apiUrls";
import { useTenant } from "../context/TenantContext";
import { useAuth } from "../context/AuthContext";
import { ChatHistoryItem, ChatRequest } from "@/types/agents";

export const CHAT_ENDPOINT = `${agenticService}/ai-agents/chat`;
export const HISTORY_ENDPOINT = `${CHAT_ENDPOINT}/history`;

type ApiResponse<T> = Promise<T>;

const useScyraApi = () => {
  const { tenantId, activeAgent } = useTenant();
  const { isAuthenticated } = useAuth();
  const agentKey = activeAgent || "";

  // Always call both hooks to maintain consistent hook order
  const authenticatedRequest = usePivotlPrivateRequest(
    BASE_URL,
    tenantId || "",
    agentKey
  );
  const unauthenticatedRequest = useUnauthenticatedAgentRequest(
    BASE_URL,
    agentKey
  );

  // Return the appropriate request based on authentication status
  return isAuthenticated ? authenticatedRequest : unauthenticatedRequest;
};

// Scyra chat hook
export const useScyraChatApi = () => {
  const pivotlRequestRef = useScyraApi();

  const chatWithScyra = useCallback(
    async (payload: ChatRequest): ApiResponse<string> => {
      try {
        const axiosInstance = pivotlRequestRef.current;
        if (!axiosInstance) {
          throw new Error("Axios instance not initialized");
        }

        const { data } = await axiosInstance.post(CHAT_ENDPOINT, payload);
        return data || "";
      } catch (error) {
        // Try to surface agent name if present on the axios instance headers or fallback
        const agentName =
          pivotlRequestRef.current?.defaults?.headers?.["X-Active-Agent"] ||
          "agent";
        console.error(`${agentName} Chat API Error:`, error);
        throw new Error(
          `Failed to communicate with ${agentName}. Please try again.`
        );
      }
    },
    [pivotlRequestRef]
  );

  return chatWithScyra;
};

// Chat history hook
export const useScyraChatHistoryApi = () => {
  const pivotlRequestRef = useScyraApi();

  const fetchChatHistory = useCallback(async (): ApiResponse<
    ChatHistoryItem[]
  > => {
    try {
      const axiosInstance = pivotlRequestRef.current;
      if (!axiosInstance) {
        throw new Error("Axios instance not initialized");
      }

      const { data } = await axiosInstance.get(HISTORY_ENDPOINT);
      return data || [];
    } catch (error) {
      console.error("Failed to fetch chat history:", error);
      throw new Error("Failed to fetch chat history");
    }
  }, [pivotlRequestRef]);

  return fetchChatHistory;
};
