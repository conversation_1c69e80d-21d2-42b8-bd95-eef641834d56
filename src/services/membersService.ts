import { axiosInstance } from '@/helpers/axiosConfig';
import { agenticUserService } from '@/utils/apiServiceControllersRoute';
import type {
  InviteSuiteMemberRequest,
  UpdateSuiteMemberRoleRequest,
  UpdateSuiteInviteRoleRequest,
  ClaimAgentSuiteRequest,
  ApiInvitesListResponse,
  ApiMembersListResponse,
  ApiStandardResponse,
} from '@/types/members';

class MembersService {
  private static instance: MembersService;
  private readonly BASE_URL: string;

  private constructor() {
    this.BASE_URL = `${agenticUserService}/tenants`;
  }

  public static getInstance(): MembersService {
    if (!MembersService.instance) {
      MembersService.instance = new MembersService();
    }
    return MembersService.instance;
  }

  async getSuiteMembers(
    kcToken: string | undefined,
    filters: {
      agentSuiteKey: string;
      searchQuery: string;
      page: number;
      pageSize: number;
    }
  ): Promise<ApiMembersListResponse> {
    const response = await axiosInstance.get(`${this.BASE_URL}/suite-members`, {
      headers: { Authorization: `Bearer ${kcToken}` },
      params: {
        agentSuiteKey: filters.agentSuiteKey,
        search: filters.searchQuery,
        page: filters.page,
        pageSize: filters.pageSize,
      },
    });
    return response.data;
  }

  async getSuiteInvites(
    kcToken: string | undefined,
    filters: {
      agentSuiteKey: string;
      searchQuery: string;
      page: number;
      pageSize: number;
    }
  ): Promise<ApiInvitesListResponse> {
    const response = await axiosInstance.get(`${this.BASE_URL}/suite-invites`, {
      headers: { Authorization: `Bearer ${kcToken}` },
      params: {
        agentSuiteKey: filters.agentSuiteKey,
        search: filters.searchQuery,
        page: filters.page,
        pageSize: filters.pageSize,
      },
    });
    return response.data;
  }

  async inviteSuiteMember(
    kcToken: string | undefined,
    payload: InviteSuiteMemberRequest
  ): Promise<ApiStandardResponse<string>> {
    const response = await axiosInstance.post(
      `${this.BASE_URL}/suite-members`,
      payload,
      {
        headers: { Authorization: `Bearer ${kcToken}` },
      }
    );
    return response.data;
  }

  async updateSuiteMemberRole(
    kcToken: string | undefined,
    memberId: string,
    payload: UpdateSuiteMemberRoleRequest
  ): Promise<ApiStandardResponse<string>> {
    const response = await axiosInstance.patch(
      `${this.BASE_URL}/suite-members-role/${memberId}`,
      payload,
      {
        headers: { Authorization: `Bearer ${kcToken}` },
      }
    );
    return response.data;
  }

  async updateSuiteInviteRole(
    kcToken: string | undefined,
    inviteId: string,
    payload: UpdateSuiteInviteRoleRequest
  ): Promise<ApiStandardResponse<string>> {
    const response = await axiosInstance.patch(
      `${this.BASE_URL}/suite-invites-role/${inviteId}`,
      payload,
      {
        headers: { Authorization: `Bearer ${kcToken}` },
      }
    );
    return response.data;
  }

  async removeSuiteMember(
    kcToken: string | undefined,
    memberId: string,
    agentSuiteKey: string
  ): Promise<ApiStandardResponse<string>> {
    const response = await axiosInstance.delete(
      `${this.BASE_URL}/suite-members/${memberId}`,
      {
        params: { agentSuiteKey: agentSuiteKey },
        headers: { Authorization: `Bearer ${kcToken}` },
      }
    );
    return response.data;
  }

  async cancelSuiteInvite(
    kcToken: string | undefined,
    inviteId: string,
    agentSuiteKey: string
  ): Promise<ApiStandardResponse<string>> {
    const response = await axiosInstance.delete(
      `${this.BASE_URL}/suite-invites/${inviteId}?agentSuiteKey=${agentSuiteKey}`,
      {
        headers: { Authorization: `Bearer ${kcToken}` },
      }
    );
    return response.data;
  }

  async claimAgentSuite(
    kcToken: string | undefined,
    payload: ClaimAgentSuiteRequest
  ): Promise<any> {
    const response = await axiosInstance.post(
      `${this.BASE_URL}/claim-agent-suite`,
      payload,
      {
        headers: { Authorization: `Bearer ${kcToken}` },
      }
    );
    return response.data;
  }
}

export default MembersService.getInstance();


