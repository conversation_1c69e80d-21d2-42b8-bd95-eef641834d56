import { axiosInstance } from '@/helpers/axiosConfig';
import type {
  CreateDailyInsightRequest,
  DailyInsightFilter,
  DailyInsightListResponse,
  DailyInsightResponse,
} from '@/types/dashboard';
import { agenticService } from '@/utils/apiServiceControllersRoute';

class DashboardService {
  private static instance: DashboardService;
  private readonly BASE_URL: string;

  private constructor() {
    this.BASE_URL = `${agenticService}/daily-insights`;
  }

  public static getInstance(): DashboardService {
    if (!DashboardService.instance) {
      DashboardService.instance = new DashboardService();
    }
    return DashboardService.instance;
  }

  async getDailyInsights(
    kcToken: string | undefined,
    filter: DailyInsightFilter
  ): Promise<DailyInsightListResponse> {
    const response = await axiosInstance.get(this.BASE_URL, {
      headers: { Authorization: `Bearer ${kcToken}` },
      params: filter,
    });
    return response.data;
  }

  async createDailyInsight(
    kcToken: string | undefined,
    payload: CreateDailyInsightRequest
  ): Promise<DailyInsightResponse> {
    const response = await axiosInstance.post(this.BASE_URL, payload, {
      headers: { Authorization: `Bearer ${kcToken}` },
    });
    return response.data;
  }
}

export default DashboardService.getInstance();
