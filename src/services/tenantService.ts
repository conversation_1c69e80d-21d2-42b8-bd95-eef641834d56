import { usePivotlPrivateRequest } from '@/lib/axios/usePrivateRequest';
import { BASE_URL } from '@/utils/apiUrls';
import { agenticUserService } from '@/utils/apiServiceControllersRoute';
import { useTenant } from '@/context/TenantContext';
import { TenantInfo } from '../types/user';

// TypeScript interfaces for Tenant API
export interface UpdateTenantInfoRequest {
  tenantId: string;
  tenantName: string;
  tenantAddress: string;
  tenantEmail: string;
}

export interface UpdateTenantInfoResponse {
  status: boolean;
  message: string;
  data: TenantInfo;
}

export interface TenantApiErrorResponse {
  status: boolean;
  message: string;
  data: {
    timestamp: string;
    details: string;
  };
}

// Hook for tenant API calls
export const useUpdateTenantInfoApi = () => {
  const { tenantId: activeTenantId } = useTenant();
  const axiosInstance = usePivotlPrivateRequest(
    BASE_URL,
    activeTenantId || '',
    ''
  );

  const updateTenantInfo = async (
    payload: UpdateTenantInfoRequest
  ): Promise<UpdateTenantInfoResponse> => {
    const response = await axiosInstance.current?.patch(
      `${agenticUserService}/tenants/update-info`,
      payload,
      {
        timeout: 30000,
      }
    );
    return response?.data;
  };

  return updateTenantInfo;
};
