import { axiosInstance } from '@/helpers/axiosConfig';
import type {
  AssignmentLogFilter,
  AssignmentLogListResponse,
  AssignmentLogResponse,
} from '@/types/assignmentLog';
import { agenticService } from '@/utils/apiServiceControllersRoute';

class AssignmentLogService {
  private static instance: AssignmentLogService;
  private readonly BASE_URL: string;

  private constructor() {
    this.BASE_URL = `${agenticService}/assignment-logs`;
  }

  public static getInstance(): AssignmentLogService {
    if (!AssignmentLogService.instance) {
      AssignmentLogService.instance = new AssignmentLogService();
    }
    return AssignmentLogService.instance;
  }

  async getAssignmentLogs(
    kcToken: string | undefined,
    filter: AssignmentLogFilter
  ): Promise<AssignmentLogListResponse> {
    const { search, ...rest } = filter;
    const response = await axiosInstance.get(this.BASE_URL, {
      params: {
        search,
        ...rest,
      },
      headers: { Authorization: `Bearer ${kcToken}` },
    });
    return response.data;
  }

  async getAssignmentLog(
    kcToken: string | undefined,
    id: string
  ): Promise<AssignmentLogResponse> {
    const response = await axiosInstance.get(`${this.BASE_URL}/${id}`, {
      headers: { Authorization: `Bearer ${kcToken}` },
    });
    return response.data;
  }
}

export default AssignmentLogService.getInstance();
