export interface DailyInsight {
  id: string;
  agentName: string;
  insight: string;
  createdAt: string;
  createdBy: string;
  tenantId: string;
}

export interface DailyInsightFilter {
  agentName?: string;
  tenantId: string;
  search?: string;
  from?: string;
  to?: string;
  page?: number;
  pageSize?: number;
}

export interface CreateDailyInsightRequest {
  agentName: string;
  tenantId: string;
  insight: string;
}

export interface DailyInsightListResponse {
  status: boolean;
  message: string;
  data: {
    insights: DailyInsight[];
    total: number;
    page: number;
    pageSize: number;
  };
}

export interface DailyInsightResponse {
  status: boolean;
  message: string;
  data: DailyInsight;
}
