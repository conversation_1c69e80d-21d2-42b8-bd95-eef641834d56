export interface Attachment {
  name: string;
  link: string;
  type: string;
  source: string;
}

export interface AuditEntry {
  timestamp: string;
  actor: string;
  event: string;
  notes: string;
}

export interface TaskLog {
  id: string;
  createdAt: string;
  updatedAt: string;
  createdBy: string;
  updatedBy?: string;
  userId: string;
  tenantId: string;
  taskTitle: string;
  assignedTo: string;
  status: TaskLogStatus;
  priority: TaskLogPriority;
  dueDate: string;
  successCriteria?: string;
  inputPayload?: Record<string, any>;
  outputPayload?: Record<string, any>;
  taskType: string;
  dependsOn?: string[];
  completedBy?: string;
  completedAt?: string;
  auditTrail: AuditEntry[];
  escalationPolicy?: string;
  escalatedTo?: string;
  humanOverrideAllowed: boolean;
  confidenceScore?: number;
  tags?: string[];
  resolutionNotes?: string;
  recurrenceRule?: string;
  timeSpentSeconds?: number;
  attachments?: Attachment[];
  recurring: boolean;
}

export type TaskLogStatus = 'PENDING' | 'IN_PROGRESS' | 'COMPLETED' | 'CANCELLED';
export type TaskLogPriority = 'LOW' | 'MEDIUM' | 'HIGH';

// Display versions for UI
export type TaskLogStatusDisplay =
  | 'Not Started'
  | 'In Progress'
  | 'Completed'
  | 'Cancelled';
export type TaskLogPriorityDisplay = 'Low' | 'Medium' | 'High';

// Mapping functions for API to UI conversion
export const mapStatusToDisplay = (status: TaskLogStatus): TaskLogStatusDisplay => {
  const statusMap: Record<TaskLogStatus, TaskLogStatusDisplay> = {
    'PENDING': 'Not Started',
    'IN_PROGRESS': 'In Progress', 
    'COMPLETED': 'Completed',
    'CANCELLED': 'Cancelled'
  };
  return statusMap[status];
};

export const mapDisplayToStatus = (display: TaskLogStatusDisplay): TaskLogStatus => {
  const displayMap: Record<TaskLogStatusDisplay, TaskLogStatus> = {
    'Not Started': 'PENDING',
    'In Progress': 'IN_PROGRESS',
    'Completed': 'COMPLETED', 
    'Cancelled': 'CANCELLED'
  };
  return displayMap[display];
};

export const mapPriorityToDisplay = (
  priority: TaskLogPriority
): TaskLogPriorityDisplay => {
  const priorityMap: Record<TaskLogPriority, TaskLogPriorityDisplay> = {
    LOW: 'Low',
    MEDIUM: 'Medium',
    HIGH: 'High',
  };
  return priorityMap[priority];
};

export const mapDisplayToPriority = (
  display: TaskLogPriorityDisplay
): TaskLogPriority => {
  const displayMap: Record<TaskLogPriorityDisplay, TaskLogPriority> = {
    Low: 'LOW',
    Medium: 'MEDIUM',
    High: 'HIGH',
  };
  return displayMap[display];
};

// Request/Response types for API
export interface CreateTaskLogRequest {
  userId: string;
  tenantId: string;
  taskTitle: string;
  assignedTo: string;
  status: TaskLogStatus;
  priority: TaskLogPriority;
  dueDate: string;
  successCriteria?: string;
  inputPayload?: Record<string, any>;
  taskType: string;
  dependsOn?: string[];
  escalationPolicy?: string;
  humanOverrideAllowed: boolean;
  tags?: string[];
  isRecurring: boolean;
  recurrenceRule?: string;
  attachments?: Attachment[];
}

export interface UpdateTaskLogRequest {
  id: string;
  userId?: string;
  tenantId?: string;
  assignedTo?: string;
  status?: TaskLogStatus;
  priority?: TaskLogPriority;
  dueDate?: string;
  successCriteria?: string;
  inputPayload?: Record<string, any>;
  outputPayload?: Record<string, any>;
  dependsOn?: string[];
  completedBy?: string;
  completedAt?: string;
  escalationPolicy?: string;
  escalatedTo?: string;
  humanOverrideAllowed?: boolean;
  confidenceScore?: number;
  tags?: string[];
  resolutionNotes?: string;
  isRecurring?: boolean;
  recurrenceRule?: string;
  timeSpentSeconds?: number;
  attachments?: Attachment[];
  auditEvent?: string;
  auditNotes?: string;
}

export interface TaskLogListResponse {
  status: boolean;
  message: string;
  data: {
    tasks: TaskLog[];
    total: number;
    page: number;
    pageSize: number;
  };
}

export interface TaskLogResponse {
  status: boolean;
  message: string;
  data: TaskLog;
}

export interface DeleteTaskLogResponse {
  status: boolean;
  message: string;
  data: string;
}

export interface ApiErrorResponse {
  status: boolean;
  message: string;
  data: {
    timestamp: string;
    message: string;
    details: string;
  };
}

// Filters for fetching task logs list
export interface TaskLogFilter {
  search?: string;
  assignedTo: string; // Make required as API expects it
  tenantId: string;
  status?: TaskLogStatus;
  priority?: TaskLogPriority;
  from?: string; // ISO date string for date range filtering
  to?: string; // ISO date string for date range filtering
  page?: number;
  pageSize?: number;
}

// UI specific types for the details page
export interface TaskLogDetailsFormData {
  taskTitle: string;
  description: string;
  time: string;
  dateCreated: string;
  dueDate: string;
  assignedBy: string;
  type: string;
  priority: TaskLogPriorityDisplay;
  status: TaskLogStatusDisplay;
  escalationPolicy: string;
}
