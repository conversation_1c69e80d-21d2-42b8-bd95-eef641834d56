interface FileData {
  iconUrl: string;
  name: string;
  key: string;
  description: string;
}

interface UserInfo {
  userId: string;
  firstName: string;
  lastName: string;
  phoneNumber: string | null;
  profilePicture: string | null;
  title: string;
  email: string;
  country: string | null;
  gender: string | null;
  phoneNumberVerified: boolean;
  accountActive: boolean;
  emailVerified: boolean;
  dateOfBirth: string | Date | null;
  userRoles: string[];
  timezone: string | null;
  roleInCompany: string | null;
  tenant: TenantInfo;
  lastActivity: string | null;
}

interface TenantInfo {
  tenantId: string;
  tenantDomain: string;
  claimedAgentSuites: ClaimedAgentSuite[];
  plan: Plan;
  subscriptionStatus: string; // Define a union type if known: "ACTIVE" | "INACTIVE" | "EXPIRED" | "TRIAL"
  trialExpiryDate: string | null;
  subscriptionExpiryDate: string | null;
}

interface ClaimedAgentSuite {
  suite: AgentSuite;
  claimerId: string | null;
  claimerEmail: string | null;
  tenantDomain: string;
  members: Member[];
}

interface Member {
  agentSuite: AgentSuite | null;
  user: UserInfo;
  memberRoles: string[];
}

interface AgentSuite {
  agentSuiteName: string;
  agentSuiteKey: string;
  description: string;
  roleDescription: string;
  avatar: string;
  availableAgents: Agent[];
  enabled: boolean;
  fileData?: FileData[];
}

interface Agent {
  id?: string;
  createdAt?: string;
  updatedAt?: string;
  agentName: string;
  agentKey: string;
  agentSuiteKey?: string;
  description: string;
  roleDescription: string;
  avatar: string;
  roles: string[];
  categories: string[];
  enabled: boolean;
  fileData?: FileData[];
}

interface Plan {
  id: string;
  subscriptionType: string; // Define a union type if known: "Free" | "Basic" | "Premium" | "Enterprise"
  dollarRate: string;
  currencyCode?: string | null;
}

interface UserBasicInfoPayload {
  userInfo: UserInfo;
  tenantAgents: Agent[];
}

export interface UpdateUserInfoRequest {
  firstname: string;
  lastname: string;
  gender?: string;
  country?: string;
  title?: string;
  timezone?: string;
  roleInCompany?: string;
}

export interface UpdateAvatarRequest {
  avatar: string;
}

export interface ChangePasswordRequest {
  token: string;
  oldPassword: string;
  newPassword: string;
}

export interface UserProfileResponse {
  id: string;
  createdAt: string;
  updatedAt: string;
  userId: string;
  firstName: string;
  lastName: string;
  phoneNumber: string;
  title: string;
  email: string;
  gender: string;
  country: string;
  emailVerified: boolean;
  profilePicture: string;
  dateOfBirth: string;
  accountActive: boolean;
  phoneNumberVerified: boolean;
  userRoles: string[];
  timezone: string;
  roleInCompany: string;
}

export interface ApiResponse<T> {
  status: boolean;
  message: string;
  data: T;
}

export interface ApiErrorResponse {
  status: boolean;
  message: string;
  data: {
    timestamp: string;
    details: string;
  };
}

export type {
  UserInfo,
  TenantInfo,
  ClaimedAgentSuite,
  AgentSuite,
  Agent,
  Plan,
  FileData,
  UserBasicInfoPayload,
  Member,
};
