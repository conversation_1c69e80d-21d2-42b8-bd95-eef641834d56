export interface AssignmentLogAttachment {
  name: string;
  link: string;
  type: string;
  source: string;
}

export interface AssignmentLogAuditEntry {
  timestamp: string;
  actor: string;
  event: string;
  notes: string;
}

export type AssignmentLogStatus =
  | 'PENDING'
  | 'IN_PROGRESS'
  | 'COMPLETED'
  | 'CANCELLED';
export type AssignmentLogPriority =
  | 'LOW'
  | 'MEDIUM'
  | 'HIGH'
  | 'CRITICAL'
  | 'URGENT';

export interface AssignmentLogTaskTypeRef {
  id: string;
  createdAt: string;
  updatedAt: string;
  createdBy: string;
  updatedBy: string;
  name: string;
  description: string;
}

export interface AssignmentLog {
  id: string;
  createdAt: string;
  updatedAt: string;
  createdBy: string;
  updatedBy?: string;
  userId: string;
  tenantId: string;
  taskTitle: string;
  assignedTo: string;
  status: AssignmentLogStatus;
  priority: AssignmentLogPriority;
  dueDate?: string;
  successCriteria?: string;
  inputPayload?: Record<string, any>;
  outputPayload?: Record<string, any>;
  dependsOn?: string[];
  completedBy?: string;
  completedAt?: string;
  auditTrail?: AssignmentLogAuditEntry[];
  escalationPolicy?: string;
  escalatedTo?: string;
  humanOverrideAllowed?: boolean;
  confidenceScore?: number;
  tags?: string[];
  resolutionNotes?: string;
  recurrenceRule?: string;
  timeSpentSeconds?: number;
  attachments?: AssignmentLogAttachment[];
  taskTypeReference?: AssignmentLogTaskTypeRef;
  recurring?: boolean;
}

export interface AssignmentLogFilter {
  search?: string;
  assignedTo?: string;
  tenantId: string;
  status?: AssignmentLogStatus;
  assignedDateFrom?: string; // ISO date string for assigned date range filtering
  assignedDateTo?: string; // ISO date string for assigned date range filtering
  startDateFrom?: string; // ISO date string for start date range filtering
  startDateTo?: string; // ISO date string for start date range filtering
  page?: number;
  pageSize?: number;
}

export interface AssignmentLogListResponse {
  status: boolean;
  message: string;
  data: {
    assignments: AssignmentLog[];
    total: number;
    page: number;
    pageSize: number;
  };
}

export interface AssignmentLogResponse {
  status: boolean;
  message: string;
  data: AssignmentLog;
}

// Display versions for UI
export type AssignmentLogStatusDisplay =
  | 'Not Started'
  | 'In Progress'
  | 'Completed'
  | 'Cancelled';

export type AssignmentLogCheckInsDisplay =
  | 'Hourly'
  | 'Daily'
  | 'Weekly'
  | 'Quarterly'
  | 'Monthly';

// Mapping functions for API to UI conversion
export const mapAssignmentStatusToDisplay = (
  status: AssignmentLogStatus
): AssignmentLogStatusDisplay => {
  const statusMap: Record<AssignmentLogStatus, AssignmentLogStatusDisplay> = {
    PENDING: 'Not Started',
    IN_PROGRESS: 'In Progress',
    COMPLETED: 'Completed',
    CANCELLED: 'Cancelled',
  };
  return statusMap[status];
};

export const mapAssignmentDisplayToStatus = (
  display: AssignmentLogStatusDisplay
): AssignmentLogStatus => {
  const displayMap: Record<AssignmentLogStatusDisplay, AssignmentLogStatus> = {
    'Not Started': 'PENDING',
    'In Progress': 'IN_PROGRESS',
    Completed: 'COMPLETED',
    Cancelled: 'CANCELLED',
  };
  return displayMap[display];
};

// UI specific types for the assignment details page
export interface AssignmentLogDetailsFormData {
  taskTitle: string;
  description: string;
  time: string;
  dateCreated: string;
  dueDate: string;
  assignedTo?: string;
  createdBy: string;
  type: string;
  checkIns: AssignmentLogCheckInsDisplay;
  status: AssignmentLogStatusDisplay;
  escalationPolicy: string;
}
