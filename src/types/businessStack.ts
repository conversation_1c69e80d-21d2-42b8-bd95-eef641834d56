export interface ScyraMessage {
  id: string;
  sender: string;
  content: string;
  timestamp: Date;
  senderName: string;
}

export interface ScyraChatState {
  messages: ScyraMessage[];
  isLoading: boolean;
  sessionId: string;
}

export interface ScyraChatInterfaceProps {
  state: ScyraChatState;
  ChatInputComponent: React.ComponentType;
  groupedMessages?: Record<string, ScyraMessage[]>;
}

export interface FormField {
  key: string;
  label: string | null;
  placeholder: string | null;
  description: string | null;
  type: 'TEXT' | 'PASSWORD' | string; // Add other possible types if needed
  required: boolean;
  minLength: number | null;
  maxLength: number | null;
  options: any[] | null;
  order: number | null;
}

interface FormConfig {
  submitButtonText: string;
  fields: FormField[];
}

interface PreAuthConfig {
  title: string;
  submitButtonText: string;
  fields: FormField[];
}

interface UiHints {
  shortDescription: string | null;
  longDescription: string | null;
  connectButtonText: string | null;
}

export interface AvailableApp {
  key: string;
  name: string;
  logo: string;
  enabled: boolean;
  authType: 'BASIC' | 'OAUTH2' | string; // Add other auth types
  appCategory:
    | 'MESSAGING_SYSTEM'
    | 'DOCUMENT_REPO'
    | 'CRM_SYSTEM'
    | 'EMAILING_SYSTEM'
    | string;
  uiHints: UiHints;
  form: FormConfig | null;
  preAuth: PreAuthConfig | null;
  connectionRequestFields: FormField[];
  isConnected: boolean;
}

export interface AvailableAppsGridProps {
  apps: AvailableApp[];
  isLoading: boolean;
  searchQuery: string;
  selectedCategory: string;
  onSearchChange: (query: string) => void;
  onCategoryChange: (category: string) => void;
  onConnectApp: (appName: string) => void;
  connectionFlow?: ConnectionFlowContext;
}

export interface AppCardProps {
  app: AvailableApp;
  onConnect: (appName: string) => void;
  connectionFlow?: ConnectionFlowContext;
}

export interface BusinessStackPageState {
  availableApps: AvailableApp[];
  isLoadingApps: boolean;
  searchQuery: string;
  selectedCategory: string;
  scyraChatState: ScyraChatState;
  currentPage: number;
  totalPages: number;
}

// Unified Connection Flow Types
export type ConnectionFlowStep =
  | 'idle'
  | 'collecting-preauth'
  | 'collecting-form'
  | 'authenticating'
  | 'waiting-auth'
  | 'saving-credentials'
  | 'success'
  | 'error';

export interface ConnectionFlowState {
  step: ConnectionFlowStep;
  isLoading: boolean;
  error?: string;
  currentApp?: AvailableApp;
  collectedData: Record<string, string>;
  currentFieldIndex: number;
  fieldsToCollect: FormField[];
  connectionUrl?: string;
  authResult?: {
    code?: string;
    state?: string;
  };
  localMessages: ScyraMessage[];
  tabMonitor?: NodeJS.Timeout;
}

export interface ConnectionFlowContext {
  state: ConnectionFlowState;
  startConnection: (app: AvailableApp) => void;
  handleUserInput: (input: string) => void;
  handleDisconnect: (app: AvailableApp) => void;
  handleReconnect: (app: AvailableApp) => void;
  resetFlow: (preserveMessages?: boolean) => void;
  handleOAuthCallback: (
    appKey: string,
    code: string,
    state?: string
  ) => Promise<void>;
  handleOAuthCallbackWithApp: (
    app: AvailableApp,
    code: string,
    state?: string
  ) => Promise<void>;
  handleSaveCredentials: (
    app: AvailableApp,
    credentials: Record<string, string>
  ) => Promise<void>;
}

// Enhanced ScyraChatInterface props for connection flows
export interface EnhancedScyraChatInterfaceProps
  extends ScyraChatInterfaceProps {
  connectionFlow?: ConnectionFlowContext;
  originalSendMessage?: (message: string) => void;
}

// Connection API types
export interface ConnectionUrlRequest {
  appKey: string;
  params?: Record<string, string>;
}

export interface ConnectionUrlResponse {
  status: boolean;
  message: string;
  data: {
    data: string;
  };
}

export interface SaveConnectionCredentialsRequest {
  appKey: string;
  connectionData: Record<string, string>;
}

export interface SaveConnectionCredentialsResponse {
  status: boolean;
  message: string;
  data?: any;
}

export interface DisconnectAppRequest {
  appKey: string;
}

export interface DisconnectAppResponse {
  status: boolean;
  message: string;
  data?: any;
}

export interface OAuthPopupResult {
  success: boolean;
  state?: string;
  error?: string;
}
