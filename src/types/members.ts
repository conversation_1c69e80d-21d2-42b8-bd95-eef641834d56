import { UserInfo } from './user';

export interface MemberUser extends Omit<UserInfo, 'tenant' | 'userRoles'> {
  // Fields can be null in this context
}

export interface Member {
  id: string;
  claimedAgentSuite: any; // Or a more specific type if available
  user: MemberUser;
  memberRoles: SuiteMemberRoleApi[];
}

export interface Invitation {
  id: string;
  agentSuiteKey: string;
  firstname: string;
  lastname: string;
  email: string;
  role: MemberRole;
  tenantId: string;
  createdAt: string;
}

export type MemberRole = 'manager' | 'lead' | 'member';

export interface RolePermissions {
  canAccessAllFeatures: boolean;
  canAccessDashboards: boolean;
  canManageMembers: boolean;
  canViewAnalytics: boolean;
  canManageSettings: boolean;
}

export const ROLE_PERMISSIONS: Record<MemberRole, RolePermissions> = {
  manager: {
    canAccessAllFeatures: true,
    canAccessDashboards: true,
    canManageMembers: true,
    canViewAnalytics: true,
    canManageSettings: true,
  },
  lead: {
    canAccessAllFeatures: true,
    canAccessDashboards: true,
    canManageMembers: false,
    canViewAnalytics: true,
    canManageSettings: false,
  },
  member: {
    canAccessAllFeatures: false,
    canAccessDashboards: true,
    canManageMembers: false,
    canViewAnalytics: false,
    canManageSettings: false,
  },
};

export const ROLE_DESCRIPTIONS: Record<MemberRole, string> = {
  manager: 'The manager can access all suite features.',
  lead: 'Leads can access all suite features.',
  member:
    'Members can get authorized information from manager and lead to access dashboards.',
};

export interface MembersPageState {
  members: Member[];
  invitations: Invitation[];
  loading: boolean;
  searchQuery: string;
  selectedRole: MemberRole | 'all';
  currentPage: number;
  totalPages: number;
  totalMembers: number;
  totalInvitations: number;
}

export interface MemberFormData {
  emails: string[];
  role: MemberRole;
}

export interface UpdateRoleData {
  memberId: string;
  newRole: MemberRole;
}

// ===== API types for Tenant Members/Invites (tenant-controller) =====

// API uses uppercase role values
export type SuiteMemberRoleApi = 'MANAGER' | 'LEAD' | 'MEMBER';

export interface InviteSuiteMemberRequest {
  id?: string;
  agentSuiteKey: string;
  firstname: string;
  lastname: string;
  email: string;
  role: SuiteMemberRoleApi;
}

export interface UpdateSuiteMemberRoleRequest {
  agentSuiteKey: string;
  memberRole: SuiteMemberRoleApi;
}

export interface UpdateSuiteInviteRoleRequest {
  agentSuiteKey: string;
  memberRole: SuiteMemberRoleApi;
}

export interface ClaimAgentSuiteRequest {
  data: string; // agentSuiteKey
}

export interface ApiStandardResponse<T> {
  status: boolean;
  message: string;
  data: T;
}

export interface ApiMembersListResponse {
  status: boolean;
  message: string;
  data: {
    members: Member[];
    total: number;
    page: number;
    pageSize: number;
  };
}

export interface ApiInvitesListResponse {
  status: boolean;
  message: string;
  data: {
    invites: Invitation[];
    total: number;
    page: number;
    pageSize: number;
  };
}

export const mapApiRoleToUiRole = (role: SuiteMemberRoleApi): MemberRole => {
  const map: Record<SuiteMemberRoleApi, MemberRole> = {
    MANAGER: 'manager',
    LEAD: 'lead',
    MEMBER: 'member',
  };
  return map[role];
};

export const mapUiRoleToApiRole = (role: MemberRole): SuiteMemberRoleApi => {
  const map: Record<MemberRole, SuiteMemberRoleApi> = {
    manager: 'MANAGER',
    lead: 'LEAD',
    member: 'MEMBER',
  };
  return map[role];
};
