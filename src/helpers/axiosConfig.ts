import { BASE_URL } from '@/utils/apiUrls';
import { isDevEnvironment } from '@/utils/helpers';
import type { AxiosInstance } from 'axios';
import axios from 'axios';
import { useKeycloak } from '@react-keycloak/web';

// Create base axios instance without auth
const createBaseAxiosInstance = (): AxiosInstance => {
  const instance = axios.create({
    baseURL: BASE_URL,
    timeout: 120000,
    maxContentLength: Infinity,
    maxBodyLength: Infinity,
  });

  instance.defaults.headers.common['Save-Data'] = 'on';

  instance.interceptors.response.use(
    (response) => response,
    async (error) => {
      if (error.code === 'ECONNABORTED') {
        console.warn('Request timed out. Retrying...');
        return instance?.request(error.config);
      }
      return Promise.reject(error);
    }
  );

  return instance;
};

// Create instance with auth token (to be called from components/hooks)
export const createAuthenticatedAxiosInstance = (): string | undefined => {
  const { keycloak, initialized } = useKeycloak();
  const authToken =
    initialized || isDevEnvironment()
      ? (keycloak?.token ?? import.meta.env.VITE_DEV_ACCESS_TOKEN)
      : '';

  return authToken;
};

// Base instance for non-authenticated requests
export const axiosInstance = createBaseAxiosInstance();

// Hook to get authenticated axios instance
export const useAuthenticatedAxios = () => {
  // This will be properly implemented in a hook that uses useKeycloak
  return axiosInstance;
};