/**
 * OAuth Redirect Handler Utility
 *
 * This utility handles the OAuth redirect flow for Microsoft Outlook (Organisation) integration.
 * It should be used on the redirect_uri page that Microsoft redirects to after authentication.
 */

/**
 * Extracts OAuth parameters from the current URL
 * @returns Object containing state, code, error, and other OAuth parameters
 */
export const extractOAuthParams = () => {
  const urlParams = new URLSearchParams(window.location.search);
  const hashParams = new URLSearchParams(window.location.hash.substring(1));

  return {
    state: urlParams.get('state') || hashParams.get('state'),
    code: urlParams.get('code') || hashParams.get('code'),
    error: urlParams.get('error') || hashParams.get('error'),
    errorDescription:
      urlParams.get('error_description') || hashParams.get('error_description'),
    adminConsent:
      urlParams.get('admin_consent') || hashParams.get('admin_consent'),
  };
};

/**
 * Sends OAuth result to parent window (for popup flow)
 * This function should be called on the redirect_uri page
 */
export const sendOAuthResultToParent = () => {
  const params = extractOAuthParams();

  if (window.opener) {
    if (params.error) {
      // Send error to parent window
      window.opener.postMessage(
        {
          type: 'OAUTH_ERROR',
          error:
            params.errorDescription || params.error || 'Authentication failed',
        },
        window.location.origin,
      );
    } else if (params.state) {
      // Send success with state to parent window
      window.opener.postMessage(
        {
          type: 'OAUTH_SUCCESS',
          state: params.state,
          code: params.code,
          adminConsent: params.adminConsent,
        },
        window.location.origin,
      );
    }

    // Close the popup window
    window.close();
  }
};

/**
 * Example usage for redirect_uri page:
 *
 * ```typescript
 * import { sendOAuthResultToParent } from './utils/oauthRedirectHandler';
 *
 * // Call this when the redirect page loads
 * useEffect(() => {
 *   sendOAuthResultToParent();
 * }, []);
 * ```
 */
