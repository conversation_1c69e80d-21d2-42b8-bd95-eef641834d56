import { useSearchParams, useNavigate } from 'react-router-dom';

interface AnalyticsFilters {
  suite?: string;
  agent?: string;
}

// Utility to encode filter objects for shorter URLs
export const encodeFilters = (filters: Record<string, any>): string => {
  try {
    // Remove undefined/null values
    const cleanFilters = Object.fromEntries(
      Object.entries(filters).filter(([_, value]) => value != null && value !== '')
    );
    
    if (Object.keys(cleanFilters).length === 0) return '';
    
    // Use base64 encoding for shorter URLs
    return btoa(JSON.stringify(cleanFilters));
  } catch {
    return '';
  }
};

// Utility to decode filter objects from URLs
export const decodeFilters = (encoded: string): Record<string, any> => {
  if (!encoded) return {};
  
  try {
    return JSON.parse(atob(encoded));
  } catch {
    return {};
  }
};

// Utility to merge filters with current URL params
export const mergeFiltersWithUrl = (
  currentSearchParams: URLSearchParams,
  newFilters: Record<string, any>
): URLSearchParams => {
  const merged = new URLSearchParams(currentSearchParams);
  
  // Encode complex filters
  if (Object.keys(newFilters).length > 0) {
    const encoded = encodeFilters(newFilters);
    if (encoded) {
      merged.set('f', encoded);
    } else {
      merged.delete('f');
    }
  }
  
  return merged;
};

export const useAnalyticsParams = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const navigate = useNavigate();

  const filters: AnalyticsFilters = {
    suite: searchParams.get('suite') || undefined,
    agent: searchParams.get('agent') || undefined,
  };

  const updateFilters = (newFilters: Partial<AnalyticsFilters>) => {
    const current = new URLSearchParams(searchParams);
    
    Object.entries(newFilters).forEach(([key, value]) => {
      if (value) {
        current.set(key, value);
      } else {
        current.delete(key);
      }
    });

    setSearchParams(current);
  };

  const navigateWithFilters = (path: string) => {
    const queryString = searchParams.toString();
    navigate(`${path}${queryString ? `?${queryString}` : ''}`);
  };

  return {
    filters,
    updateFilters,
    navigateWithFilters,
  };
};

export const getUrlWithParams = (basePath: string, params: Record<string, string>) => {
  const url = new URL(basePath, window.location.origin);
  Object.entries(params).forEach(([key, value]) => {
    if (value) url.searchParams.set(key, value);
  });
  return url.pathname + url.search;
};