interface NetworkInfo {
  type: string;
  effectiveType: string;
  rtt: number;
  downlink: number;
}

declare global {
  interface Navigator {
    connection?: any;
    mozConnection?: any;
    webkitConnection?: any;
  }
}

export function getNetworkConnectionType(): NetworkInfo {
  const connection =
    navigator.connection ||
    navigator.mozConnection ||
    navigator.webkitConnection;

  if (!connection)
    return { type: 'unknown', effectiveType: '4g', rtt: 100, downlink: 10 };

  const { type, effectiveType, rtt, downlink } = connection;
  return { type, effectiveType, rtt, downlink };
}

export function isSlowNetwork(effectiveType: string) {
  return effectiveType === '2g' || effectiveType === '3g';
}