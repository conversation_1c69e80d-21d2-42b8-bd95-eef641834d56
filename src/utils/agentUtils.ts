import { scyra } from "../assets/images";
import { marketplaceAgents } from "../data/constants";
import { UserBasicInfoPayload } from "../types/user";

/**
 * Utility function to get the avatar for the currently active agent
 * @param activeAgent - The active agent key from TenantContext
 * @param userData - User data containing claimed agent suites
 * @returns The avatar URL for the active agent
 */
export const getAgentAvatar = (
  activeAgent: string | null,
  userData: UserBasicInfoPayload | undefined
): string => {
  if (!activeAgent) return scyra;

  // First, try to find the agent in user's claimed suites
  if (userData?.userInfo?.tenant?.claimedAgentSuites) {
    for (const claimedSuite of userData.userInfo.tenant.claimedAgentSuites) {
      const agent = claimedSuite.suite.availableAgents.find(
        (agent) => agent.agentKey.toLowerCase() === activeAgent.toLowerCase()
      );
      if (agent?.avatar) {
        return agent.avatar;
      }
    }
  }

  // Fallback to marketplace agents
  const marketplaceAgent = marketplaceAgents.find(
    (agent) => agent.id.toLowerCase() === activeAgent.toLowerCase()
  );
  if (marketplaceAgent?.image) {
    return marketplaceAgent.image;
  }

  // Final fallback to scyra
  return scyra;
};

/**
 * Utility function to get the name of the currently active agent
 * @param activeAgent - The active agent key from TenantContext
 * @param userData - User data containing claimed agent suites
 * @returns The display name for the active agent
 */
export const getAgentName = (
  activeAgent: string | null,
  userData: UserBasicInfoPayload | undefined
): string => {
  if (!activeAgent) return "Agent";

  // First, try to find the agent in user's claimed suites
  if (userData?.userInfo?.tenant?.claimedAgentSuites) {
    for (const claimedSuite of userData.userInfo.tenant.claimedAgentSuites) {
      const agent = claimedSuite.suite.availableAgents.find(
        (agent) => agent.agentKey.toLowerCase() === activeAgent.toLowerCase()
      );
      if (agent?.agentName) {
        return agent.agentName;
      }
    }
  }

  // Fallback to marketplace agents
  const marketplaceAgent = marketplaceAgents.find(
    (agent) => agent.id.toLowerCase() === activeAgent.toLowerCase()
  );
  if (marketplaceAgent?.name) {
    return marketplaceAgent.name;
  }

  // Final fallback to capitalized agent key
  return activeAgent.charAt(0).toUpperCase() + activeAgent.slice(1);
};

export const capitalizeAgentName = (key: string) => {
  if (!key) return "Agent";
  return key.charAt(0).toUpperCase() + key.slice(1);
};

export const generateSecureSessionId = (): string => {
  return `session_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
};