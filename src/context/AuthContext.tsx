import { useKeycloak } from '@react-keycloak/web';
import React, { createContext, useContext, useEffect, useState } from 'react';

import { isDevEnvironment } from '@/utils/helpers';

import { useGetUserProfile } from '../hooks/useUserProfile';
import {
  ClaimedAgentSuite,
  UserBasicInfoPayload,
  UserInfo,
} from '../types/user';
import { useTenant } from './TenantContext';

interface AuthContextType {
  isAuthenticated: boolean;
  isLoading: boolean;
  isError: boolean;
  isLoadingUserInitials: boolean;
  isOnline: boolean;
  user: UserInfo | null;
  claimedSuites: ClaimedAgentSuite[] | null;
  login: () => void;
  logout: () => void;
  getUserInitials: () => string;
}

const AuthContext = createContext<AuthContextType | null>(null);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: React.ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const { keycloak, initialized } = useKeycloak();
  const [isReady, setIsReady] = useState(false);
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const { setTenantId } = useTenant();
  const isDev = isDevEnvironment();
  // Network connectivity detection
  useEffect(() => {
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  const {
    data: userData,
    isLoading: userDataLoading,
    isError,
    refetch: refetchUser,
  } = useGetUserProfile<UserBasicInfoPayload>({
    onSuccess: (data: UserBasicInfoPayload) => {
      if (data && data.userInfo) {
        // Find the active tenant from the user's tenants
        if (data.userInfo.tenant?.tenantId) {
          setTenantId(data.userInfo.tenant.tenantId);
        }
      }
    },
    enabled: initialized && keycloak.authenticated && isOnline,
    retry: isOnline ? 3 : 0,
    refetchOnReconnect: true,
    networkMode: isOnline ? 'online' : 'offlineFirst',
  });

  useEffect(() => {
    if (initialized) {
      setIsReady(true);
      if (keycloak.authenticated && !userData && !userDataLoading) {
        refetchUser();
      }
    }
  }, [
    initialized,
    keycloak.authenticated,
    userData,
    userDataLoading,
    refetchUser,
  ]);

  // Set tenant ID when user data is loaded
  useEffect(() => {
    if (userData?.userInfo?.tenant?.tenantId) {
      setTenantId(userData.userInfo.tenant.tenantId);
    }
  }, [userData?.userInfo?.tenant?.tenantId, setTenantId]);

  const login = () => {
    keycloak.login({
      redirectUri: `${window.location.origin}/dashboard`,
    });
  };

  const logout = () => {
    // Clear all tenant-related localStorage data
    try {
      localStorage.removeItem('agentous_tenant_id');
      localStorage.removeItem('agentous_active_agent');
    } catch (error) {
      console.error('Error clearing localStorage on logout:', error);
    }

    keycloak.logout({
      redirectUri: `${window.location.origin}/`,
    });
  };

  const getUserInitials = (): string => {
    if (!userData?.userInfo) return 'U';

    const { firstName, lastName } = userData.userInfo;

    if (firstName && lastName) {
      return `${firstName.charAt(0).toUpperCase()}${lastName.charAt(0).toUpperCase()}`;
    }

    if (firstName) {
      return firstName.charAt(0).toUpperCase();
    }

    if (lastName) {
      return lastName.charAt(0).toUpperCase();
    }

    if (userData.userInfo.email) {
      return userData.userInfo.email.charAt(0).toUpperCase();
    }

    return 'U';
  };

  const value: AuthContextType = {
    isAuthenticated: isDev
      ? !!userData
      : initialized &&
        (keycloak.authenticated ?? false) &&
        !!userData &&
        isOnline,
    isLoading: !isReady || userDataLoading,
    isLoadingUserInitials: userDataLoading,
    isError: isError || !isOnline,
    isOnline,
    user: userData?.userInfo || null,
    claimedSuites: userData?.userInfo?.tenant?.claimedAgentSuites || null,
    login,
    logout,
    getUserInitials,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};
