import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import {
  useAIAgentsApi,
  useAIAgentSuiteApi,
  useAIAgentsCategoriesApi,
  useClaimAgentSuiteApi,
} from "../services/upivotalAgenticService";
import {
  AgentCategoriesResponse,
  AgentCategory,
  AIAgent,
  AIAgentsResponse,
  AIAgentsSuiteResponse,
  AIAgentSuite,
} from "@/types/agents";
import {
  GET_AI_AGENTS_QUERY,
  GET_AI_AGENT_SUITES_QUERY,
  GET_AI_AGENT_CATEGORIES_QUERY,
  CLAIM_AGENT_SUITE_QUERY,
} from "@/utils/queryKeys";

// Hook for fetching AI Agents with React Query
export const useGetAIAgents = (options = {}) => {
  const getAIAgents = useAIAgentsApi();

  return useQuery<AIAgentsResponse, Error, AIAgent[]>(
    [GET_AI_AGENTS_QUERY],
    () => getAIAgents(),
    {
      select: (data) => {
        // Extract the agents array from the response
        return data?.status && data?.data?.aiAgents ? data.data.aiAgents : [];
      },
      staleTime: 5 * 60 * 1000, // 5 minutes - agents don't change frequently
      cacheTime: 10 * 60 * 1000, // 10 minutes
      ...options,
    }
  );
};

// Hook for fetching AI Agent Suites with React Query
export const useGetAIAgentSuites = (options = {}) => {
  const getAIAgentSuites = useAIAgentSuiteApi();

  return useQuery<AIAgentsSuiteResponse, Error, AIAgentSuite[]>(
    [GET_AI_AGENT_SUITES_QUERY],
    () => getAIAgentSuites(),
    {
      select: (data) => {
        // Extract the agent suites array from the response
        return data?.status && data?.data?.aiAgentSuites
          ? data.data.aiAgentSuites
          : [];
      },
      staleTime: 5 * 60 * 1000, // 5 minutes - suites don't change frequently
      cacheTime: 10 * 60 * 1000, // 10 minutes
      ...options,
    }
  );
};

// Hook for fetching AI Agent Categories with React Query
export const useGetAIAgentCategories = (options = {}) => {
  const getAIAgentCategories = useAIAgentsCategoriesApi();

  return useQuery<AgentCategoriesResponse, Error, AgentCategory[]>(
    [GET_AI_AGENT_CATEGORIES_QUERY],
    () => getAIAgentCategories(),
    {
      select: (data) => {
        // Extract the categories array from the response
        return data?.status && data?.data ? data.data : [];
      },
      staleTime: 10 * 60 * 1000, // 10 minutes - categories rarely change
      cacheTime: 15 * 60 * 1000, // 15 minutes
      ...options,
    }
  );
};

// Hook for claiming agents suite with React Query mutation
export const useClaimAgentSuite = (options = {}) => {
  const queryClient = useQueryClient();
  const claimAgentSuite = useClaimAgentSuiteApi();

  return useMutation({
    mutationKey: [CLAIM_AGENT_SUITE_QUERY],
    mutationFn: (agentSuiteKey: string) => claimAgentSuite(agentSuiteKey),
    onSuccess: () => {
      // Invalidate relevant queries after successful claiming
      queryClient.invalidateQueries([GET_AI_AGENT_SUITES_QUERY]);
      queryClient.invalidateQueries([GET_AI_AGENTS_QUERY]);
    },
    ...options,
  });
};

// Convenience hook that fetches both agents and suites
export const useGetAIAgentsData = (options = {}) => {
  const agentsQuery = useGetAIAgents(options);
  const suitesQuery = useGetAIAgentSuites(options);

  return {
    agents: agentsQuery.data || [],
    agentSuites: suitesQuery.data || [],
    isLoadingAgents: agentsQuery.isLoading,
    isLoadingSuites: suitesQuery.isLoading,
    isLoading: agentsQuery.isLoading || suitesQuery.isLoading,
    error: agentsQuery.error || suitesQuery.error,
    refetchAgents: agentsQuery.refetch,
    refetchSuites: suitesQuery.refetch,
    refetchAll: () => {
      agentsQuery.refetch();
      suitesQuery.refetch();
    },
  };
};
