import { useCallback, useEffect, useState } from 'react';

import { useUpdateAvatarMutation } from './useUserProfile';

export const useAvatarUpload = () => {
  const [isUploading, setIsUploading] = useState(false);
  const [uploadError, setUploadError] = useState<string | null>(null);
  const updateAvatarMutation = useUpdateAvatarMutation();

  // Auto-clear error after 3 seconds
  useEffect(() => {
    if (uploadError) {
      const timer = setTimeout(() => {
        setUploadError(null);
      }, 3000);

      return () => clearTimeout(timer);
    }
  }, [uploadError]);

  const convertFileToBase64 = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => {
        if (typeof reader.result === 'string') {
          // Remove data:image/jpeg;base64, prefix to get just the base64 string
          const base64 = reader.result.split(',')[1];
          resolve(base64);
        } else {
          reject(new Error('Failed to convert file to base64'));
        }
      };
      reader.onerror = () => reject(new Error('Failed to read file'));
    });
  };

  const validateFile = (file: File): string | null => {
    const maxSizeInBytes = 5 * 1024 * 1024; // 5MB
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];

    if (!allowedTypes.includes(file.type)) {
      return 'Please select a valid image file (JPEG, PNG, or GIF)';
    }

    if (file.size > maxSizeInBytes) {
      return 'File size must be less than 5MB';
    }

    return null;
  };

  const uploadAvatar = useCallback(
    async (file: File): Promise<boolean> => {
      setIsUploading(true);
      setUploadError(null);

      try {
        // Validate file
        const validationError = validateFile(file);
        if (validationError) {
          setUploadError(validationError);
          return false;
        }

        // Convert to base64
        const base64Avatar = await convertFileToBase64(file);

        // Upload avatar
        const response = await updateAvatarMutation.mutateAsync({
          avatar: base64Avatar,
        });

        if (!response.status) {
          throw new Error(response.message || 'Avatar upload failed');
        }

        return true;
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : 'Failed to upload avatar';
        setUploadError(errorMessage);
        return false;
      } finally {
        setIsUploading(false);
      }
    },
    [updateAvatarMutation]
  );

  const handleFileSelect = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      const file = event.target.files?.[0];
      if (file) {
        return uploadAvatar(file);
      }
      return Promise.resolve(false);
    },
    [uploadAvatar]
  );

  return {
    isUploading,
    uploadError,
    uploadAvatar,
    handleFileSelect,
    validateFile,
  };
};
