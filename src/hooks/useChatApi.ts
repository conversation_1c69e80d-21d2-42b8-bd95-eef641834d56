import { useCallback } from 'react';

import { useTenant } from '@/context/TenantContext';

import { useScyraChatApi } from '../services/scyraChatService';
import { useConversationState } from './useConversationState';
import { useScyraChatHistory } from './useScyraChat';

export const useChatApi = () => {
  const chatWithScyra = useScyraChatApi();
  const {
    state,
    addMessage,
    setLoading,
    startSignup,
    extractOnboardingDataFromMessage,
    extractUserDataFromUserMessage,
  } = useConversationState();
  const { loadChatHistory } = useScyraChatHistory();
  /**
   * Loads chat history and ensures history messages are added before the welcome message.
   * Returns a grouped object: { [date: string]: ChatMessage[] }
   */
  const loadAndGroupChatHistory = useCallback(async () => {
    await loadChatHistory();
    // After loading, group messages by date (ascending)
    const grouped: Record<string, typeof state.messages> = {};
    const sorted = [...state.messages].sort(
      (a, b) => a.timestamp.getTime() - b.timestamp.getTime()
    );
    sorted.forEach(msg => {
      const dateKey = msg.timestamp.toISOString().split('T')[0];
      if (!grouped[dateKey]) grouped[dateKey] = [];
      grouped[dateKey].push(msg);
    });
    return grouped;
  }, [loadChatHistory, state.messages]);

  const { isLoading: isActiveTenantLoading } = useTenant();

  const sendMessage = useCallback(
    async (userMessage: string) => {
      if (isActiveTenantLoading) {
        console.warn(
          'Attempted to send message while tenant/agent initializing'
        );
        addMessage(
          'scyra',
          'Agent is initializing. Please wait a moment.',
          'Scyra'
        );
        return;
      }
      try {
        // Add user message immediately
        addMessage('user', userMessage, 'You');

        // Extract user data ONLY for verification code and password steps
        // All other data (firstName, lastName, email) comes from Scyra's metadata
        if (state.onboardingProgress.isSignupStarted) {
          const currentStep = state.onboardingProgress.currentStep;
          if (currentStep === 'verification' || currentStep === 'password') {
            extractUserDataFromUserMessage(userMessage, currentStep);
          }
        }

        // Set loading state
        setLoading(true);

        // Send message to API
        const response = await chatWithScyra({
          userMessage,
          sessionId: state.sessionId,
        });

        // Extract onboarding data from Scyra's full response (including metadata)
        extractOnboardingDataFromMessage(response);

        // Add Scyra response (display text only, without metadata)
        const displayMessage = response.split('metaData')[0].trim();
        addMessage('scyra', displayMessage, 'Scyra');
      } catch (error) {
        console.error('Chat error:', error);
        addMessage(
          'scyra',
          'I apologize, but I encountered an error. Please try again.',
          'Scyra'
        );
      } finally {
        setLoading(false);
      }
    },
    [
      addMessage,
      setLoading,
      chatWithScyra,
      isActiveTenantLoading,
      state.onboardingProgress.isSignupStarted,
      state.onboardingProgress.currentStep,
      extractOnboardingDataFromMessage,
      extractUserDataFromUserMessage,
    ]
  );

  const handleSignupStart = useCallback(async () => {
    startSignup();
    await sendMessage('I would like to signup.');
  }, [startSignup, sendMessage]);

  return {
    state,
    sendMessage,
    handleSignupStart,
    loadAndGroupChatHistory,
  };
};
