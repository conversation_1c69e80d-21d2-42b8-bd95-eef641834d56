import { useState, useCallback } from "react";
import {
  ConnectionFlowState,
  ConnectionFlowContext,
  AvailableApp,
  ScyraMessage,
} from "../types/businessStack";
import { useUnifiedConnectionApi } from "../services/businessStackService";
import { useTenant } from "@/context/TenantContext";

const initialState: ConnectionFlowState = {
  step: "idle",
  isLoading: false,
  collectedData: {},
  currentFieldIndex: 0,
  fieldsToCollect: [],
  localMessages: [],
};

const capitalizeAgentName = (key: string) => {
  if (!key) return "Agent";
  return key.charAt(0).toUpperCase() + key.slice(1);
};

export const useConnectionFlow = (
  onConnectionStateChange?: (app: AvailableApp, isConnected: boolean) => void
): ConnectionFlowContext => {
  const [state, setState] = useState<ConnectionFlowState>(initialState);
  const { getConnectionUrl, saveConnectionCredentials, disconnectApp } =
    useUnifiedConnectionApi();

  const { activeAgent } = useTenant();

  const resetFlow = useCallback((preserveMessages = false) => {
    setState((prev) => {
      // Clear any existing tab monitor
      if (prev.tabMonitor) {
        clearInterval(prev.tabMonitor);
      }

      return {
        ...initialState,
        localMessages: preserveMessages ? prev.localMessages : [],
      };
    });
  }, []);

  const addLocalMessage = useCallback(
    (content: string, sender: string = activeAgent || "") => {
      setState((prev) => {
        // Check if the same message was already added recently (within 1 second)
        const recentMessages = prev.localMessages.filter(
          (msg) => Date.now() - msg.timestamp.getTime() < 1000
        );
        const isDuplicate = recentMessages.some(
          (msg) => msg.content === content && msg.sender === sender
        );

        if (isDuplicate) {
          return prev; // Don't add duplicate message
        }

        // Generate a more unique ID using timestamp, random number, and counter
        const uniqueId = `local-${Date.now()}-${Math.random().toString(36).substring(2, 11)}-${performance.now()}`;

        const message: ScyraMessage = {
          id: uniqueId,
          sender,
          content,
          timestamp: new Date(),
          senderName:
            sender === "user" ? "You" : capitalizeAgentName(activeAgent || ""),
        };

        return {
          ...prev,
          localMessages: [...prev.localMessages, message],
        };
      });
    },
    [activeAgent]
  );

  const hashPasswordForDisplay = useCallback((password: string): string => {
    return "•".repeat(password.length);
  }, []);

  const handleOAuth2Flow = useCallback(
    async (app: AvailableApp, collectedData?: Record<string, string>) => {
      try {
        setState((prev) => ({
          ...prev,
          isLoading: true,
          step: "authenticating",
        }));

        // Use provided collectedData or fall back to state.collectedData
        const dataToUse = collectedData || state.collectedData;

        // Step 1: Display connection message (only if not coming from pre-auth)
        if (!collectedData) {
          const connectionMessage =
            `${app.uiHints.longDescription || ""}`.trim();
          addLocalMessage(connectionMessage);
        }

        // Step 2: Handle pre-authentication if needed (only for initial flow)
        if (!collectedData && app.preAuth && app.preAuth.fields.length > 0) {
          const sortedFields = app.preAuth!.fields.sort(
            (a, b) => (a.order || 0) - (b.order || 0)
          );

          setState((prev) => ({
            ...prev,
            step: "collecting-preauth",
            fieldsToCollect: sortedFields,
            currentFieldIndex: 0,
            isLoading: false,
          }));

          // Ask for the first field
          if (sortedFields.length > 0) {
            const firstField = sortedFields[0];
            const fieldMessage =
              firstField.description || `Please enter your ${firstField.label}`;
            addLocalMessage(fieldMessage);
          }

          return;
        }

        // Step 3: Get connection URL
        const urlResponse = await getConnectionUrl({
          appKey: app.key,
          params: dataToUse,
        });

        if (!urlResponse.status) {
          throw new Error(urlResponse.message);
        }

        // Validate the connection URL
        let connectionUrl = urlResponse.data?.data;
        if (!connectionUrl || !connectionUrl.startsWith("http")) {
          throw new Error("Invalid connection URL received from server");
        }

        // Step 4: Modify redirect_uri to include current page URL and app key as path parameter
        let currentUrl = window.location.href.split("?")[0]; // Remove existing query params
        currentUrl = currentUrl.split("/business-stack")[0];

        // Construct redirect URI with appKey as path parameter
        const redirectUri = `${currentUrl}/business-stack/${app.key}`;

        // Parse the connection URL and update redirect_uri parameter
        // Note: URLSearchParams.set() automatically handles URL encoding, so no manual encoding needed
        const urlObj = new URL(connectionUrl);
        urlObj.searchParams.set("redirect_uri", redirectUri);
        connectionUrl = urlObj.toString();

        setState((prev) => ({
          ...prev,
          connectionUrl: connectionUrl,
          step: "waiting-auth",
          isLoading: true,
        }));

        // Step 5: Open authentication in new tab
        const authWindow = window.open(connectionUrl, "_blank");

        if (!authWindow) {
          addLocalMessage(
            "Unable to open authentication tab. Please check your browser settings and try again."
          );
          setState((prev) => ({
            ...prev,
            step: "error",
            isLoading: false,
            error: "Failed to open authentication tab",
          }));
          return;
        }

        addLocalMessage(
          "Please complete the authentication in the new tab that just opened."
        );

        // Monitor tab for closure
        const tabMonitor = setInterval(() => {
          try {
            // Check if window is closed
            if (authWindow.closed) {
              clearInterval(tabMonitor);

              // Use setTimeout to ensure state is checked after any pending updates
              setTimeout(() => {
                setState((prev) => {
                  // Only show cancellation if we're still waiting for auth
                  if (prev.step === "waiting-auth") {
                    addLocalMessage("Authentication was cancelled.");
                    return {
                      ...prev,
                      step: "error",
                      isLoading: false,
                      error: "Authentication cancelled",
                      tabMonitor: undefined,
                    };
                  }
                  return {
                    ...prev,
                    tabMonitor: undefined,
                  };
                });
              }, 100);
            }
          } catch (error) {
            // In case of any errors accessing the window, assume it's closed
            console.log(
              "Error checking OAuth tab status, assuming closed:",
              error
            );
            clearInterval(tabMonitor);
            setTimeout(() => {
              setState((prev) => {
                if (prev.step === "waiting-auth") {
                  addLocalMessage("Authentication was cancelled.");
                  return {
                    ...prev,
                    step: "error",
                    isLoading: false,
                    error: "Authentication cancelled",
                    tabMonitor: undefined,
                  };
                }
                return {
                  ...prev,
                  tabMonitor: undefined,
                };
              });
            }, 100);
          }
        }, 500);

        // Store the monitor reference in state
        setState((prev) => ({
          ...prev,
          tabMonitor,
        }));
      } catch (error) {
        console.error("OAuth2 flow error:", error);
        const errorMessage =
          error instanceof Error
            ? error.message
            : "An unexpected error occurred.";

        setState((prev) => ({
          ...prev,
          step: "error",
          isLoading: false,
          error: errorMessage,
        }));

        addLocalMessage(`Connection failed: ${errorMessage}`);
      }
    },
    [state.collectedData, getConnectionUrl, addLocalMessage]
  );

  const handleBasicAuthFlow = useCallback(
    async (app: AvailableApp) => {
      try {
        setState((prev) => ({
          ...prev,
          isLoading: false,
          step: "collecting-form",
        }));

        // Display connection message
        const connectionMessage =
          app.uiHints.longDescription ||
          `Allow Agentous PivoTL to access your ${app.name} account?`;
        addLocalMessage(connectionMessage);

        if (app.form && app.form.fields.length > 0) {
          const sortedFields = app.form!.fields.sort(
            (a, b) => (a.order || 0) - (b.order || 0)
          );

          setState((prev) => ({
            ...prev,
            fieldsToCollect: sortedFields,
            currentFieldIndex: 0,
          }));

          // Ask for the first field
          if (sortedFields.length > 0) {
            const firstField = sortedFields[0];
            const fieldMessage =
              firstField.description || `Please enter your ${firstField.label}`;
            addLocalMessage(fieldMessage);
          }

          return;
        }

        // If no form fields, proceed directly to save credentials
        await handleSaveCredentials(app, state.collectedData);
      } catch (error) {
        console.error("Basic auth flow error:", error);
        setState((prev) => ({
          ...prev,
          step: "error",
          isLoading: false,
          error:
            error instanceof Error
              ? error.message
              : "An unexpected error occurred.",
        }));
        addLocalMessage("Connection failed. Please try again.");
      }
    },
    [state.collectedData, addLocalMessage]
  );

  const handleSaveCredentials = useCallback(
    async (app: AvailableApp, credentials: Record<string, string>) => {
      try {
        setState((prev) => ({
          ...prev,
          isLoading: true,
          step: "saving-credentials",
        }));

        // Prepare connection data based on app's connectionRequestFields
        const connectionData: Record<string, string> = {};

        // For OAuth2 apps, use credentials directly (code, state)
        if (app.authType === "OAUTH2") {
          // For OAuth2, pass the credentials directly
          Object.assign(connectionData, credentials);
        } else {
          // For other auth types, map through connectionRequestFields
          app.connectionRequestFields?.forEach((field) => {
            if (credentials[field.key]) {
              connectionData[field.key] = credentials[field.key];
            }
          });
        }

        const response = await saveConnectionCredentials({
          appKey: app.key,
          connectionData,
        });

        if (response.status) {
          setState((prev) => ({ ...prev, step: "success", isLoading: false }));
          addLocalMessage(`${app.name} app successfully connected!`);

          // Update app connection status
          if (onConnectionStateChange) {
            onConnectionStateChange(app, true);
          }

          // Reset flow after success but preserve messages for user to see
          setTimeout(() => {
            resetFlow(true);
          }, 2000);
        } else {
          throw new Error(response.message);
        }
      } catch (error) {
        console.error("Save credentials error:", error);
        setState((prev) => ({
          ...prev,
          step: "error",
          isLoading: false,
          error:
            error instanceof Error
              ? error.message
              : "Failed to save connection credentials.",
        }));
        addLocalMessage("Failed to save connection. Please try again.");
      }
    },
    [
      saveConnectionCredentials,
      onConnectionStateChange,
      addLocalMessage,
      resetFlow,
    ]
  );

  // Handle OAuth callback with full app object (for OAuth2 flows)
  const handleOAuthCallbackWithApp = useCallback(
    async (app: AvailableApp, code: string, oauthState?: string) => {
      try {
        // Clear tab monitor if it exists (OAuth completed successfully)
        if (state.tabMonitor) {
          clearInterval(state.tabMonitor);
        }

        setState((prev) => ({
          ...prev,
          step: "saving-credentials",
          isLoading: true,
          tabMonitor: undefined,
          currentApp: app, // Set the current app
        }));

        // For OAuth2 apps, the credentials are the code and state
        const oauthCredentials = { code, state: oauthState || "" };

        // Save credentials using the OAuth code and state
        await handleSaveCredentials(app, oauthCredentials);
      } catch (error) {
        console.error("OAuth callback error:", error);
        setState((prev) => ({
          ...prev,
          step: "error",
          isLoading: false,
          error:
            error instanceof Error
              ? error.message
              : "OAuth callback processing failed",
          tabMonitor: undefined,
        }));
        addLocalMessage("OAuth authentication failed. Please try again.");
      }
    },
    [state.tabMonitor, handleSaveCredentials, addLocalMessage]
  );

  const startConnection = useCallback(
    (app: AvailableApp) => {
      setState((prev) => ({
        ...prev,
        currentApp: app,
        collectedData: {},
        currentFieldIndex: 0,
        fieldsToCollect: [],
      }));

      if (app.authType === "OAUTH2") {
        handleOAuth2Flow(app);
      } else if (app.authType === "BASIC") {
        handleBasicAuthFlow(app);
      } else {
        setState((prev) => ({
          ...prev,
          step: "error",
          error: `Unsupported authentication type: ${app.authType}`,
        }));
        addLocalMessage(`Unsupported authentication type for ${app.name}.`);
      }
    },
    [handleOAuth2Flow, handleBasicAuthFlow, addLocalMessage]
  );

  const handleUserInput = useCallback(
    (input: string) => {
      if (!state.currentApp || state.fieldsToCollect.length === 0) return;

      const currentField = state.fieldsToCollect[state.currentFieldIndex];
      if (!currentField) return;

      // Add user message to local messages
      addLocalMessage(
        currentField.type === "PASSWORD"
          ? hashPasswordForDisplay(input)
          : input,
        "user"
      );

      const updatedData = {
        ...state.collectedData,
        [currentField.key]: input,
      };

      setState((prev) => ({
        ...prev,
        collectedData: updatedData,
        currentFieldIndex: prev.currentFieldIndex + 1,
      }));

      // Check if we've collected all fields
      if (state.currentFieldIndex + 1 >= state.fieldsToCollect.length) {
        if (state.step === "collecting-preauth") {
          // Update state and proceed with OAuth2 flow
          setState((prev) => ({
            ...prev,
            collectedData: updatedData,
            step: "authenticating",
            isLoading: true,
          }));

          // Call OAuth2 flow directly with the current app and updated data
          if (state.currentApp) {
            handleOAuth2Flow(state.currentApp, updatedData);
          }
        } else if (state.step === "collecting-form") {
          // Save credentials for Basic auth
          handleSaveCredentials(state.currentApp, updatedData);
        }
      } else {
        // Ask for the next field
        const nextField = state.fieldsToCollect[state.currentFieldIndex + 1];
        if (nextField) {
          const nextFieldMessage =
            nextField.description || `Please enter your ${nextField.label}`;
          addLocalMessage(nextFieldMessage);
        }
      }
    },
    [
      state,
      hashPasswordForDisplay,
      handleOAuth2Flow,
      handleSaveCredentials,
      addLocalMessage,
    ]
  );

  const handleDisconnect = useCallback(
    async (app: AvailableApp) => {
      try {
        setState((prev) => ({ ...prev, isLoading: true }));

        const response = await disconnectApp({ appKey: app.key });

        if (response.status) {
          addLocalMessage(`${app.name} app successfully disconnected.`);

          if (onConnectionStateChange) {
            onConnectionStateChange(app, false);
          }
        } else {
          throw new Error(response.message);
        }
      } catch (error) {
        console.error("Disconnect error:", error);
        addLocalMessage(`Failed to disconnect ${app.name}. Please try again.`);
      } finally {
        setState((prev) => ({ ...prev, isLoading: false }));
      }
    },
    [disconnectApp, onConnectionStateChange, addLocalMessage]
  );

  const handleReconnect = useCallback(
    (app: AvailableApp) => {
      resetFlow();
      startConnection(app);
    },
    [resetFlow, startConnection]
  );

  // Handle OAuth callback from URL parameters
  const handleOAuthCallback = useCallback(
    async (appKey: string, code: string, oauthState?: string) => {
      try {
        // Find the app by key - allow processing even if no current app is set
        const app = state.currentApp;
        if (!app) {
          console.log(
            "No current app in connection flow state, but proceeding with OAuth callback"
          );
        } else if (app.key !== appKey) {
          console.log(`App key mismatch: expected ${appKey}, got ${app.key}`);
        }

        // Clear tab monitor if it exists (OAuth completed successfully)
        if (state.tabMonitor) {
          clearInterval(state.tabMonitor);
        }

        setState((prev) => ({
          ...prev,
          step: "saving-credentials",
          isLoading: true,
          tabMonitor: undefined,
        }));

        // If no current app, we need to handle this differently
        if (!app) {
          console.log(
            "No current app available, attempting to save credentials with appKey"
          );
          // Create a minimal app object for credential saving
          const minimalApp = { key: appKey } as AvailableApp;
          await handleSaveCredentials(minimalApp, {
            code,
            state: oauthState || "",
          });
        } else {
          // Save credentials using the OAuth code and state
          await handleSaveCredentials(app, { code, state: oauthState || "" });
        }
      } catch (error) {
        console.error("OAuth callback error:", error);
        setState((prev) => ({
          ...prev,
          step: "error",
          isLoading: false,
          error:
            error instanceof Error
              ? error.message
              : "OAuth callback processing failed",
          tabMonitor: undefined,
        }));
        addLocalMessage("Authentication failed. Please try again.");
      }
    },
    [state.currentApp, state.tabMonitor, handleSaveCredentials, addLocalMessage]
  );

  return {
    state,
    startConnection,
    handleUserInput,
    handleDisconnect,
    handleReconnect,
    resetFlow,
    handleOAuthCallback,
    handleOAuthCallbackWithApp,
    handleSaveCredentials,
  };
};
