import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import dashboardService from '@/services/dashboardService';
import { CreateDailyInsightRequest, DailyInsightFilter } from '@/types/dashboard';
import { createAuthenticatedAxiosInstance } from '@/helpers/axiosConfig';

export const DASHBOARD_QUERY_KEYS = {
  all: ['daily-insights'] as const,
  lists: () => [...DASHBOARD_QUERY_KEYS.all, 'list'] as const,
  list: (filter: DailyInsightFilter) =>
    [...DASHBOARD_QUERY_KEYS.lists(), filter] as const,
};

export const useDailyInsightsList = (
  filter: DailyInsightFilter,
  enabled = true
) => {
  const authToken = createAuthenticatedAxiosInstance();
  return useQuery({
    queryKey: DASHBOARD_QUERY_KEYS.list(filter),
    queryFn: () => dashboardService.getDailyInsights(authToken, filter),
    enabled: enabled && !!filter?.tenantId && !!authToken,
    select: (data) => data.data,
    retry: 2,
    refetchOnWindowFocus: true,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });
};

export const useCreateDailyInsightMutation = () => {
  const queryClient = useQueryClient();
  const authToken = createAuthenticatedAxiosInstance();

  return useMutation({
    mutationFn: (payload: CreateDailyInsightRequest) =>
      dashboardService.createDailyInsight(authToken, payload),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: DASHBOARD_QUERY_KEYS.lists(),
      });
    },
    onError: (error) => {
      console.error('Failed to create daily insight:', error);
    },
  });
};
