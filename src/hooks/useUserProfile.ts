import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import userProfileService from '@/services/userProfileService';
import { usePrivateRequest } from '@/lib/axios/usePrivateRequest';
import { BASE_URL } from '@/utils/apiUrls';
import {
  UpdateUserInfoRequest,
  UpdateAvatarRequest,
  ChangePasswordRequest,
  UserBasicInfoPayload,
} from '@/types/user';
import { useKeycloak } from '@react-keycloak/web';
import { useGetUserApi } from '@/services/upivotalAgenticService';
import { GET_USER_QUERY } from '@/utils/queryKeys';
import { isDevEnvironment } from '@/utils/helpers';

// Query keys for React Query cache management
export const USER_PROFILE_QUERY_KEYS = {
  all: ['user-profile'] as const,
  userFunctions: () => [...USER_PROFILE_QUERY_KEYS.all, 'user-functions'] as const,
  userProfile: () => [...USER_PROFILE_QUERY_KEYS.all, 'user-profile-data'] as const,
};

/**
 * Hook to fetch the user's profile information
 */
export const useGetUserProfile = <T extends UserBasicInfoPayload>(
  options = {}
) => {
  const { initialized, keycloak } = useKeycloak();
  const getUser = useGetUserApi();
  const isDev = isDevEnvironment();

  return useQuery({
    queryKey: [GET_USER_QUERY],
    queryFn: () => getUser<T>(),
    ...options,
    select: (data) => {
      const {
        userInfo: { dateOfBirth, ...userRest },
        ...others
      } = data;

      const newData = {
        ...others,
        userInfo: {
          ...userRest,
          dateOfBirth: dateOfBirth && new Date(dateOfBirth),
        },
      };
      return newData;
    },
    enabled: isDev ? true : !!initialized && !!keycloak.authenticated,
  });
};

/**
 * Hook to fetch user functions/roles from API
 */
export const useGetUserFunctions = () => {
  const axiosInstance = usePrivateRequest(BASE_URL);

  return useQuery({
    queryKey: USER_PROFILE_QUERY_KEYS.userFunctions(),
    queryFn: async () => {
      if (!axiosInstance.current) {
        throw new Error('Axios instance not ready');
      }
      const response = await userProfileService.getUserFunctions(axiosInstance.current);
      return response;
    },
    enabled: !!axiosInstance.current,
    staleTime: 5 * 60 * 1000, // 5 minutes
    cacheTime: 10 * 60 * 1000, // 10 minutes
    retry: 2,
    refetchOnWindowFocus: true,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });
};

/**
 * Hook to update user information
 */
export const useUpdateUserInfoMutation = () => {
  const queryClient = useQueryClient();
  const axiosInstance = usePrivateRequest(BASE_URL);

  return useMutation({
    mutationFn: async (payload: UpdateUserInfoRequest) => {
      if (!axiosInstance.current) {
        throw new Error('Axios instance not ready');
      }
      return userProfileService.updateUserInfo(axiosInstance.current, payload);
    },
    onSuccess: () => {
      // Invalidate user data to trigger refetch
      queryClient.invalidateQueries({ queryKey: ['auth'] });
      queryClient.invalidateQueries({ queryKey: USER_PROFILE_QUERY_KEYS.all });
    },
    onError: (error) => {
      console.error('Failed to update user info:', error);
    },
  });
};

/**
 * Hook to update user avatar
 */
export const useUpdateAvatarMutation = () => {
  const queryClient = useQueryClient();
  const axiosInstance = usePrivateRequest(BASE_URL);

  return useMutation({
    mutationFn: async (payload: UpdateAvatarRequest) => {
      if (!axiosInstance.current) {
        throw new Error('Axios instance not ready');
      }
      return userProfileService.updateAvatar(axiosInstance.current, payload);
    },
    onSuccess: () => {
      // Invalidate user data to trigger refetch
      queryClient.invalidateQueries({ queryKey: ['auth'] });
      queryClient.invalidateQueries({ queryKey: USER_PROFILE_QUERY_KEYS.all });
    },
    onError: (error) => {
      console.error('Failed to update avatar:', error);
    },
  });
};

/**
 * Hook to change user password
 */
export const useChangePasswordMutation = () => {
  const axiosInstance = usePrivateRequest(BASE_URL);

  return useMutation({
    mutationFn: async (payload: ChangePasswordRequest) => {
      if (!axiosInstance.current) {
        throw new Error('Axios instance not ready');
      }
      return userProfileService.changePassword(axiosInstance.current, payload);
    },
    onError: (error) => {
      console.error('Failed to change password:', error);
    },
  });
};

/**
 * Hook to change user email
 */
export const useChangeEmailMutation = () => {
  const axiosInstance = usePrivateRequest(BASE_URL);

  return useMutation({
    mutationFn: async (payload: { email: string }) => {
      if (!axiosInstance.current) {
        throw new Error('Axios instance not ready');
      }
      return userProfileService.changeEmail(axiosInstance.current, payload);
    },
    onError: (error) => {
      console.error('Failed to change email:', error);
    },
  });
};

// Legacy exports for backward compatibility
export const useUpdateUserInfo = () => {
  const mutation = useUpdateUserInfoMutation();
  return mutation.mutateAsync;
};

export const useUpdateAvatar = () => {
  const mutation = useUpdateAvatarMutation();
  return mutation.mutateAsync;
};